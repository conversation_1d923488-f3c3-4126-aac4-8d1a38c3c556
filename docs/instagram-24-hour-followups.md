# Instagram 24-Hour Follow-Up System

## Overview

Instagram's messaging policy allows businesses to send messages to users within 24 hours of the user's last message. After this window expires, businesses need to use message templates or external plugins to continue communication.

This system implements automatic follow-up sending within the 24-hour window and marks follow-ups outside this window for external plugin handling.

## Key Components

### 1. 24-Hour Window Utilities (`packages/instagram-bot/src/24-hour-window.ts`)

Core utilities for managing the 24-hour window:

- `isWithin24HourWindow()` - Check if a scheduled time is within the window
- `getLatest24HourTime()` - Calculate the latest time for sending within the window
- `getTimeRemainingInWindow()` - Get remaining time in the current window
- `validateFollowUpTiming()` - Validate follow-up scheduling
- `filterFollowUpsWithin24Hours()` - Filter follow-ups by window status

### 2. Message Tracking

The system now properly tracks `lastInteractionAt` for each contact:

- Updated when user messages are received (webhook processing)
- Updated when manual messages are sent
- Updated when follow-ups are successfully sent

### 3. Follow-Up Processing (`packages/instagram-bot/src/follow-up-scheduler.ts`)

Enhanced `processFollowUps()` function:

- Checks 24-hour window before sending each follow-up
- Only sends follow-ups within the window
- Marks expired follow-ups as "failed" (for external plugin handling)
- Updates `lastInteractionAt` when follow-ups are sent

### 4. Follow-Up Management API (`apps/dashboard/app/api/instagram/follow-ups/route.ts`)

Enhanced API endpoints:

- **GET**: Returns follow-ups with 24-hour window information
- **PATCH**: Validates 24-hour window when updating follow-ups
- **DELETE**: Standard deletion (no window validation needed)

### 5. 24-Hour Window Info API (`apps/dashboard/app/api/instagram/contacts/[contactId]/24-hour-window/route.ts`)

New endpoint providing detailed window information:

```json
{
  "success": true,
  "data": {
    "contactId": "uuid",
    "contactUsername": "username",
    "lastInteractionAt": "2024-01-01T12:00:00.000Z",
    "latest24HourTime": "2024-01-02T12:00:00.000Z",
    "isCurrentlyWithinWindow": true,
    "timeRemainingMs": 64800000,
    "timeRemainingHours": 18,
    "timeRemainingMinutes": 0
  }
}
```

## How It Works

### 1. User Message Received

1. Webhook processes incoming message
2. `lastInteractionAt` is updated to current timestamp
3. 24-hour window starts from this moment

### 2. Follow-Up Scheduling

1. AI generates follow-up messages with delay times
2. System calculates scheduled times based on current time + delay
3. Each follow-up is validated against the 24-hour window
4. Warning logged for follow-ups outside the window

### 3. Follow-Up Processing (Cron Job)

1. Cron job runs every minute to check for due follow-ups
2. For each due follow-up:
   - Check if it's within the 24-hour window
   - If within window: send the message and mark as sent
   - If outside window: mark as failed (for external plugin)
3. Update `lastInteractionAt` when messages are sent

### 4. External Plugin Handling

Follow-ups marked as "failed" due to window expiration should be:

1. Picked up by external plugins
2. Sent using Instagram message templates
3. Or handled through other approved messaging methods

## API Usage Examples

### Check 24-Hour Window Status

```typescript
const response = await fetch(`/api/instagram/contacts/${contactId}/24-hour-window`);
const { data } = await response.json();

if (data.isCurrentlyWithinWindow) {
  console.log(`${data.timeRemainingHours}h ${data.timeRemainingMinutes}m remaining`);
} else {
  console.log('Window expired, use external plugin');
}
```

### Validate Follow-Up Timing

```typescript
import { validateFollowUpTiming } from '@workspace/instagram-bot';

const validation = validateFollowUpTiming(
  contact.lastInteractionAt,
  scheduledTime
);

if (!validation.isValid) {
  console.error(validation.error);
  // Handle external plugin scheduling
}
```

### Filter Follow-Ups by Window

```typescript
import { filterFollowUpsWithin24Hours } from '@workspace/instagram-bot';

const { within24Hours, outside24Hours } = filterFollowUpsWithin24Hours(
  followUps,
  contact.lastInteractionAt
);

// Send within24Hours through our system
// Send outside24Hours through external plugin
```

## Configuration

### Server Setup (Non-Vercel)

For your own server deployment, use the provided setup script:

#### 1. Set Environment Variables

```bash
# Required environment variables
export CRON_SECRET="your-secure-random-secret"
export APP_URL="https://yourdomain.com"

# Optional: Global cleanup days (overridden by organization settings)
export FOLLOWUP_CLEANUP_DAYS=30
```

#### 2. Run Setup Script

```bash
# Make the script executable
chmod +x scripts/setup-cron.sh

# Run the setup script
./scripts/setup-cron.sh
```

This will install two cron jobs:
- **Follow-up processor**: Runs every minute
- **Cleanup old follow-ups**: Runs daily at 2 AM

#### 3. Manual Cron Setup (Alternative)

If you prefer manual setup, add these to your crontab:

```bash
# Edit crontab
crontab -e

# Add these lines:
# Process follow-ups every minute
* * * * * curl -s -H "Authorization: Bearer YOUR_CRON_SECRET" "https://yourdomain.com/api/cron/instagram-followups" >/dev/null 2>&1

# Clean up old follow-ups daily at 2 AM
0 2 * * * curl -s -H "Authorization: Bearer YOUR_CRON_SECRET" "https://yourdomain.com/api/cron/cleanup-old-followups" >/dev/null 2>&1
```

### Auto-Removal Configuration

#### Organization-Level Settings

Each organization can configure:

- **Auto Cleanup Enabled**: Enable/disable automatic cleanup
- **Cleanup Days**: Number of days after which to remove old follow-ups (1-365)

#### API Endpoints

**Get cleanup settings:**
```typescript
GET /api/instagram/cleanup-settings
```

**Update cleanup settings:**
```typescript
PATCH /api/instagram/cleanup-settings
Content-Type: application/json

{
  "followUpCleanupDays": 30,
  "autoCleanupEnabled": true
}
```

**Manual cleanup:**
```typescript
POST /api/instagram/manual-cleanup
Content-Type: application/json

{
  "cleanupDays": 30
}
```

### Environment Variables

```env
# Required
CRON_SECRET=your-secure-random-secret
APP_URL=https://yourdomain.com

# Optional
FOLLOWUP_CLEANUP_DAYS=30  # Global default, overridden by org settings
```

## Testing

Run the test suite:

```bash
npm test packages/instagram-bot/src/__tests__/24-hour-window.test.ts
```

## Monitoring

Monitor the system by checking:

1. Follow-up success/failure rates
2. 24-hour window compliance
3. External plugin handoff efficiency
4. `lastInteractionAt` tracking accuracy

## Future Enhancements

1. **Dashboard Indicators**: Show 24-hour window status in the UI
2. **Smart Scheduling**: Automatically adjust follow-up times to fit within windows
3. **Template Integration**: Direct integration with Instagram message templates
4. **Analytics**: Track window utilization and conversion rates
5. **Notifications**: Alert when windows are about to expire
