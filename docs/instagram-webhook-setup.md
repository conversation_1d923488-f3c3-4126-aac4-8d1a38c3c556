# Setting Up Instagram Webhook with ngrok

This guide will walk you through setting up an Instagram webhook using ngrok to expose your local development server to the internet.

## Prerequisites

1. An Instagram Business Account
2. A Facebook Developer Account
3. A Facebook App with Instagram Messaging permissions
4. ngrok installed on your machine

## Step 1: Install and Configure ngrok

1. Sign up for a free ngrok account at https://dashboard.ngrok.com/signup
2. Install ngrok if you haven't already:
   ```bash
   npm install -g ngrok
   ```
3. Authenticate ngrok with your authtoken:
   ```bash
   ngrok authtoken YOUR_AUTH_TOKEN
   ```
   You can find your authtoken at https://dashboard.ngrok.com/get-started/your-authtoken

## Step 2: Start Your Local Development Server

1. Start your development server:
   ```bash
   pnpm dev
   ```
2. Make sure your server is running on port 3000

## Step 3: Start ngrok to Expose Your Server

1. In a new terminal, start ngrok:
   ```bash
   ngrok http 3000
   ```
2. Note the HTTPS URL provided by ngrok (e.g., `https://abc123.ngrok.io`)

## Step 4: Configure Your Instagram Webhook

1. Go to your Facebook Developer Dashboard: https://developers.facebook.com/
2. Select your app
3. Navigate to "Messenger" > "Settings"
4. Under "Webhooks", click "Add Callback URL"
5. Enter your ngrok URL followed by the webhook path:
   ```
   https://your-ngrok-url.ngrok.io/api/webhooks/instagram
   ```
6. Enter your verify token (the same one in your .env file)
7. Click "Verify and Save"

## Step 5: Subscribe to Webhook Events

1. Under "Webhook Fields", select:
   - messages
   - messaging_postbacks
   - messaging_optins
2. Click "Subscribe"

## Step 6: Test Your Webhook

1. Go to your Instagram Settings page in your application
2. Use the Instagram Webhook Tester to send test messages
3. Check the logs in your terminal to see if the webhook is being received and processed

## Step 7: Configure Environment Variables

Make sure your `.env.local` file has the following variables:

```
# Instagram Integration
INSTAGRAM_APP_SECRET=your_app_secret
INSTAGRAM_VERIFY_TOKEN=your_verify_token

# OpenAI API (required for transcription and image analysis)
OPENAI_API_KEY=your_openai_api_key
```

## Troubleshooting

### Webhook Verification Fails

- Make sure your verify token matches the one in your environment variables
- Check that your webhook URL is correct and accessible

### Messages Not Being Processed

- Check that the Instagram bot is enabled in your settings
- Verify that your access token is valid
- Look for errors in the server logs

### OpenAI Integration Issues

- Verify your OpenAI API key is valid
- Check for rate limiting or quota issues

## Notes

- ngrok URLs change every time you restart ngrok (unless you have a paid plan)
- You'll need to update your webhook URL in the Facebook Developer Dashboard each time the ngrok URL changes
- For production, you'll need a stable URL with SSL
