# Instagram Integration

This document provides instructions for setting up and testing the Instagram integration.

## Overview

The Instagram integration allows the application to:

1. Receive messages from Instagram users via webhooks
2. Process messages, including text, images, and voice messages
3. Generate AI responses using OpenAI
4. Send responses back to the user

## Setup

### 1. Environment Variables

Make sure the following environment variables are set in your `.env` file:

```
# Instagram API
INSTAGRAM_APP_SECRET=your_app_secret
INSTAGRAM_VERIFY_TOKEN=your_verify_token

# OpenAI API
OPENAI_API_KEY=your_openai_api_key
```

### 2. Instagram Settings

Configure your Instagram settings in the application:

1. Go to the Instagram Settings page in your organization
2. Enable the Instagram bot
3. Set the response time range
4. Add your Instagram access token

## Testing

### Local Testing

You can test the Instagram integration locally using the provided test scripts:

1. **Test Instagram Bot**

   This script tests the bot functionality by creating a test contact and simulating a conversation:

   ```bash
   pnpm test:instagram-bot
   ```

2. **Test Instagram Webhook**

   This script tests the webhook functionality by sending simulated webhook events:

   ```bash
   pnpm test:instagram-webhook
   ```

3. **Using the Webhook Tester UI**

   You can also test the webhook functionality using the UI:

   1. Go to the Instagram Settings page in your organization
   2. Scroll down to the "Instagram Webhook Tester" section
   3. Enter a message and click "Send Test Webhook"
   4. You can also test with images and voice messages by providing URLs

### Production Setup

For production, you need to set up a real Instagram webhook:

1. **Create a Facebook App**

   1. Go to [Facebook for Developers](https://developers.facebook.com/)
   2. Create a new app with the "Business" type
   3. Add the "Instagram Messaging" product

2. **Configure Webhook**

   1. Set up a webhook with the following URL:
      ```
      https://your-domain.com/api/webhooks/instagram
      ```
   2. Use the same verify token as in your environment variables
   3. Subscribe to the following webhook fields:
      - `messages`
      - `messaging_postbacks`
      - `messaging_optins`

3. **Connect to Instagram**

   1. Connect your Instagram business account to your Facebook page
   2. Generate an access token for your page
   3. Add the access token to your Instagram settings in the application

## Webhook Testing with ngrok

To test with a real Instagram webhook, you can use ngrok to expose your local server:

1. Install ngrok:
   ```bash
   npm install -g ngrok
   ```

2. Start your application:
   ```bash
   pnpm dev
   ```

3. In a new terminal, start ngrok:
   ```bash
   ngrok http 3000
   ```

4. Use the ngrok URL as your webhook URL in the Facebook Developer Portal:
   ```
   https://your-ngrok-url.ngrok.io/api/webhooks/instagram
   ```

5. Test sending messages to your Instagram account

## Troubleshooting

### Common Issues

1. **Webhook Verification Fails**
   - Make sure your verify token matches the one in your environment variables
   - Check that your webhook URL is correct and accessible

2. **Messages Not Being Processed**
   - Check that the Instagram bot is enabled in your settings
   - Verify that your access token is valid
   - Look for errors in the server logs

3. **OpenAI Integration Issues**
   - Verify your OpenAI API key is valid
   - Check for rate limiting or quota issues

### Logs

Check the server logs for detailed information about webhook events and message processing:

```bash
pnpm dev
```

Look for log messages related to Instagram webhook events and message processing.

## Architecture

The Instagram integration consists of the following components:

1. **Webhook Endpoint** (`/api/webhooks/instagram`)
   - Receives webhook events from Instagram
   - Validates the signature
   - Processes the message asynchronously

2. **Message Processor** (`packages/instagram-bot/src/message-processor.ts`)
   - Processes incoming messages
   - Handles different types of media (text, image, audio)
   - Generates AI responses
   - Sends responses back to the user

3. **OpenAI Integration** (`packages/openai`)
   - Transcribes audio messages
   - Analyzes images
   - Generates responses based on conversation history

4. **Instagram Client** (`packages/instagram/src/client.ts`)
   - Sends messages to Instagram
   - Retrieves user profiles
   - Gets conversation history
