# Instagram Timestamp Handling Fixes

## Overview

This document outlines the comprehensive fixes implemented to resolve date/timestamp issues in the Instagram messaging API integration. The fixes ensure consistent and accurate timestamp handling across all parts of the system.

## Issues Identified

### 1. Inconsistent Timestamp Parsing
- **Problem**: Different parts of the codebase handled Instagram API timestamps differently
- **Impact**: Messages could display incorrect dates or times
- **Root Cause**: Instagram API returns `created_time` as Unix timestamp in seconds, but some code treated it as milliseconds

### 2. Mixed Timestamp Formats
- **Problem**: Instagram webhooks use milliseconds while API responses use seconds
- **Impact**: Inconsistent timestamp storage and display
- **Root Cause**: No unified handling for different timestamp formats

### 3. Webhook Timestamp Handling
- **Problem**: Webhook timestamp processing was inconsistent
- **Impact**: Real-time messages could have incorrect timestamps
- **Root Cause**: Unclear handling of `messaging.timestamp` format

### 4. API Message Timestamp Parsing
- **Problem**: Some API message parsing was correct, others were not
- **Impact**: Historical message sync could have wrong timestamps
- **Root Cause**: Inconsistent application of Unix timestamp conversion

## Solutions Implemented

### 1. Created Utility Functions (`apps/dashboard/lib/formatters.ts`)

#### `formatInstagramTimestamp(timestamp: string | number | Date): Date`
- Handles Instagram API timestamps (Unix seconds as string)
- Handles webhook timestamps (milliseconds as number)
- Handles JavaScript timestamps (milliseconds)
- Handles Date objects
- Uses intelligent threshold detection (1e10) to distinguish seconds vs milliseconds

#### `normalizeTimestamp(timestamp: string | number | Date | null | undefined): Date`
- Wrapper around `formatInstagramTimestamp` with null/undefined handling
- Returns current time for null/undefined inputs
- Ensures consistent timestamp normalization across the application

### 2. Updated Webhook Handler (`apps/dashboard/app/api/webhooks/instagram/route.ts`)

#### Changes Made:
- Added import for `normalizeTimestamp`
- Updated webhook timestamp handling with better logging
- Used `normalizeTimestamp()` for consistent message timestamp processing
- Updated historical message processing to use `normalizeTimestamp()`
- Added detailed logging for timestamp debugging

#### Key Fixes:
```typescript
// Before: Inconsistent handling
const timestamp = messaging.timestamp || Date.now();

// After: Consistent handling with logging
const timestamp = messaging.timestamp ? messaging.timestamp : Date.now();
console.log(`Timestamp: ${timestamp} (${new Date(timestamp).toISOString()})`);

// Before: Manual conversion
timestamp: new Date(parseInt(message.created_time) * 1000)

// After: Normalized handling
timestamp: normalizeTimestamp(message.created_time)
```

### 3. Updated Conversation API (`apps/dashboard/app/api/instagram/conversations/route.ts`)

#### Changes Made:
- Added import for `normalizeTimestamp`
- Updated API message timestamp parsing to use `normalizeTimestamp()`
- Enhanced logging for timestamp debugging

#### Key Fixes:
```typescript
// Before: Manual parsing
const timestamp = apiMessage.created_time ?
  new Date(parseInt(apiMessage.created_time) * 1000) :
  new Date();

// After: Normalized handling
const timestamp = normalizeTimestamp(apiMessage.created_time);
```

### 4. Updated Message Processor (`packages/instagram-bot/src/message-processor.ts`)

#### Changes Made:
- Updated timestamp handling to ensure proper Date object creation
- Added better logging for timestamp debugging

#### Key Fixes:
```typescript
// Before: Basic Date conversion
timestamp: timestamp || new Date()

// After: Proper timestamp handling
const messageTimestamp = timestamp ? new Date(timestamp) : new Date();
timestamp: messageTimestamp
```

## Testing

### Manual Testing Performed
- Created comprehensive test suite covering all timestamp scenarios
- Tested Instagram API timestamp format (Unix seconds as string)
- Tested webhook timestamp format (milliseconds as number)
- Tested edge cases (null, undefined, very old/new timestamps)
- Verified threshold logic for seconds vs milliseconds detection

### Test Results
✅ All timestamp handling functions work correctly
✅ Instagram API timestamps (seconds) → Correct Date objects
✅ Webhook timestamps (milliseconds) → Correct Date objects
✅ Null/undefined handling → Current time fallback
✅ Edge cases → Proper format detection

## Benefits

### 1. Consistent Timestamp Handling
- All timestamp processing now uses the same utility functions
- Eliminates inconsistencies between different parts of the system
- Reduces bugs related to timestamp format confusion

### 2. Better Debugging
- Enhanced logging shows both raw timestamps and parsed ISO strings
- Easier to identify timestamp-related issues in production
- Clear audit trail for timestamp processing

### 3. Future-Proof Design
- Utility functions can handle new timestamp formats
- Centralized logic makes updates easier
- Robust error handling for edge cases

### 4. Improved User Experience
- Messages display with correct timestamps
- Conversation history shows accurate timing
- Real-time messages have proper timestamps

## Files Modified

1. `apps/dashboard/lib/formatters.ts` - Added utility functions
2. `apps/dashboard/app/api/webhooks/instagram/route.ts` - Updated webhook handling
3. `apps/dashboard/app/api/instagram/conversations/route.ts` - Updated API handling
4. `packages/instagram-bot/src/message-processor.ts` - Updated message processing

## Verification Steps

To verify the fixes are working:

1. **Check Webhook Processing**: Monitor logs for timestamp parsing during webhook events
2. **Check API Sync**: Verify historical message timestamps are correct when syncing from Instagram API
3. **Check UI Display**: Ensure message timestamps display correctly in conversation interfaces
4. **Check Database**: Verify stored timestamps are in correct format and timezone

## Future Considerations

1. **Timezone Handling**: Consider adding timezone-aware timestamp processing
2. **Performance**: Monitor performance impact of timestamp normalization
3. **Testing**: Add automated tests when test framework is configured
4. **Documentation**: Update API documentation to reflect timestamp handling standards
