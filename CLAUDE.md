# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AISetter is an Instagram DM bot SaaS platform that automates lead qualification and appointment booking through AI-powered conversations. It includes a Chrome extension for Instagram integration and uses Claude 4 Sonnet API for intelligent responses.

## Key Architecture

### Monorepo Structure
- **apps/dashboard**: Main Next.js 15 app (App Router) - the primary SaaS application
- **apps/insta-dm-pro-main**: Chrome extension for Instagram automation
- **apps/marketing**: Marketing website
- **packages/database**: Prisma ORM with PostgreSQL
- **packages/instagram**: Instagram API client
- **packages/instagram-bot**: Bot logic and AI response handling
- **packages/auth**: NextAuth.js authentication

### Tech Stack
- React 19 + Next.js 15 (App Router)
- TypeScript everywhere
- Prisma ORM + PostgreSQL
- NextAuth.js (Google/Microsoft providers)
- Tailwind CSS + shadcn/ui
- Claude API (Anthropic) for conversations
- OpenAI API for additional AI features

## Essential Commands

### Development
```bash
# Install dependencies
pnpm install

# Start development (all apps)
pnpm dev

# Start specific app
pnpm --filter dashboard dev

# Show all TypeScript errors
__NEXT_TEST_MODE=true pnpm --filter dashboard dev
```

### Database
```bash
# Push schema changes (development)
pnpm --filter @workspace/database db:push

# Create migration (production)
pnpm --filter @workspace/database migrate dev

# Generate Prisma client
pnpm --filter @workspace/database generate
```

### Code Quality
```bash
# Format code
pnpm format:fix

# Lint code
pnpm lint:fix

# Type checking
pnpm typecheck
```

### Testing
```bash
# Test Instagram bot
pnpm test:instagram-bot

# Test Claude AI responses
pnpm test:claude-response

# Test webhook integration
pnpm test:instagram-webhook
```

### Build & Deploy
```bash
# Build all apps
pnpm build

# Start production
pnpm start
```

## Key Patterns

### Server Components & Actions
- Use Server Components for data fetching
- Use Server Actions (with next-safe-action) for mutations
- Implement optimistic updates with React transitions

### Authentication
- Multi-tenant organization structure
- Role-based access (OWNER, ADMIN, MEMBER)
- API key authentication for Chrome extension

### Instagram Integration Flow
1. Chrome extension collects followers → uploads to app
2. App analyzes conversations → sets priority/status
3. AI generates responses based on stage (new → initial → engaged → qualified)
4. Chrome extension polls for follow-ups → sends messages
5. 24-hour follow-up system for non-responders

### Contact Stages
- **new**: No conversation yet
- **initial**: First contact made
- **engaged**: Active conversation
- **qualified**: Lead qualified
- **formsent**: Appointment form sent
- **disqualified**: Not a good fit
- **converted**: Became a customer

### Priority System
- Priority 3: New followers (default)
- Priority 1: After first message sent (needs follow-up)
- Priorities 2-5: Based on engagement level

## Current Development Focus

Based on git status, the project is actively developing:
- Chrome extension API integration
- Instagram follower management
- Attack list functionality for targeted messaging
- Message batch and follow-up template systems
- Username to ID conversion for Instagram

## Important Notes

- Always check existing patterns before implementing new features
- Use existing UI components from packages/ui
- Follow the established Server Component/Action patterns
- Test Instagram features using the provided test scripts
- Environment variables are critical - check .env.example files