{"name": "next-prisma-authjs", "version": "2.0.0", "private": true, "scripts": {"dev": "cross-env FORCE_COLOR=1 turbo dev --parallel", "build": "turbo build --cache-dir=.turbo", "start": "cross-env FORCE_COLOR=1 turbo start --parallel", "clean": "git clean -xdf node_modules dist .next", "clean:workspaces": "turbo clean", "format": "turbo format --cache-dir=.turbo --continue -- --cache --cache-location=\"node_modules/.cache/.prettiercache\" --ignore-path=\"../../.gitignore\"", "format:fix": "turbo format --cache-dir=.turbo --continue -- --write --cache --cache-location=\"node_modules/.cache/.prettiercache\" --ignore-path=\"../../.gitignore\"", "lint": "turbo lint --cache-dir=.turbo --continue -- --cache --cache-location \"node_modules/.cache/.eslintcache\" && manypkg check", "lint:fix": "turbo lint --cache-dir=.turbo --continue -- --fix --cache --cache-location \"node_modules/.cache/.eslintcache\" && manypkg fix", "typecheck": "turbo typecheck --cache-dir=.turbo", "analyze": "turbo analyze --cache-dir=.turbo", "test": "turbo test --cache-dir=.turbo", "update": "pnpm update -r", "syncpack:list": "pnpm dlx syncpack list-mismatches", "syncpack:fix": "pnpm dlx syncpack fix-mismatches", "preinstall": "pnpm run --filter requirements-check requirements", "postinstall": "manypkg fix", "test:instagram-bot": "tsx scripts/test-instagram-bot.ts", "test:instagram-webhook": "tsx scripts/test-instagram-webhook.ts", "test:instagram-webhook-local": "tsx scripts/test-instagram-webhook-local.ts", "test:ai-response": "tsx scripts/test-ai-response.ts", "test:claude": "tsx scripts/test-claude.ts", "test:claude-response": "tsx scripts/test-claude-response.ts", "test:multi-message": "tsx scripts/test-multi-message.ts", "test:original-format": "tsx scripts/test-original-format.ts", "test:message-delay": "tsx scripts/test-message-delay.ts", "test:message-delay-mock": "tsx scripts/test-message-delay-mock.ts", "test:webhook-echo": "tsx scripts/test-webhook-echo.ts", "ngrok": "ngrok http 3000"}, "devDependencies": {"@manypkg/cli": "0.23.0", "@turbo/gen": "2.4.4", "@types/bcryptjs": "2.4.6", "@types/uuid": "10.0.0", "bcryptjs": "2.4.3", "cross-env": "7.0.3", "dotenv": "^16.5.0", "openai": "^4.28.0", "tsx": "^4.19.3", "turbo": "2.4.4", "typescript": "5.7.2", "uuid": "11.1.0"}, "pnpm": {"overrides": {"react": "19.0.0", "react-dom": "19.0.0", "react-is": "19.0.0", "require-in-the-middle": "7.5.2", "archiver": "7.0.1", "unzipper": "0.12.3", "tree-sitter": "0.21.1"}}, "packageManager": "pnpm@9.12.0", "engines": {"node": ">=20"}}