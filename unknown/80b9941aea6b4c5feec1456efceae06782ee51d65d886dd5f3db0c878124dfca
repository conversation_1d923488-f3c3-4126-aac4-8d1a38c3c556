'use server';

import { z } from 'zod';
import { revalidateTag } from 'next/cache';

import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';

import { authOrganizationActionClient } from '~/actions/safe-action';
import { Caching, OrganizationCacheKey } from '~/data/caching';

const schema = z.object({
  id: z.string(),
  name: z.string().min(1),
  description: z.string().min(1),
  promptText: z.string().min(1),
  isDefault: z.boolean().default(false)
});

export const updateBotStyle = authOrganizationActionClient
  .metadata({ actionName: 'updateBotStyle' })
  .schema(schema)
  .action(async ({ parsedInput, ctx }) => {
    // Only allow SaaS admin (<EMAIL>) to update bot styles
    if (ctx.session.user.email !== '<EMAIL>') {
      throw new Error('Only SaaS admin can update bot styles');
    }

    // Check if bot style exists
    const existingStyle = await prisma.botStyle.findUnique({
      where: {
        id: parsedInput.id
      }
    });

    if (!existingStyle) {
      throw new NotFoundError('Bot style not found');
    }

    // If this is set as default, unset any existing default
    if (parsedInput.isDefault && !existingStyle.isDefault) {
      await prisma.botStyle.updateMany({
        where: {
          isDefault: true
        },
        data: {
          isDefault: false
        }
      });
    }

    // Update the bot style
    const botStyle = await prisma.botStyle.update({
      where: {
        id: parsedInput.id
      },
      data: {
        name: parsedInput.name,
        description: parsedInput.description,
        promptText: parsedInput.promptText,
        isDefault: parsedInput.isDefault
      }
    });

    // Revalidate cache for both global and organization-specific caches
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.BotStyles,
        'global'
      )
    );

    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.BotStyles,
        ctx.organization.id
      )
    );

    return botStyle;
  });
