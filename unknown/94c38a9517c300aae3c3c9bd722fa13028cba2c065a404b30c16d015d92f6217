'use server';

import { revalidateTag } from 'next/cache';
import { z } from 'zod';
import { transcribeMedia as transcribeMediaWithOpenAI } from '~/lib/openai-client';

import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  Caching,
  OrganizationCacheKey
} from '~/data/caching';



const transcribeMediaSchema = z.object({
  messageId: z.string().uuid()
});

export const transcribeMedia = authOrganizationActionClient
  .metadata({ actionName: 'transcribeMedia' })
  .schema(transcribeMediaSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Find the message
    const message = await prisma.instagramMessage.findFirst({
      where: {
        id: parsedInput.messageId,
        InstagramContact: {
          organizationId: ctx.organization.id
        }
      },
      include: {
        InstagramContact: true
      }
    });

    if (!message) {
      throw new NotFoundError('Message not found');
    }

    // Check if the message has media and no description yet
    if (!message.mediaUrl || message.mediaDescription) {
      return {
        success: false,
        message: 'No media to transcribe or already transcribed'
      };
    }

    try {
      // Transcribe media using OpenAI
      let mediaDescription = '';

      if (message.mediaType === 'image' || message.mediaType === 'audio') {
        mediaDescription = await transcribeMediaWithOpenAI(message.mediaUrl, message.mediaType);
      } else if (message.mediaType === 'video') {
        mediaDescription = 'Video content (transcription not available)';
      } else {
        mediaDescription = 'Media content (transcription not available)';
      }

      // Update the message with the transcription
      const updatedMessage = await prisma.instagramMessage.update({
        where: {
          id: message.id
        },
        data: {
          mediaDescription
        }
      });

      // Revalidate cache
      revalidateTag(
        Caching.createOrganizationTag(
          OrganizationCacheKey.InstagramContact,
          ctx.organization.id,
          message.InstagramContact.id
        )
      );

      return {
        success: true,
        mediaDescription
      };
    } catch (error) {
      console.error('Error transcribing media:', error);
      throw new Error('Failed to transcribe media');
    }
  });
