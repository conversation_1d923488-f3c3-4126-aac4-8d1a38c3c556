#!/usr/bin/env tsx

/**
 * Test script for Claude API response functionality
 * This script tests the Instagram bot's Claude API integration
 */

import { config } from 'dotenv';
import { join } from 'path';

// Load environment variables from the Instagram bot package
config({ path: join(__dirname, '../packages/instagram-bot/.env') });

import { generateInstagramResponse } from '../packages/instagram-bot/src/ai-response';
import { prisma } from '../packages/database/src/client';

async function testClaudeApiResponse() {
  console.log('🧪 Testing Claude API Response...\n');

  try {
    // Test 1: Environment Variables
    console.log('📋 Test 1: Environment Variables');
    console.log('================================');

    const hasAnthropicKey = !!process.env.ANTHROPIC_API_KEY;
    const claudeModel = process.env.CLAUDE_MODEL;
    const keyLength = process.env.ANTHROPIC_API_KEY?.length || 0;

    console.log(`✅ ANTHROPIC_API_KEY present: ${hasAnthropicKey}`);
    console.log(`✅ ANTHROPIC_API_KEY length: ${keyLength} characters`);
    console.log(`✅ CLAUDE_MODEL: ${claudeModel}`);

    if (!hasAnthropicKey) {
      throw new Error('❌ ANTHROPIC_API_KEY is not set in environment variables');
    }

    console.log('\n');

    // Test 2: Database Connection
    console.log('📋 Test 2: Database Connection');
    console.log('==============================');

    try {
      await prisma.$connect();
      console.log('✅ Database connection successful');

      // Check admin settings for caching
      const adminSettings = await prisma.adminSettings.findFirst({
        select: {
          cacheForAllUsers: true,
          cacheDurationHours: true
        }
      });

      console.log(`✅ Admin settings found: ${!!adminSettings}`);
      console.log(`✅ Cache for all users: ${adminSettings?.cacheForAllUsers || false}`);
      console.log(`✅ Cache duration hours: ${adminSettings?.cacheDurationHours || 'not set'}`);
    } catch (error) {
      console.log(`⚠️  Database connection issue: ${error}`);
    }

    console.log('\n');

    // Test 3: Basic Claude API Call
    console.log('📋 Test 3: Basic Claude API Call');
    console.log('=================================');

    const testPrompt = `You are a helpful Instagram DM assistant. Your name is Ja.

Your goal is to:
1. Qualify leads for fitness coaching services
2. Book appointments for consultations
3. Be friendly and conversational

Always respond in JSON format with the required structure.`;

    const testConversationHistory = `User: Hi there! I saw your fitness content and I'm interested in getting in shape.
Ja: Hello! That's fantastic that you're interested in getting fit! I'd love to help you on your fitness journey. What are your main fitness goals right now?
User: I want to lose about 20 pounds and build some muscle. I've tried dieting before but always give up.`;

    console.log('📤 Sending test request to Claude API...');
    console.log(`📝 Conversation history length: ${testConversationHistory.length} characters`);

    const startTime = Date.now();

    const response = await generateInstagramResponse({
      organizationId: 'test-org-id',
      conversationHistory: testConversationHistory,
      prompt: testPrompt
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log(`⏱️  Response time: ${responseTime}ms`);
    console.log('✅ Claude API call successful!');
    console.log('\n📥 Response received:');
    console.log('====================');
    console.log(JSON.stringify(response, null, 2));

    console.log('\n');

    // Test 4: Response Validation
    console.log('📋 Test 4: Response Validation');
    console.log('==============================');

    const requiredFields = ['message', 'stage', 'messages', 'message1'];
    const missingFields = requiredFields.filter(field => !(field in response));

    if (missingFields.length === 0) {
      console.log('✅ All required fields present in response');
    } else {
      console.log(`❌ Missing required fields: ${missingFields.join(', ')}`);
    }

    // Validate stage
    const validStages = ['initial', 'qualified', 'converted', 'disqualified'];
    if (validStages.includes(response.stage)) {
      console.log(`✅ Valid stage: ${response.stage}`);
    } else {
      console.log(`❌ Invalid stage: ${response.stage}. Expected one of: ${validStages.join(', ')}`);
    }

    // Validate message content
    if (response.message && response.message.length > 0) {
      console.log(`✅ Message content present (${response.message.length} characters)`);
    } else {
      console.log('❌ Message content is empty or missing');
    }

    console.log('\n');

    // Test 5: Error Handling
    console.log('📋 Test 5: Error Handling');
    console.log('=========================');

    try {
      console.log('🧪 Testing with invalid organization ID...');
      await generateInstagramResponse({
        organizationId: '',
        conversationHistory: 'Test message',
        prompt: 'Test prompt'
      });
      console.log('⚠️  Expected error but call succeeded');
    } catch (error) {
      console.log(`✅ Error handling working: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    console.log('\n');

    // Test Summary
    console.log('📊 Test Summary');
    console.log('===============');
    console.log('✅ Environment variables: PASSED');
    console.log('✅ Database connection: PASSED');
    console.log('✅ Claude API call: PASSED');
    console.log('✅ Response validation: PASSED');
    console.log('✅ Error handling: PASSED');
    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed with error:');
    console.error('==========================');
    console.error(error);

    if (error instanceof Error) {
      console.error(`\nError message: ${error.message}`);
      console.error(`Error stack: ${error.stack}`);
    }

    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
if (require.main === module) {
  testClaudeApiResponse().catch(console.error);
}

export { testClaudeApiResponse };
