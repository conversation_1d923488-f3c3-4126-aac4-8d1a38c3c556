#!/usr/bin/env node

/**
 * Debug script to test the exact API query logic
 * This simulates what the API endpoint does to find the issue
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// You'll need to replace this with your actual organization ID
const TEST_ORG_ID = '112f0434-23f3-42f9-8786-e296e211aca0';

async function debugAPIQuery() {
  console.log('🔍 Debugging API Query Logic');
  console.log('============================');
  console.log(`🏢 Testing with Organization ID: ${TEST_ORG_ID}`);
  console.log('');

  try {
    // Test NEW InstagramFollowUp query (same as API)
    console.log('📊 Testing NEW InstagramFollowUp query:');
    const newFollowUps = await prisma.instagramFollowUp.findMany({
      where: {
        status: 'pending',
        InstagramContact: {
          User: {
            memberships: {
              some: {
                organizationId: TEST_ORG_ID
              }
            }
          },
          isIgnored: false
        }
      },
      include: {
        InstagramContact: {
          select: {
            instagramId: true,
            instagramNickname: true,
            isIgnored: true,
            organizationId: true
          }
        }
      },
      orderBy: [
        { scheduledTime: 'asc' },
        { sequenceNumber: 'asc' }
      ]
    });

    console.log(`   Found ${newFollowUps.length} follow-ups with API query`);

    if (newFollowUps.length > 0) {
      newFollowUps.forEach((followUp, index) => {
        console.log(`   ${index + 1}. ${followUp.InstagramContact.instagramNickname}`);
        console.log(`      🆔 Follow-up ID: ${followUp.id}`);
        console.log(`      👤 Contact ID: ${followUp.contactId}`);
        console.log(`      📝 Status: ${followUp.status}`);
        console.log(`      🚫 Is Ignored: ${followUp.InstagramContact.isIgnored}`);
        console.log(`      🏢 Contact Org ID: ${followUp.InstagramContact.organizationId}`);
        console.log('');
      });
    }

    // Test LEGACY InstagramContact query (same as API)
    console.log('📊 Testing LEGACY InstagramContact query:');
    const legacyContactsWithFollowUps = await prisma.instagramContact.findMany({
      where: {
        organizationId: TEST_ORG_ID,
        isIgnored: false,
        OR: [
          { followUpStatus1: 'pending', followUpTime1: { not: null }, followUpMessage1: { not: null } },
          { followUpStatus2: 'pending', followUpTime2: { not: null }, followUpMessage2: { not: null } },
          { followUpStatus3: 'pending', followUpTime3: { not: null }, followUpMessage3: { not: null } },
          { followUpStatus4: 'pending', followUpTime4: { not: null }, followUpMessage4: { not: null } }
        ]
      },
      select: {
        id: true,
        instagramId: true,
        instagramNickname: true,
        isIgnored: true,
        organizationId: true,
        followUpMessage1: true,
        followUpTime1: true,
        followUpStatus1: true,
        followUpMessage2: true,
        followUpTime2: true,
        followUpStatus2: true,
        followUpMessage3: true,
        followUpTime3: true,
        followUpStatus3: true,
        followUpMessage4: true,
        followUpTime4: true,
        followUpStatus4: true
      }
    });

    console.log(`   Found ${legacyContactsWithFollowUps.length} legacy contacts with API query`);

    // Now let's check what might be wrong
    console.log('');
    console.log('🔍 Debugging potential issues:');

    // Check if contacts exist but are ignored
    const allContacts = await prisma.instagramContact.findMany({
      where: {
        organizationId: TEST_ORG_ID
      },
      select: {
        id: true,
        instagramNickname: true,
        isIgnored: true,
        organizationId: true
      }
    });

    console.log(`   Total contacts in organization: ${allContacts.length}`);
    const ignoredContacts = allContacts.filter(c => c.isIgnored);
    console.log(`   Ignored contacts: ${ignoredContacts.length}`);

    if (ignoredContacts.length > 0) {
      console.log('   Ignored contacts:');
      ignoredContacts.forEach(contact => {
        console.log(`     - ${contact.instagramNickname} (${contact.id})`);
      });
    }

    // Check if there are follow-ups for ignored contacts
    const followUpsForIgnoredContacts = await prisma.instagramFollowUp.findMany({
      where: {
        status: 'pending',
        InstagramContact: {
          organizationId: TEST_ORG_ID,
          isIgnored: true  // Check ignored contacts
        }
      },
      include: {
        InstagramContact: {
          select: {
            instagramNickname: true,
            isIgnored: true
          }
        }
      }
    });

    console.log(`   Follow-ups for IGNORED contacts: ${followUpsForIgnoredContacts.length}`);

    if (followUpsForIgnoredContacts.length > 0) {
      console.log('   ⚠️  Found follow-ups for ignored contacts:');
      followUpsForIgnoredContacts.forEach(fu => {
        console.log(`     - ${fu.InstagramContact.instagramNickname} (ignored: ${fu.InstagramContact.isIgnored})`);
      });
    }

  } catch (error) {
    console.error('❌ Error in debug query:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the debug
debugAPIQuery();
