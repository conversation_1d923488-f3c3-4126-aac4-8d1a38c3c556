#!/usr/bin/env node

/**
 * Debug script to check the specific contact that has follow-ups
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugSpecificContact() {
  console.log('🔍 Debugging Specific Contact: alexgodlewsky');
  console.log('=============================================');
  console.log('');

  try {
    // Find the contact
    const contact = await prisma.instagramContact.findFirst({
      where: {
        instagramNickname: 'alexgodlewsky'
      },
      include: {
        User: {
          include: {
            memberships: {
              include: {
                organization: true
              }
            }
          }
        },
        InstagramFollowUp: true
      }
    });

    if (!contact) {
      console.log('❌ Contact not found');
      return;
    }

    console.log('📋 Contact Details:');
    console.log(`   👤 Username: ${contact.instagramNickname}`);
    console.log(`   🆔 Contact ID: ${contact.id}`);
    console.log(`   🏢 Organization ID: ${contact.organizationId}`);
    console.log(`   🚫 Is Ignored: ${contact.isIgnored}`);
    console.log(`   👤 User ID: ${contact.userId}`);
    console.log('');

    console.log('👥 User Memberships:');
    if (contact.User.memberships.length === 0) {
      console.log('   ❌ No memberships found!');
    } else {
      contact.User.memberships.forEach((membership, index) => {
        console.log(`   ${index + 1}. Organization: ${membership.organization.name}`);
        console.log(`      🆔 Org ID: ${membership.organizationId}`);
        console.log(`      👑 Role: ${membership.role}`);
        console.log(`      👤 User ID: ${membership.userId}`);
        console.log('');
      });
    }

    console.log('📅 Follow-ups for this contact:');
    if (contact.InstagramFollowUp.length === 0) {
      console.log('   ❌ No follow-ups found!');
    } else {
      contact.InstagramFollowUp.forEach((followUp, index) => {
        const scheduledTime = new Date(followUp.scheduledTime);
        const now = new Date();
        const hoursFromNow = (scheduledTime - now) / (1000 * 60 * 60);
        const daysFromNow = hoursFromNow / 24;

        console.log(`   ${index + 1}. Follow-up #${followUp.sequenceNumber}`);
        console.log(`      🆔 ID: ${followUp.id}`);
        console.log(`      📅 Scheduled: ${scheduledTime.toLocaleString()}`);
        console.log(`      ⏰ ${daysFromNow.toFixed(1)} days from now`);
        console.log(`      📝 Status: ${followUp.status}`);
        console.log(`      💬 Message: ${followUp.message.substring(0, 50)}...`);
        console.log('');
      });
    }

    // Test the exact API query for this contact
    console.log('🧪 Testing API Query for this contact:');
    
    const testQuery = await prisma.instagramFollowUp.findMany({
      where: {
        status: 'pending',
        InstagramContact: {
          id: contact.id,
          User: {
            memberships: {
              some: {
                organizationId: contact.organizationId
              }
            }
          },
          isIgnored: false
        }
      },
      include: {
        InstagramContact: {
          select: {
            instagramId: true,
            instagramNickname: true
          }
        }
      }
    });

    console.log(`   API query result: ${testQuery.length} follow-ups`);
    
    if (testQuery.length === 0) {
      console.log('');
      console.log('🔍 Checking why API query failed:');
      
      // Check if contact is ignored
      if (contact.isIgnored) {
        console.log('   ❌ Contact is marked as IGNORED');
      } else {
        console.log('   ✅ Contact is NOT ignored');
      }
      
      // Check if user has membership for this org
      const hasMembership = contact.User.memberships.some(m => m.organizationId === contact.organizationId);
      if (!hasMembership) {
        console.log('   ❌ User does NOT have membership for this organization');
        console.log(`      Contact org: ${contact.organizationId}`);
        console.log(`      User memberships: ${contact.User.memberships.map(m => m.organizationId).join(', ')}`);
      } else {
        console.log('   ✅ User HAS membership for this organization');
      }
      
      // Check follow-up status
      const pendingFollowUps = contact.InstagramFollowUp.filter(fu => fu.status === 'pending');
      if (pendingFollowUps.length === 0) {
        console.log('   ❌ No follow-ups with PENDING status');
      } else {
        console.log(`   ✅ ${pendingFollowUps.length} follow-ups with PENDING status`);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the debug
debugSpecificContact();
