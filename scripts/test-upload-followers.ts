#!/usr/bin/env tsx

/**
 * Test script for uploading followers to trigger 5-day interval
 * Usage: tsx scripts/test-upload-followers.ts [count]
 */

const API_KEY = 'api_189dc9010290e48b264fc08943820696';
const BASE_URL = 'http://localhost:3000';

async function uploadFollowers(count: number = 250) {
  console.log(`🚀 Uploading ${count} test followers...`);
  
  // Generate test followers
  const followers = Array.from({ length: count }, (_, i) => ({
    instagramNickname: `test_user_${Date.now()}_${i + 1}`,
    instagramId: `${Date.now()}${i + 1}`,
    avatar: `https://picsum.photos/150/150?random=${Date.now()}_${i + 1}`,
    followerCount: Math.floor(Math.random() * 10000) + 100,
    isVerified: Math.random() > 0.8 // 20% chance of being verified
  }));

  const requestData = {
    followers,
    startPosition: 0,
    totalFollowers: count,
    isComplete: false
  };

  try {
    const response = await fetch(`${BASE_URL}/api/chrome-extension/process-followers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY
      },
      body: JSON.stringify(requestData)
    });

    const result = await response.json();
    
    console.log(`📊 Response Status: ${response.status}`);
    console.log(`📊 Response:`, JSON.stringify(result, null, 2));

    if (result.success) {
      console.log(`✅ Successfully uploaded ${result.data.newFollowers} followers`);
      console.log(`📊 Processed: ${result.data.processed}`);
      console.log(`📊 New: ${result.data.newFollowers}`);
      console.log(`📊 Existing: ${result.data.existingFollowers}`);
      
      if (result.data.errors.length > 0) {
        console.log(`❌ Errors: ${result.data.errors.length}`);
        result.data.errors.forEach((error: string) => console.log(`  - ${error}`));
      }
    } else {
      console.log(`❌ Upload failed: ${result.error}`);
    }

    return result;
  } catch (error) {
    console.error('❌ Request failed:', error);
    throw error;
  }
}

async function checkScrapingStatus() {
  console.log('\n🔍 Checking scraping status after upload...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/chrome-extension/scraping-status`, {
      headers: {
        'X-API-Key': API_KEY
      }
    });

    const result = await response.json();
    
    if (result.success) {
      const status = result.data;
      console.log(`✅ Can Scrape: ${status.canScrape}`);
      console.log(`📊 Total Scraped: ${status.totalFollowersScraped}`);
      console.log(`📱 Extension Status: ${status.extensionStatus}`);
      console.log(`💬 Status Message: ${status.statusMessage}`);
      console.log(`🎯 Recommended Action: ${status.recommendedAction}`);
      
      if (!status.canScrape) {
        console.log(`⏰ Next Allowed: ${status.nextScrapingAllowedAt}`);
        console.log(`⏳ Wait Time: ${status.waitTimeHuman}`);
      }
    }

    return result;
  } catch (error) {
    console.error('❌ Status check failed:', error);
    throw error;
  }
}

async function resetScrapingInterval() {
  console.log('\n🔄 Resetting scraping interval...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/admin/reset-scraping-interval`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY
      }
    });

    const result = await response.json();
    
    console.log(`📊 Reset Response:`, JSON.stringify(result, null, 2));

    if (result.success) {
      console.log(`✅ ${result.message}`);
      console.log(`📊 Previous state:`, result.data.previousState);
      console.log(`📊 New state:`, result.data.newState);
    } else {
      console.log(`❌ Reset failed: ${result.error}`);
    }

    return result;
  } catch (error) {
    console.error('❌ Reset failed:', error);
    throw error;
  }
}

async function runTest() {
  const args = process.argv.slice(2);
  const command = args[0] || 'upload';
  const count = parseInt(args[1]) || 250;

  console.log('🧪 Instagram Follower Upload Test');
  console.log('=' .repeat(50));

  try {
    switch (command) {
      case 'upload':
        await uploadFollowers(count);
        await checkScrapingStatus();
        break;
        
      case 'status':
        await checkScrapingStatus();
        break;
        
      case 'reset':
        await resetScrapingInterval();
        await checkScrapingStatus();
        break;
        
      case 'full':
        console.log('📋 Step 1: Check initial status');
        await checkScrapingStatus();
        
        console.log('\n📋 Step 2: Upload followers');
        await uploadFollowers(count);
        
        console.log('\n📋 Step 3: Check status after upload');
        await checkScrapingStatus();
        
        console.log('\n📋 Step 4: Reset interval');
        await resetScrapingInterval();
        
        console.log('\n📋 Step 5: Check final status');
        await checkScrapingStatus();
        break;
        
      default:
        console.log('Usage: tsx scripts/test-upload-followers.ts [command] [count]');
        console.log('Commands:');
        console.log('  upload [count] - Upload followers (default: 250)');
        console.log('  status         - Check scraping status');
        console.log('  reset          - Reset scraping interval');
        console.log('  full [count]   - Run full test cycle');
        console.log('');
        console.log('Examples:');
        console.log('  tsx scripts/test-upload-followers.ts upload 250');
        console.log('  tsx scripts/test-upload-followers.ts status');
        console.log('  tsx scripts/test-upload-followers.ts reset');
        console.log('  tsx scripts/test-upload-followers.ts full 250');
        break;
    }

    console.log('\n🎉 Test completed!');
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
runTest();
