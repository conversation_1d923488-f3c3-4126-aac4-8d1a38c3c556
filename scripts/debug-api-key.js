#!/usr/bin/env node

/**
 * Debug script to check what organization the API key belongs to
 */

const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

// Your API key
const API_KEY = 'api_7cfa680ec361ae4451085616c8174891';

// Hash function (same as the API uses)
function hashApiKey(apiKey) {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

async function debugAPIKey() {
  console.log('🔍 Debugging API Key Organization');
  console.log('=================================');
  console.log(`🔑 API Key: ${API_KEY}`);
  console.log('');

  try {
    // Hash the API key and find it in the database
    const hashedKey = hashApiKey(API_KEY);
    console.log(`🔐 Hashed Key: ${hashedKey.substring(0, 16)}...`);
    console.log('');

    const apiKeyRecord = await prisma.apiKey.findFirst({
      where: {
        hashedKey: hashedKey
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      }
    });

    if (!apiKeyRecord) {
      console.log('❌ API Key not found in database');
      return;
    }

    console.log('📋 API Key Details:');
    console.log(`   🆔 Key ID: ${apiKeyRecord.id}`);
    console.log(`   📝 Description: ${apiKeyRecord.description}`);
    console.log(`   🏢 Organization: ${apiKeyRecord.organization.name}`);
    console.log(`   🆔 Organization ID: ${apiKeyRecord.organizationId}`);
    console.log(`   🔗 Organization Slug: ${apiKeyRecord.organization.slug}`);
    console.log(`   📅 Expires At: ${apiKeyRecord.expiresAt || 'Never'}`);
    console.log(`   🕐 Last Used: ${apiKeyRecord.lastUsedAt || 'Never'}`);
    console.log('');

    // Check if this matches the organization where follow-ups exist
    const followUpOrgId = '112f0434-23f3-42f9-8786-e296e211aca0';

    if (apiKeyRecord.organizationId === followUpOrgId) {
      console.log('✅ API Key organization MATCHES follow-up organization');
    } else {
      console.log('❌ API Key organization DOES NOT MATCH follow-up organization');
      console.log(`   API Key Org: ${apiKeyRecord.organizationId}`);
      console.log(`   Follow-up Org: ${followUpOrgId}`);
    }

    // Now let's test the exact API verification logic
    console.log('');
    console.log('🧪 Testing API Key Verification Logic:');

    // This simulates the verifyApiKey function
    const now = new Date();
    const isExpired = apiKeyRecord.expiresAt && now > apiKeyRecord.expiresAt;
    const isActive = !isExpired;

    const verificationResult = {
      success: isActive,
      organizationId: apiKeyRecord.organizationId,
      errorMessage: isActive ? null : (isExpired ? 'API key is expired' : 'API key is inactive')
    };

    console.log(`   Verification Success: ${verificationResult.success}`);
    console.log(`   Returned Org ID: ${verificationResult.organizationId}`);

    if (verificationResult.errorMessage) {
      console.log(`   Error: ${verificationResult.errorMessage}`);
    }

    // Test the follow-up query with the API key's organization
    console.log('');
    console.log('🔍 Testing follow-up query with API key organization:');

    const followUpsForAPIKeyOrg = await prisma.instagramFollowUp.findMany({
      where: {
        status: 'pending',
        InstagramContact: {
          organizationId: apiKeyRecord.organizationId,
          isIgnored: false
        }
      },
      include: {
        InstagramContact: {
          select: {
            instagramId: true,
            instagramNickname: true
          }
        }
      }
    });

    console.log(`   Found ${followUpsForAPIKeyOrg.length} follow-ups for API key organization`);

    if (followUpsForAPIKeyOrg.length > 0) {
      followUpsForAPIKeyOrg.forEach((fu, index) => {
        console.log(`   ${index + 1}. ${fu.InstagramContact.instagramNickname} - Seq #${fu.sequenceNumber}`);
      });
    }

    // Also check legacy follow-ups
    const legacyFollowUpsForAPIKeyOrg = await prisma.instagramContact.findMany({
      where: {
        organizationId: apiKeyRecord.organizationId,
        isIgnored: false,
        OR: [
          { followUpStatus1: 'pending', followUpTime1: { not: null }, followUpMessage1: { not: null } },
          { followUpStatus2: 'pending', followUpTime2: { not: null }, followUpMessage2: { not: null } },
          { followUpStatus3: 'pending', followUpTime3: { not: null }, followUpMessage3: { not: null } },
          { followUpStatus4: 'pending', followUpTime4: { not: null }, followUpMessage4: { not: null } }
        ]
      }
    });

    console.log(`   Found ${legacyFollowUpsForAPIKeyOrg.length} legacy follow-ups for API key organization`);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the debug
debugAPIKey();
