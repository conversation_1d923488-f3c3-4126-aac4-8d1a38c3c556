#!/bin/bash

# AI Setter Database Deployment Script
# This script deploys the consolidated database migration to production

set -e  # Exit on any error

echo "🚀 AI Setter Database Deployment"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "packages/database/prisma/schema.prisma" ]; then
    print_error "Please run this script from the root of the ai-setter project"
    exit 1
fi

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    print_error "DATABASE_URL environment variable is not set"
    print_warning "Please set DATABASE_URL before running this script"
    exit 1
fi

print_status "Environment check passed"

# Navigate to database package
cd packages/database

print_status "Checking current migration status..."

# Check migration status
npx prisma migrate status

print_warning "This will apply database migrations to your production database"
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Deployment cancelled"
    exit 0
fi

print_status "Applying database migrations..."

# Apply migrations
npx prisma migrate deploy

print_status "Generating Prisma client..."

# Generate Prisma client
npx prisma generate

print_status "Verifying migration status..."

# Verify migrations were applied
npx prisma migrate status

print_status "Database deployment completed successfully!"

echo ""
echo "🎉 Your database is now up to date with the latest schema"
echo "📝 Migration applied: 20250525185746_consolidated_production_schema"
echo ""
echo "Next steps:"
echo "1. Restart your application server"
echo "2. Verify the application is working correctly"
echo "3. Monitor logs for any issues"
