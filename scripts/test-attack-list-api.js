/**
 * Test script to show what the Attack List API returns
 * This will help visualize the data structure and content
 */

const API_KEY = 'api_277249e31e1bcfd500ee3011fcd7a7b8'; // Your API key
const BASE_URL = 'http://localhost:3000'; // Adjust if different

async function testAttackListAPI() {
  console.log('🎯 Testing Attack List API...\n');
  
  try {
    // First verify the API key to get the organization ID
    console.log('🔑 Verifying API key...');
    const verifyResponse = await fetch(`${BASE_URL}/api/verify-api-key`, {
      method: 'POST',
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      }
    });

    if (!verifyResponse.ok) {
      throw new Error(`API key verification failed: HTTP ${verifyResponse.status}`);
    }

    const verifyData = await verifyResponse.json();
    if (!verifyData.success) {
      throw new Error(`API key verification failed: ${verifyData.message}`);
    }

    const organizationId = verifyData.data.organizationId;
    console.log(`✅ API key verified for organization: ${verifyData.data.organizationName} (${organizationId})`);

    // Test the messages-to-send endpoint with organization ID
    console.log('\n📡 Calling /api/chrome-extension/messages-to-send...');
    const response = await fetch(`${BASE_URL}/api/chrome-extension/messages-to-send?organizationId=${organizationId}`, {
      method: 'GET',
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    console.log('✅ API Response received!\n');
    console.log('📊 ATTACK LIST DATA:');
    console.log('='.repeat(50));
    console.log(JSON.stringify(data, null, 2));
    console.log('='.repeat(50));
    
    // Parse and display in a more readable format
    if (data.success) {
      console.log('\n📋 SUMMARY:');
      console.log(`• Total messages ready: ${data.count}`);
      console.log(`• Total contacts ready: ${data.metadata.totalReadyContacts}`);
      console.log(`• Smart Focus enabled: ${data.metadata.smartFocusEnabled}`);
      console.log(`• Timestamp: ${data.metadata.timestamp}`);
      
      if (data.data && data.data.length > 0) {
        console.log('\n🎯 MESSAGES TO SEND:');
        data.data.forEach((message, index) => {
          console.log(`\n${index + 1}. Contact: @${message.username}`);
          console.log(`   ID: ${message.id}`);
          console.log(`   Type: ${message.type}`);
          console.log(`   Message: "${message.message}"`);
          
          if (message.followUpId) {
            console.log(`   Follow-up ID: ${message.followUpId}`);
          }
          
          if (message.batchMessages) {
            console.log(`   Batch Messages (${message.batchMessages.length}):`);
            message.batchMessages.forEach((batch, bIndex) => {
              console.log(`     ${bIndex + 1}. "${batch.message}"`);
            });
          }
        });
      } else {
        console.log('\n📭 No messages ready to send at this time.');
      }
    } else {
      console.log(`❌ API Error: ${data.error}`);
    }
    
  } catch (error) {
    console.error('❌ Error testing API:', error.message);
    console.log('\n💡 Make sure:');
    console.log('• Dashboard is running on localhost:3000');
    console.log('• API key is correct');
    console.log('• Database has some test data');
  }
}

// Run the test
testAttackListAPI();