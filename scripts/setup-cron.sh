#!/bin/bash

# Instagram Follow-up Cron Setup Script
# This script sets up cron jobs for processing Instagram follow-ups

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Setting up Instagram Follow-up Cron Jobs${NC}"

# Check if required environment variables are set
if [ -z "$CRON_SECRET" ]; then
    echo -e "${RED}Error: CRON_SECRET environment variable is not set${NC}"
    echo "Please set CRON_SECRET in your environment or .env file"
    exit 1
fi

if [ -z "$APP_URL" ]; then
    echo -e "${RED}Error: APP_URL environment variable is not set${NC}"
    echo "Please set APP_URL to your application URL (e.g., https://yourdomain.com)"
    exit 1
fi

# Get current user
CURRENT_USER=$(whoami)
echo -e "${YELLOW}Setting up cron jobs for user: $CURRENT_USER${NC}"

# Create a temporary cron file
TEMP_CRON_FILE="/tmp/instagram_cron_jobs"

# Get existing cron jobs (excluding our Instagram jobs)
crontab -l 2>/dev/null | grep -v "# Instagram Follow-up Jobs" | grep -v "/api/cron/instagram-followups" | grep -v "/api/cron/cleanup-old-followups" | grep -v "/api/cron/process-follower-queue" > "$TEMP_CRON_FILE"

# Add our cron jobs
cat >> "$TEMP_CRON_FILE" << EOF

# Instagram Follow-up Jobs
# Process follow-ups every minute
* * * * * curl -s -H "Authorization: Bearer $CRON_SECRET" "$APP_URL/api/cron/instagram-followups" >/dev/null 2>&1

# Process follower queue every 5 minutes
*/5 * * * * curl -s -H "Authorization: Bearer $CRON_SECRET" "$APP_URL/api/cron/process-follower-queue" >/dev/null 2>&1

# Clean up old follow-ups daily at 2 AM
0 2 * * * curl -s -H "Authorization: Bearer $CRON_SECRET" "$APP_URL/api/cron/cleanup-old-followups" >/dev/null 2>&1
EOF

# Install the new cron jobs
crontab "$TEMP_CRON_FILE"

# Clean up
rm "$TEMP_CRON_FILE"

echo -e "${GREEN}Cron jobs installed successfully!${NC}"
echo ""
echo -e "${YELLOW}Installed jobs:${NC}"
echo "1. Process follow-ups: Every minute"
echo "2. Cleanup old follow-ups: Daily at 2 AM"
echo ""
echo -e "${YELLOW}To view current cron jobs:${NC}"
echo "crontab -l"
echo ""
echo -e "${YELLOW}To remove Instagram cron jobs:${NC}"
echo "crontab -l | grep -v 'Instagram Follow-up Jobs' | grep -v '/api/cron/instagram-followups' | grep -v '/api/cron/cleanup-old-followups' | crontab -"
echo ""
echo -e "${GREEN}Setup complete!${NC}"
