/**
 * <PERSON><PERSON><PERSON> to create test data for Attack List functionality
 * This creates InstagramContacts with different priorities and timing
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestAttackListData() {
  try {
    console.log('🚀 Creating test data for Attack List...');

    // Get the first organization (you may need to adjust this)
    const organization = await prisma.organization.findFirst({
      include: {
        memberships: {
          include: {
            user: true
          }
        }
      }
    });

    if (!organization) {
      console.error('❌ No organization found. Please create an organization first.');
      return;
    }

    const userId = organization.memberships[0]?.userId;
    if (!userId) {
      console.error('❌ No user found in organization.');
      return;
    }

    console.log(`📋 Using organization: ${organization.name} (${organization.id})`);
    console.log(`👤 Using user: ${userId}`);

    // Test data with different priorities and timing scenarios
    const testContacts = [
      // Priority 5 - Highly Engaged (ready now)
      {
        instagramNickname: 'highly_engaged_user1',
        instagramId: '1234567890123456', // Numeric Instagram ID
        priority: 5,
        stage: 'engaged',
        attackListStatus: 'ready',
        nextMessageAt: null, // Ready now
        lastInteractionAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      },
      {
        instagramNickname: 'highly_engaged_user2',
        instagramId: '1234567890123457', // Numeric Instagram ID
        priority: 5,
        stage: 'engaged',
        attackListStatus: 'ready',
        nextMessageAt: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago (overdue)
        lastInteractionAt: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      },

      // Priority 4 - Engaged (ready now)
      {
        instagramNickname: 'engaged_user1',
        instagramId: '2234567890123456', // Numeric Instagram ID
        priority: 4,
        stage: 'initial',
        attackListStatus: 'ready',
        nextMessageAt: null, // Ready now
        lastInteractionAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      },
      {
        instagramNickname: 'engaged_user2',
        instagramId: '2234567890123457', // Numeric Instagram ID
        priority: 4,
        stage: 'engaged',
        attackListStatus: 'pending',
        nextMessageAt: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
        lastInteractionAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
      },

      // Priority 3 - New Followers (ready now)
      {
        instagramNickname: 'new_follower1',
        instagramId: '3234567890123456', // Numeric Instagram ID
        priority: 3,
        stage: 'new',
        attackListStatus: 'ready',
        nextMessageAt: null, // Ready now
        lastInteractionAt: null, // Never interacted
      },
      {
        instagramNickname: 'new_follower2',
        instagramId: '3234567890123457', // Numeric Instagram ID
        priority: 3,
        stage: 'new',
        attackListStatus: 'ready',
        nextMessageAt: null, // Ready now
        lastInteractionAt: null, // Never interacted
      },
      {
        instagramNickname: 'new_follower3',
        instagramId: '3234567890123458', // Numeric Instagram ID
        priority: 3,
        stage: 'new',
        attackListStatus: 'pending',
        nextMessageAt: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
        lastInteractionAt: null, // Never interacted
      },

      // Priority 2 - Low Engaged (some ready, some not)
      {
        instagramNickname: 'low_engaged_user1',
        instagramId: '4234567890123456', // Numeric Instagram ID
        priority: 2,
        stage: 'initial',
        attackListStatus: 'ready',
        nextMessageAt: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago (ready)
        lastInteractionAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
      },
      {
        instagramNickname: 'low_engaged_user2',
        instagramId: '4234567890123457', // Numeric Instagram ID
        priority: 2,
        stage: 'initial',
        attackListStatus: 'pending',
        nextMessageAt: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours from now
        lastInteractionAt: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
      },

      // Priority 1 - Follow-up (24h+ delay)
      {
        instagramNickname: 'followup_user1',
        instagramId: '5234567890123456', // Numeric Instagram ID
        priority: 1,
        stage: 'initial',
        attackListStatus: 'ready',
        nextMessageAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago (ready)
        lastInteractionAt: new Date(Date.now() - 25 * 60 * 60 * 1000), // 25 hours ago
      },
      {
        instagramNickname: 'followup_user2',
        instagramId: '5234567890123457', // Numeric Instagram ID
        priority: 1,
        stage: 'initial',
        attackListStatus: 'pending',
        nextMessageAt: new Date(Date.now() + 6 * 60 * 60 * 1000), // 6 hours from now
        lastInteractionAt: new Date(Date.now() - 20 * 60 * 60 * 1000), // 20 hours ago
      },
    ];

    // Create the test contacts
    for (const contactData of testContacts) {
      try {
        const contact = await prisma.instagramContact.create({
          data: {
            userId,
            organizationId: organization.id,
            ...contactData,
            conversationSource: 'extension',
            avatar: `https://i.pravatar.cc/150?u=${contactData.instagramNickname}`,
          }
        });

        console.log(`✅ Created contact: ${contact.instagramNickname} (Priority: ${contact.priority}, Status: ${contact.attackListStatus})`);
      } catch (error) {
        console.error(`❌ Error creating contact ${contactData.instagramNickname}:`, error.message);
      }
    }

    console.log('\n📊 Test data summary:');
    console.log('- Priority 5 (Highly Engaged): 2 contacts');
    console.log('- Priority 4 (Engaged): 2 contacts');
    console.log('- Priority 3 (New Followers): 3 contacts');
    console.log('- Priority 2 (Low Engaged): 2 contacts');
    console.log('- Priority 1 (Follow-up): 2 contacts');
    console.log('\n🎯 Attack List should show contacts sorted by:');
    console.log('1. Time (nextMessageAt <= now)');
    console.log('2. Priority (5→4→3→2→1)');
    console.log('3. Creation time');

  } catch (error) {
    console.error('❌ Error creating test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
createTestAttackListData();
