#!/usr/bin/env tsx

/**
 * Test script for 5-day interval scraping functionality
 * Usage: tsx scripts/test-5day-interval.ts
 */

const API_KEY = 'api_189dc9010290e48b264fc08943820696';
const BASE_URL = 'http://localhost:3000';

interface ScrapingEligibilityResponse {
  success: boolean;
  data: {
    isEligible: boolean;
    nextAllowedAt: string | null;
    waitTimeMs: number;
    waitTimeHuman: string;
    intervalDays: number;
    lastScrapingSession: string | null;
    totalFollowersScraped: number;
    extensionStatus: string;
    allFollowersScraped: boolean;
  };
}

interface StatusResponse {
  success: boolean;
  data: {
    extensionStatus: string;
    isScrapingEligible: boolean;
    nextScrapingAllowedAt: string | null;
    waitTimeMs: number;
    waitTimeHuman: string;
    scrapingIntervalDays: number;
    totalFollowersScraped: number;
    lastScrapingSession: string | null;
    // ... other fields
  };
}

async function makeRequest(endpoint: string, options: RequestInit = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    'X-API-Key': API_KEY,
    ...options.headers,
  };

  console.log(`🔗 Making request to: ${url}`);
  
  try {
    const response = await fetch(url, { ...options, headers });
    const data = await response.json();
    
    console.log(`📊 Response (${response.status}):`, JSON.stringify(data, null, 2));
    return { response, data };
  } catch (error) {
    console.error(`❌ Request failed:`, error);
    throw error;
  }
}

async function testScrapingEligibility() {
  console.log('\n🧪 Testing Scraping Eligibility Endpoint');
  console.log('=' .repeat(50));
  
  const { data } = await makeRequest('/api/chrome-extension/scraping-eligibility');
  
  if (data.success) {
    const eligibility = data.data;
    console.log(`✅ Scraping Eligible: ${eligibility.isEligible}`);
    console.log(`📊 Total Scraped: ${eligibility.totalFollowersScraped}`);
    console.log(`📅 Last Session: ${eligibility.lastScrapingSession || 'Never'}`);
    console.log(`⏰ Next Allowed: ${eligibility.nextAllowedAt || 'Now'}`);
    console.log(`⏳ Wait Time: ${eligibility.waitTimeHuman || 'None'}`);
    console.log(`🔄 Interval: ${eligibility.intervalDays} days`);
  }
  
  return data;
}

async function testStatusEndpoint() {
  console.log('\n🧪 Testing Status Endpoint');
  console.log('=' .repeat(50));

  const { data } = await makeRequest('/api/chrome-extension/status');

  if (data.success) {
    const status = data.data;
    console.log(`📱 Extension Status: ${status.extensionStatus}`);
    console.log(`✅ Scraping Eligible: ${status.isScrapingEligible}`);
    console.log(`📊 Total Scraped: ${status.totalFollowersScraped}`);
    console.log(`⏰ Next Allowed: ${status.nextScrapingAllowedAt || 'Now'}`);
    console.log(`⏳ Wait Time: ${status.waitTimeHuman || 'None'}`);
  }

  return data;
}

async function testAdminStatusEndpoint() {
  console.log('\n🧪 Testing Admin Status Endpoint');
  console.log('=' .repeat(50));

  const { data } = await makeRequest('/api/admin/reset-scraping-interval');

  if (data.success) {
    const status = data.data;
    console.log(`✅ Scraping Eligible: ${status.isEligible}`);
    console.log(`📊 Total Scraped: ${status.totalFollowersScraped}`);
    console.log(`⏰ Next Allowed: ${status.nextScrapingAllowedAt || 'Now'}`);
    console.log(`⏳ Wait Time: ${status.waitTimeHuman || 'None'}`);
    console.log(`🔄 Can Reset: ${status.canReset}`);
  }

  return data;
}

async function testScrapingStatusEndpoint() {
  console.log('\n🧪 Testing Chrome Extension Scraping Status Endpoint');
  console.log('=' .repeat(50));

  const { data } = await makeRequest('/api/chrome-extension/scraping-status');

  if (data.success) {
    const status = data.data;
    console.log(`✅ Can Scrape: ${status.canScrape}`);
    console.log(`📊 Total Scraped: ${status.totalFollowersScraped}`);
    console.log(`⏰ Next Allowed: ${status.nextScrapingAllowedAt || 'Now'}`);
    console.log(`⏳ Wait Time: ${status.waitTimeHuman || 'None'}`);
    console.log(`📱 Extension Status: ${status.extensionStatus}`);
    console.log(`💬 Status Message: ${status.statusMessage}`);
    console.log(`🎯 Recommended Action: ${status.recommendedAction}`);
  }

  return data;
}

async function simulateFollowerUpload(count: number = 250) {
  console.log(`\n🧪 Simulating Upload of ${count} Followers`);
  console.log('=' .repeat(50));
  
  // Generate fake followers
  const followers = Array.from({ length: count }, (_, i) => ({
    instagramNickname: `test_user_${Date.now()}_${i}`,
    instagramId: `${1000000000 + i}`,
    avatar: `https://example.com/avatar${i}.jpg`,
    followerCount: Math.floor(Math.random() * 10000),
    isVerified: Math.random() > 0.9
  }));

  const { data } = await makeRequest('/api/chrome-extension/process-followers', {
    method: 'POST',
    body: JSON.stringify({
      followers,
      startPosition: 0,
      totalFollowers: count,
      isComplete: false
    })
  });
  
  if (data.success) {
    console.log(`✅ Successfully uploaded ${count} followers`);
    console.log(`📊 Results:`, data.data);
  }
  
  return data;
}

async function resetForTesting() {
  console.log('\n🧪 Resetting for Testing (Admin Endpoint)');
  console.log('=' .repeat(50));

  const { data } = await makeRequest('/api/admin/reset-scraping-interval', {
    method: 'POST'
  });

  if (data.success) {
    console.log(`✅ ${data.message}`);
    console.log(`📊 Previous state:`, data.data.previousState);
    console.log(`📊 New state:`, data.data.newState);
  }

  return data;
}

async function simulateWait() {
  console.log('\n🧪 Simulating 5-Day Wait');
  console.log('=' .repeat(50));
  
  const { data } = await makeRequest('/api/test/scraping-interval', {
    method: 'POST',
    body: JSON.stringify({ action: 'simulate_wait' })
  });
  
  if (data.success) {
    console.log(`✅ ${data.message}`);
  }
  
  return data;
}

async function runFullTest() {
  console.log('🚀 Starting 5-Day Interval Scraping Test');
  console.log('=' .repeat(60));
  
  try {
    // Test 1: Check initial eligibility
    console.log('\n📋 Test 1: Initial State');
    await testScrapingEligibility();
    await testStatusEndpoint();
    await testAdminStatusEndpoint();
    await testScrapingStatusEndpoint();
    
    // Test 2: Reset to fresh state
    console.log('\n📋 Test 2: Reset to Fresh State');
    await resetForTesting();
    await testScrapingEligibility();
    
    // Test 3: Upload 250 followers (should trigger 5-day wait)
    console.log('\n📋 Test 3: Upload 250 Followers');
    await simulateFollowerUpload(250);
    await testScrapingEligibility();
    await testStatusEndpoint();
    await testScrapingStatusEndpoint();
    
    // Test 4: Try to check eligibility during wait period
    console.log('\n📋 Test 4: Check During Wait Period');
    await testScrapingEligibility();
    
    // Test 5: Simulate wait and check again
    console.log('\n📋 Test 5: Simulate Wait Period Completion');
    await simulateWait();
    await testScrapingEligibility();
    
    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  }
}

// Run the test
runFullTest();
