#!/usr/bin/env tsx

/**
 * Debug script to check API key format and validity
 */

import { config } from 'dotenv';
import { join } from 'path';

// Load environment variables from the Instagram bot package
config({ path: join(__dirname, '../packages/instagram-bot/.env') });

async function debugApiKey() {
  console.log('🔍 Debugging API Key...\n');

  const apiKey = process.env.ANTHROPIC_API_KEY;
  
  if (!apiKey) {
    console.error('❌ ANTHROPIC_API_KEY not found in environment');
    return;
  }

  console.log('📋 API Key Analysis:');
  console.log('====================');
  console.log(`✅ Length: ${apiKey.length} characters`);
  console.log(`✅ Starts with: ${apiKey.substring(0, 15)}...`);
  console.log(`✅ Ends with: ...${apiKey.substring(apiKey.length - 10)}`);
  console.log(`✅ Contains spaces: ${apiKey.includes(' ')}`);
  console.log(`✅ Contains newlines: ${apiKey.includes('\n')}`);
  console.log(`✅ Contains tabs: ${apiKey.includes('\t')}`);
  console.log(`✅ Trimmed length: ${apiKey.trim().length}`);
  
  // Check if it matches the expected format
  const expectedFormat = /^sk-ant-api03-[A-Za-z0-9_-]+$/;
  console.log(`✅ Matches expected format: ${expectedFormat.test(apiKey.trim())}`);
  
  // Show the exact key being used (be careful with this in production!)
  console.log('\n🔑 Exact API Key:');
  console.log('=================');
  console.log(`"${apiKey}"`);
  
  // Check for common issues
  console.log('\n🔧 Common Issues Check:');
  console.log('=======================');
  
  if (apiKey !== apiKey.trim()) {
    console.log('⚠️  API key has leading/trailing whitespace');
  } else {
    console.log('✅ No leading/trailing whitespace');
  }
  
  if (apiKey.includes('\n') || apiKey.includes('\r')) {
    console.log('⚠️  API key contains line breaks');
  } else {
    console.log('✅ No line breaks in API key');
  }
  
  if (apiKey.length !== 108) {
    console.log(`⚠️  API key length is ${apiKey.length}, expected around 108 characters`);
  } else {
    console.log('✅ API key length looks correct');
  }
}

// Run the debug
if (require.main === module) {
  debugApiKey().catch(console.error);
}

export { debugApiKey };
