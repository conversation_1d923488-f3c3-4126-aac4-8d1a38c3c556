#!/usr/bin/env tsx

/**
 * Simple Claude API test to verify authentication and model availability
 */

import { config } from 'dotenv';
import { join } from 'path';
import axios from 'axios';

// Load environment variables from the Instagram bot package
config({ path: join(__dirname, '../packages/instagram-bot/.env') });

async function testClaudeSimple() {
  console.log('🧪 Testing Claude API Authentication...\n');

  const apiKey = process.env.ANTHROPIC_API_KEY;

  if (!apiKey) {
    console.error('❌ ANTHROPIC_API_KEY not found');
    process.exit(1);
  }

  console.log(`✅ API Key loaded: ${apiKey.substring(0, 20)}...`);
  console.log(`✅ API Key length: ${apiKey.length} characters\n`);

  // Test with different models to find what works
  const testModels = [
    'claude-sonnet-4-20250514'    // Claude 4 Sonnet
  ];

  for (const model of testModels) {
    console.log(`🧪 Testing model: ${model}`);
    console.log('================================');

    try {
      const requestBody = {
        model: model,
        max_tokens: 100,
        messages: [
          {
            role: 'user',
            content: 'Hello! Please respond with a simple JSON object containing just {"status": "success", "message": "Hello from Claude!"}'
          }
        ]
      };

      const headers = {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      };

      console.log('📤 Making request...');

      const response = await axios.post(
        'https://api.anthropic.com/v1/messages',
        requestBody,
        { headers, timeout: 30000 }
      );

      console.log('✅ SUCCESS!');
      console.log(`📥 Response status: ${response.status}`);
      console.log('📥 Response data:');
      console.log(JSON.stringify(response.data, null, 2));
      console.log('\n');

      // If we get here, this model works
      break;

    } catch (error) {
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const errorData = error.response?.data;

        console.log(`❌ FAILED with status ${status}`);
        console.log('📥 Error response:');
        console.log(JSON.stringify(errorData, null, 2));

        if (status === 401) {
          console.log('🔑 Authentication failed - API key might be invalid');
          break; // No point testing other models if auth fails
        } else if (status === 400) {
          console.log('📝 Bad request - model might not exist or request format issue');
        } else if (status === 404) {
          console.log('🔍 Model not found');
        }
      } else {
        console.log(`❌ Network error: ${error}`);
      }
      console.log('\n');
    }
  }
}

// Run the test
if (require.main === module) {
  testClaudeSimple().catch(console.error);
}

export { testClaudeSimple };
