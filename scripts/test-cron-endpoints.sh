#!/bin/bash

# Test script for Instagram Follow-up Cron Endpoints
# This script tests the cron endpoints to ensure they're working correctly

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Testing Instagram Follow-up Cron Endpoints${NC}"
echo "=============================================="

# Check if required environment variables are set
if [ -z "$CRON_SECRET" ]; then
    echo -e "${RED}Error: CRON_SECRET environment variable is not set${NC}"
    exit 1
fi

if [ -z "$APP_URL" ]; then
    echo -e "${RED}Error: APP_URL environment variable is not set${NC}"
    echo "Please set APP_URL to your application URL (e.g., https://yourdomain.com)"
    exit 1
fi

echo -e "${YELLOW}Using APP_URL: $APP_URL${NC}"
echo -e "${YELLOW}Using CRON_SECRET: ${CRON_SECRET:0:8}...${NC}"
echo ""

# Test 1: Process Follow-ups Endpoint
echo -e "${BLUE}Test 1: Testing follow-up processing endpoint${NC}"
echo "Endpoint: $APP_URL/api/cron/instagram-followups"

response=$(curl -s -w "\n%{http_code}" -H "Authorization: Bearer $CRON_SECRET" "$APP_URL/api/cron/instagram-followups")
http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | head -n -1)

if [ "$http_code" = "200" ]; then
    echo -e "${GREEN}✓ Follow-up processing endpoint is working${NC}"
    echo "Response: $body"
else
    echo -e "${RED}✗ Follow-up processing endpoint failed (HTTP $http_code)${NC}"
    echo "Response: $body"
fi

echo ""

# Test 2: Cleanup Old Follow-ups Endpoint
echo -e "${BLUE}Test 2: Testing cleanup endpoint${NC}"
echo "Endpoint: $APP_URL/api/cron/cleanup-old-followups"

response=$(curl -s -w "\n%{http_code}" -H "Authorization: Bearer $CRON_SECRET" "$APP_URL/api/cron/cleanup-old-followups")
http_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | head -n -1)

if [ "$http_code" = "200" ]; then
    echo -e "${GREEN}✓ Cleanup endpoint is working${NC}"
    echo "Response: $body"
else
    echo -e "${RED}✗ Cleanup endpoint failed (HTTP $http_code)${NC}"
    echo "Response: $body"
fi

echo ""

# Test 3: Test unauthorized access
echo -e "${BLUE}Test 3: Testing unauthorized access (should fail)${NC}"

response=$(curl -s -w "\n%{http_code}" -H "Authorization: Bearer invalid-secret" "$APP_URL/api/cron/instagram-followups")
http_code=$(echo "$response" | tail -n1)

if [ "$http_code" = "401" ]; then
    echo -e "${GREEN}✓ Unauthorized access properly rejected${NC}"
else
    echo -e "${RED}✗ Unauthorized access not properly rejected (HTTP $http_code)${NC}"
fi

echo ""

# Test 4: Check if cron jobs are installed
echo -e "${BLUE}Test 4: Checking installed cron jobs${NC}"

cron_jobs=$(crontab -l 2>/dev/null | grep -E "(instagram-followups|cleanup-old-followups)")

if [ -n "$cron_jobs" ]; then
    echo -e "${GREEN}✓ Instagram cron jobs are installed:${NC}"
    echo "$cron_jobs"
else
    echo -e "${YELLOW}⚠ No Instagram cron jobs found${NC}"
    echo "Run './scripts/setup-cron.sh' to install them"
fi

echo ""
echo -e "${BLUE}Test Summary${NC}"
echo "============"

if [ "$http_code" = "200" ]; then
    echo -e "${GREEN}✓ All endpoints are accessible${NC}"
    echo -e "${GREEN}✓ Your 24-hour follow-up system is ready!${NC}"
else
    echo -e "${RED}✗ Some endpoints failed${NC}"
    echo -e "${YELLOW}Please check your server configuration and environment variables${NC}"
fi

echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Ensure your application is running"
echo "2. Run './scripts/setup-cron.sh' if cron jobs aren't installed"
echo "3. Monitor logs to ensure follow-ups are being processed"
echo "4. Test the 24-hour window functionality with real Instagram messages"
