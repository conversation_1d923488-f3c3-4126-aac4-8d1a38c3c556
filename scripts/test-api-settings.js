const fetch = require('node-fetch');

const API_KEY = 'api_782ef78a245f7e67579e53370041ea71';
const BASE_URL = 'https://ai-setter.com';

async function testApiSettings() {
  try {
    console.log('🔧 Testing API settings endpoint...');
    
    const response = await fetch(`${BASE_URL}/api/chrome-extension/settings`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', response.status, errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ API Settings Response:');
    console.log(JSON.stringify(data, null, 2));

    if (data.success && data.data) {
      console.log('\n📋 Current API Settings:');
      console.log('- Time Between DMs (min):', data.data.timeBetweenDMsMin, 'minutes');
      console.log('- Time Between DMs (max):', data.data.timeBetweenDMsMax, 'minutes');
      console.log('- Messages Before Break (min):', data.data.messagesBeforeBreakMin);
      console.log('- Messages Before Break (max):', data.data.messagesBeforeBreakMax);
      console.log('- Break Duration (min):', data.data.breakDurationMin, 'minutes');
      console.log('- Break Duration (max):', data.data.breakDurationMax, 'minutes');
      console.log('- Pause Start:', data.data.pauseStart);
      console.log('- Pause Stop:', data.data.pauseStop);
      console.log('- Smart Focus:', data.data.smartFocus);
      
      // Check for any additional settings
      console.log('\n🔍 All Available Settings:');
      Object.keys(data.data).forEach(key => {
        console.log(`- ${key}:`, data.data[key]);
      });
    }

  } catch (error) {
    console.error('❌ Error testing API settings:', error);
  }
}

testApiSettings();