# Chrome Extension 5-Day Interval Scraping Implementation

## Overview

The system now enforces a 5-day waiting period between follower scraping sessions. After scraping 250 followers, the extension must wait 5 days before scraping the next batch.

## Database Changes

### New Fields in `ChromeExtensionSettings`

```sql
-- 5-day interval scraping controls
scrapingIntervalDays   Int          @default(5) -- Days to wait between scraping sessions
nextScrapingAllowedAt  DateTime?    -- When the next scraping session is allowed
```

## API Endpoints

### 1. Check Scraping Eligibility
```
GET /api/chrome-extension/scraping-eligibility
```

**Response:**
```json
{
  "success": true,
  "data": {
    "isEligible": true,
    "nextAllowedAt": null,
    "waitTimeMs": 0,
    "waitTimeHuman": "",
    "intervalDays": 5,
    "lastScrapingSession": "2024-12-07T14:00:00Z",
    "totalFollowersScraped": 250,
    "extensionStatus": "SCRAPED_250",
    "allFollowersScraped": false
  }
}
```

**When Not Eligible:**
```json
{
  "success": true,
  "data": {
    "isEligible": false,
    "nextAllowedAt": "2024-12-12T14:00:00Z",
    "waitTimeMs": 432000000,
    "waitTimeHuman": "5 days, 0 hours",
    "intervalDays": 5,
    "lastScrapingSession": "2024-12-07T14:00:00Z",
    "totalFollowersScraped": 250,
    "extensionStatus": "SCRAPED_250",
    "allFollowersScraped": false
  }
}
```

### 2. Enhanced Status Endpoint
```
GET /api/chrome-extension/status
```

Now includes scraping eligibility information:
```json
{
  "success": true,
  "data": {
    "extensionStatus": "SCRAPED_250",
    "isScrapingEligible": false,
    "nextScrapingAllowedAt": "2024-12-12T14:00:00Z",
    "waitTimeMs": 432000000,
    "waitTimeHuman": "5 days, 0 hours",
    "scrapingIntervalDays": 5,
    // ... other existing fields
  }
}
```

### 3. Process Followers (Updated)
```
POST /api/chrome-extension/process-followers
```

- **No longer blocks uploads** - followers can always be uploaded
- **Sets 5-day wait automatically** when 250 followers milestone is reached
- **Calculates next allowed scraping time** and stores in database

## Chrome Extension Workflow

### 1. Before Starting Scraping

```javascript
// Check if scraping is allowed
const response = await fetch('/api/chrome-extension/scraping-eligibility', {
  headers: { 'X-API-Key': apiKey }
});

const { data } = await response.json();

if (!data.isEligible) {
  // Show wait message to user
  showWaitMessage(data.waitTimeHuman, data.nextAllowedAt);
  return; // Don't start scraping
}

// Proceed with scraping
startScraping();
```

### 2. During Scraping

```javascript
// Normal scraping process - upload followers as usual
const followers = await scrapeFollowers(250);

const uploadResponse = await fetch('/api/chrome-extension/process-followers', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'X-API-Key': apiKey 
  },
  body: JSON.stringify({
    followers,
    startPosition: currentPosition,
    isComplete: false
  })
});

// Check if 5-day wait was triggered
const statusResponse = await fetch('/api/chrome-extension/status', {
  headers: { 'X-API-Key': apiKey }
});

const { data: status } = await statusResponse.json();

if (!status.isScrapingEligible) {
  // 250 followers completed - show wait message
  showCompletionMessage(status.waitTimeHuman, status.nextAllowedAt);
}
```

### 3. User Interface Updates

#### Before Scraping
```javascript
function checkScrapingEligibility() {
  // Call scraping-eligibility endpoint
  // Update UI based on response
  
  if (isEligible) {
    enableScrapingButton();
    showMessage("Ready to scrape 250 followers");
  } else {
    disableScrapingButton();
    showCountdown(waitTimeMs, nextAllowedAt);
  }
}
```

#### During Wait Period
```javascript
function showWaitMessage(waitTimeHuman, nextAllowedAt) {
  const message = `
    ⏳ Scraping completed! 
    
    Next scraping session available in: ${waitTimeHuman}
    
    Next allowed: ${new Date(nextAllowedAt).toLocaleString()}
    
    This 5-day interval helps maintain account safety.
  `;
  
  updateExtensionUI(message);
  startCountdownTimer(nextAllowedAt);
}
```

#### Countdown Timer
```javascript
function startCountdownTimer(nextAllowedAt) {
  const timer = setInterval(() => {
    const now = new Date();
    const target = new Date(nextAllowedAt);
    const diff = target - now;
    
    if (diff <= 0) {
      clearInterval(timer);
      enableScrapingButton();
      showMessage("✅ Ready to scrape next 250 followers!");
      return;
    }
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    updateCountdownDisplay(`${days}d ${hours}h ${minutes}m`);
  }, 60000); // Update every minute
}
```

## Testing

### Test Endpoint
```
GET /api/test/scraping-interval
POST /api/test/scraping-interval
```

**Reset for testing:**
```javascript
fetch('/api/test/scraping-interval', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ action: 'reset' })
});
```

**Simulate 5-day wait:**
```javascript
fetch('/api/test/scraping-interval', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ action: 'simulate_wait' })
});
```

## Key Implementation Points

1. **No Upload Blocking**: The 5-day rule only applies to starting new scraping sessions, not uploading scraped followers
2. **Automatic Timing**: The system automatically sets the 5-day wait when 250 followers are reached
3. **Configurable Interval**: The interval is configurable via `scrapingIntervalDays` (default: 5)
4. **Override for Testing**: When `allFollowersScraped` is true, scraping is always allowed
5. **Human-Readable Times**: Wait times are converted to user-friendly format
6. **Real-time Updates**: Extension should check eligibility before each scraping session

## Chrome Extension Modifications Required

1. **Add eligibility check** before starting scraping
2. **Update UI** to show wait times and countdowns
3. **Handle completion messages** when 250 followers are reached
4. **Implement countdown timer** during wait periods
5. **Add manual refresh** to check if wait period has ended
6. **Update settings page** to show next allowed scraping time
