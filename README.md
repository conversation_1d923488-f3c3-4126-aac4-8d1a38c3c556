# AISetter

AISetter is an Instagram DM bot platform that automates conversations with leads to book appointments. The platform includes contact management, conversation history, prompt management, and integration with Instagram's API.

## Next.js App Router

This application uses Next.js App Router for improved performance and server-side rendering capabilities. The codebase has been fully migrated from Pages Router to App Router.

## Deployment

The application is automatically deployed to the production server when changes are pushed to the main branch. The deployment process is handled by GitHub Actions.

## Features

- **Instagram Integration**: Handle webhook events, retrieve conversations, access user profiles, and send messages through Instagram's API
- **AI-Powered Responses**: Uses Claude 3.7 Sonnet API for intelligent conversation handling
- **Contact Management**: Organize and manage Instagram contacts
- **Prompt Management**: Create and customize bot conversation styles
- **Automated Follow-ups**: Schedule and send follow-up messages

## Project Structure

This project uses a monorepo structure with pnpm as the package manager:

- `apps/dashboard`: Main dashboard application
- `packages/instagram`: Instagram API integration
- `packages/instagram-bot`: Bot logic for handling conversations
- `packages/database`: Database models and migrations

## Getting Started

1. Install dependencies:
   ```bash
   pnpm install
   ```

2. Set up environment variables:
   ```bash
   cp apps/dashboard/.env.example apps/dashboard/.env
   cp packages/database/.env.example packages/database/.env
   ```

3. Start the development server:
   ```bash
   pnpm --filter dashboard dev
   ```

4. Navigate to http://localhost:3000

## Development Environment

To show all TypeScript errors during development, you can use the following environment variable:

```bash
__NEXT_TEST_MODE=true pnpm --filter dashboard dev
```

This will display all TypeScript errors at once, making it easier to fix them.

## Deployment

This project uses GitHub Actions for continuous deployment:

1. Changes pushed to the main branch trigger the deployment workflow
2. The workflow builds the application and deploys it to the production server
3. The application is accessible at https://app.aisetter.pl


