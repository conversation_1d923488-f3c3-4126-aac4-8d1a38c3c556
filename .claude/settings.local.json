{"permissions": {"allow": ["Bash(pnpm:*)", "Bash(grep:*)", "<PERSON><PERSON>(chmod:*)", "Bash(npm run build:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(ls:*)", "Bash(find:*)", "mcp__gemini-collab__ask_gemini", "mcp__gemini-collab__gemini_brainstorm", "Bash(git add:*)", "Bash(git checkout:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebSearch", "WebFetch(domain:developers.facebook.com)", "Bash(prisma db push:*)"], "deny": [], "defaultMode": "acceptEdits"}}