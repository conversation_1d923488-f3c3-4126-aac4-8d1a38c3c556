{"name": "marketing", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "next dev --port 3001 --turbo", "build": "content-collections build && next build", "build:content": "content-collections build", "start": "next start --port 3001", "analyze": "BUNDLE_ANALYZE=both next build", "clean": "git clean -xdf .cache .content-collections .next .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "content-collections build && tsc --noEmit", "postinstall": "content-collections build"}, "dependencies": {"@hookform/resolvers": "4.1.3", "@t3-oss/env-nextjs": "0.12.0", "@tanstack/react-table": "8.21.2", "@workspace/analytics": "workspace:*", "@workspace/common": "workspace:*", "@workspace/routes": "workspace:*", "@workspace/ui": "workspace:*", "date-fns": "3.6.0", "exceljs": "4.4.0", "framer-motion": "12.4.10", "lucide-react": "0.477.0", "mdast-util-toc": "7.1.0", "next": "15.2.1", "next-secure-headers": "2.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "7.54.2", "react-is": "19.0.0", "recharts": "2.15.1", "server-only": "0.0.1", "sharp": "0.33.5", "unist-util-visit": "5.0.0", "vaul": "1.1.2", "vfile": "6.0.3", "zod": "3.24.2"}, "devDependencies": {"@content-collections/cli": "0.1.6", "@content-collections/core": "0.8.0", "@content-collections/mdx": "0.2.0", "@content-collections/next": "0.2.5", "@next/bundle-analyzer": "15.2.1", "@svgr/webpack": "8.1.0", "@types/mdast": "4.0.4", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/unist": "3.0.3", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/tailwind-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "autoprefixer": "10.4.20", "mdx-bundler": "10.1.0", "postcss": "8.5.3", "rehype": "13.0.2", "rehype-autolink-headings": "7.1.0", "rehype-pretty-code": "0.14.0", "rehype-slug": "6.0.0", "remark": "15.0.1", "remark-code-import": "1.2.0", "remark-gfm": "4.0.1", "shiki": "1.24.3", "tailwindcss": "3.4.17", "tailwindcss-animate": "1.0.7"}, "prettier": "@workspace/prettier-config"}