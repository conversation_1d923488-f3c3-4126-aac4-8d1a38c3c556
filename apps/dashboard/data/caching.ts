export enum UserCacheKey {
  Organizations,
  Profile,
  PersonalDetails,
  Preferences,
  MultiFactorAuthentication,
  Sessions,
  TransactionalEmails,
  MarketingEmails
}

export enum OrganizationCacheKey {
  OrganizationLogo,
  OrganizationDetails,
  BusinessHours,
  SocialMedia,
  Members,
  Invitations,
  ApiKeys,
  Webhooks,
  InstagramContacts,
  InstagramContact,
  InstagramMessages,
  InstagramSettings,
  PromptConfig,
  BotStyles,
  AdminPrompt,
  AdminSettings,
  InstagramStatistics
}

export class Caching {
  private static readonly USER_PREFIX = 'user';
  private static readonly ORGANIZATION_PREFIX = 'organization';

  private static joinKeyParts(...parts: string[]): string[] {
    return parts.filter((part) => part.length > 0);
  }

  private static joinTagParts(...parts: string[]): string {
    return parts.filter((part) => part.length > 0).join(':');
  }

  public static createUserKeyParts(
    key: UserCacheKey,
    userId: string,
    ...additionalKeyParts: string[]
  ): string[] {
    if (!userId) {
      throw new Error('User ID cannot be empty');
    }
    return this.joinKeyParts(
      this.USER_PREFIX,
      userId,
      UserCacheKey[key].toLowerCase(),
      ...additionalKeyParts
    );
  }

  public static createUserTag(
    key: UserCacheKey,
    userId: string,
    ...additionalTagParts: string[]
  ): string {
    if (!userId) {
      throw new Error('User ID cannot be empty');
    }
    return this.joinTagParts(
      this.USER_PREFIX,
      userId,
      UserCacheKey[key].toLowerCase(),
      ...additionalTagParts
    );
  }

  public static createOrganizationKeyParts(
    key: OrganizationCacheKey,
    organizationId?: string,
    ...additionalKeyParts: string[]
  ): string[] {
    const orgId = organizationId || 'global';
    return this.joinKeyParts(
      this.ORGANIZATION_PREFIX,
      orgId,
      OrganizationCacheKey[key].toLowerCase(),
      ...additionalKeyParts
    );
  }

  public static createOrganizationTag(
    key: OrganizationCacheKey,
    organizationId?: string,
    ...additionalTagParts: string[]
  ): string {
    const orgId = organizationId || 'global';
    return this.joinTagParts(
      this.ORGANIZATION_PREFIX,
      orgId,
      OrganizationCacheKey[key].toLowerCase(),
      ...additionalTagParts
    );
  }
}

export const defaultRevalidateTimeInSeconds =
  process.env.NODE_ENV === 'production' ? 3600 : 120;
