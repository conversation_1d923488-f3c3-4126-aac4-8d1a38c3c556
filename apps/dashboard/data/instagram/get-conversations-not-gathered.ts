import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';

interface GetConversationsNotGatheredParams {
  page?: number;
  pageSize?: number;
}

export async function getConversationsNotGathered(params?: GetConversationsNotGatheredParams) {
  const { organization } = await getAuthOrganizationContext();
  const page = params?.page || 1;
  const pageSize = params?.pageSize || 50;
  const skip = (page - 1) * pageSize;

  const [conversations, total] = await Promise.all([
    prisma.instagramConversationsNotGathered.findMany({
      where: {
        organizationId: organization.id
      },
      orderBy: {
        updatedTime: 'desc'
      },
      skip,
      take: pageSize
    }),
    prisma.instagramConversationsNotGathered.count({
      where: {
        organizationId: organization.id
      }
    })
  ]);

  return {
    conversations,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  };
}
