export interface InstagramSettingsDto {
  id: string;
  isBotEnabled: boolean;
  minResponseTime: number;
  maxResponseTime: number;
  messageDelayMin: number;
  messageDelayMax: number;
  instagramToken?: string;
  instagramAccountId?: string;
  instagramUsername?: string;
  instagramName?: string;
  instagramProfilePicture?: string;
  isConnected?: boolean;
  tokenExpiresAt?: Date;
  autoCleanupEnabled?: boolean;
  followUpCleanupDays?: number;
}
