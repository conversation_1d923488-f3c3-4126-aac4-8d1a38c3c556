import { prisma } from '@workspace/database/client';
import { triggerFollowerProcessing } from './instagram-follower-trigger';

export interface FollowerProcessingQueueStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
}

export class FollowerProcessingQueue {
  private processingInterval: NodeJS.Timeout | null = null;
  private isProcessing = false;
  private readonly BATCH_SIZE = 10; // Process 10 followers at a time
  private readonly CONVERSATION_DELAY = 60000; // 60 seconds (1 minute) delay for AI conversation processing
  private readonly BATCH_DELAY = 1000; // 1 second delay for batch processing

  constructor(private intervalMs: number = 300000) { } // Check every 5 minutes (300000ms)

  async start(): Promise<void> {
    if (this.processingInterval) {
      return;
    }

    console.log('🚀 Follower processing queue ENABLED for conversation gathering');
    // Queue processing enabled for conversation gathering
    this.processingInterval = setInterval(() => {
      this.processQueue().catch(error => {
        console.error('❌ Error in follower queue processing:', error);
      });
    }, this.intervalMs);

    // Process immediately on start
    await this.processQueue();
  }

  async stop(): Promise<void> {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    console.log('⏹️ Stopped follower processing queue processor');
  }

  /**
   * Add a follower to the processing queue
   */
  async enqueue(
    organizationId: string,
    followerId: string,
    priority: number = 1,
    hasConversation: boolean = false
  ): Promise<string> {
    try {
      const queueItem = await prisma.followerProcessingQueue.create({
        data: {
          organizationId,
          followerId,
          priority,
          scheduledAt: new Date(),
          status: 'pending',
          hasConversation,
          processingType: hasConversation ? 'conversation' : 'batch'
        }
      });

      console.log(`📝 Enqueued follower ${followerId} for processing (priority: ${priority}, hasConversation: ${hasConversation})`);
      return queueItem.id;
    } catch (error) {
      console.error('❌ Error enqueuing follower:', error);
      throw error;
    }
  }

  /**
   * Process pending items in the queue
   */
  async processQueue(): Promise<void> {
    if (this.isProcessing) {
      console.log('⚠️ Queue processing already in progress, skipping...');
      return;
    }

    this.isProcessing = true;
    console.log('🚀 Starting follower processing queue...');

    try {
      // Get pending items that are due for processing
      const pendingItems = await prisma.followerProcessingQueue.findMany({
        where: {
          status: 'pending',
          scheduledAt: {
            lte: new Date()
          },
          attempts: {
            lt: prisma.followerProcessingQueue.fields.maxAttempts
          }
        },
        include: {
          InstagramFollower: true
        }
      });

      if (pendingItems.length === 0) {
        console.log('✅ No pending followers to process');
        // --- WATCHDOG: Check for stuck conversations ---
        const stuckConversations = await prisma.instagramConversationsNotGathered.findMany({
          where: {
            isGathered: false
          },
          take: 10 // Limit to avoid overload, process in batches
        });
        if (stuckConversations.length > 0) {
          console.log(`🛠️ Watchdog: Found ${stuckConversations.length} stuck conversations. Auto-processing...`);
          for (const conv of stuckConversations) {
            try {
              // Find or create follower for this participant
              const follower = await prisma.instagramFollower.findFirst({
                where: {
                  organizationId: conv.organizationId,
                  instagramNickname: conv.participantUsername
                }
              });
              if (follower) {
                // Trigger normal processing for this follower
                await triggerFollowerProcessing(follower.id);
                // Mark conversation as gathered (defensive)
                await prisma.instagramConversationsNotGathered.updateMany({
                  where: { id: conv.id },
                  data: { isGathered: true }
                });
                console.log(`✅ Watchdog: Processed stuck conversation for ${conv.participantUsername}`);
              } else {
                // If no follower, just mark as gathered to avoid infinite loop
                await prisma.instagramConversationsNotGathered.updateMany({
                  where: { id: conv.id },
                  data: { isGathered: true }
                });
                console.log(`⚠️ Watchdog: No follower found for ${conv.participantUsername}, marked as gathered.`);
              }
            } catch (err) {
              console.error(`❌ Watchdog: Error processing stuck conversation for ${conv.participantUsername}:`, err);
            }
          }
        }
        // --- END WATCHDOG ---
        return;
      }

      console.log(`🔄 Processing ${pendingItems.length} followers from queue`);

      // Separate conversation and batch processing
      const conversationItems = pendingItems.filter(item => item.hasConversation);
      const batchItems = pendingItems.filter(item => !item.hasConversation);

      // Process batch items first (faster)
      if (batchItems.length > 0) {
        console.log(`📦 Processing ${batchItems.length} batch followers`);
        await this.processBatchItems(batchItems);
      }

      // Process conversation items with delays (slower)
      if (conversationItems.length > 0) {
        console.log(`💬 Processing ${conversationItems.length} conversation followers with AI delays`);
        await this.processConversationItems(conversationItems);
      }

      console.log('✅ Queue processing completed');

    } catch (error) {
      console.error('❌ Error processing queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process batch items (no conversation, faster)
   */
  private async processBatchItems(items: any[]): Promise<void> {
    for (const item of items) {
      try {
        await this.processQueueItem(item);
        
        // Small delay between batch items
        if (items.indexOf(item) < items.length - 1) {
          await new Promise(resolve => setTimeout(resolve, this.BATCH_DELAY));
        }
      } catch (error) {
        console.error(`❌ Error processing batch item ${item.id}:`, error);
      }
    }
  }

  /**
   * Process conversation items (with AI, slower with delays)
   */
  private async processConversationItems(items: any[]): Promise<void> {
    for (const item of items) {
      try {
        await this.processQueueItem(item);
        
        // 60-second delay between conversation processing (AI rate limiting)
        if (items.indexOf(item) < items.length - 1) {
          console.log(`⏱️ Waiting ${this.CONVERSATION_DELAY / 1000}s before next AI conversation processing...`);
          await new Promise(resolve => setTimeout(resolve, this.CONVERSATION_DELAY));
        }
      } catch (error) {
        console.error(`❌ Error processing conversation item ${item.id}:`, error);
      }
    }
  }

  /**
   * Process a single queue item
   */
  private async processQueueItem(queueItem: any): Promise<void> {
    try {
      // Mark as processing
      await prisma.followerProcessingQueue.update({
        where: { id: queueItem.id },
        data: {
          status: 'processing',
          attempts: { increment: 1 },
          updatedAt: new Date()
        }
      });

      console.log(`🔄 Processing follower: ${queueItem.InstagramFollower.instagramNickname}`);

      // Call the existing trigger function
      const result = await triggerFollowerProcessing(queueItem.followerId);

      if (result.success) {
        // Mark as completed
        await prisma.followerProcessingQueue.update({
          where: { id: queueItem.id },
          data: {
            status: 'completed',
            updatedAt: new Date()
          }
        });

        console.log(`✅ Successfully processed follower: ${queueItem.InstagramFollower.instagramNickname}`);
      } else {
        // Mark as failed
        await prisma.followerProcessingQueue.update({
          where: { id: queueItem.id },
          data: {
            status: 'failed',
            errorMessage: result.error || 'Unknown error',
            updatedAt: new Date()
          }
        });

        console.error(`❌ Failed to process follower: ${queueItem.InstagramFollower.instagramNickname} - ${result.error}`);
      }

    } catch (error) {
      // Mark as failed
      await prisma.followerProcessingQueue.update({
        where: { id: queueItem.id },
        data: {
          status: 'failed',
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
          updatedAt: new Date()
        }
      });

      console.error(`💥 Error processing queue item ${queueItem.id}:`, error);
      throw error;
    }
  }

  /**
   * Get queue statistics
   */
  async getStats(): Promise<FollowerProcessingQueueStats> {
    const stats = await prisma.followerProcessingQueue.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    });

    const result: FollowerProcessingQueueStats = {
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0
    };

    stats.forEach(stat => {
      result[stat.status as keyof FollowerProcessingQueueStats] = stat._count.id;
    });

    return result;
  }

  /**
   * Clean up old completed/failed items
   */
  async cleanup(olderThanDays: number = 7): Promise<number> {
    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);

    const result = await prisma.followerProcessingQueue.deleteMany({
      where: {
        status: { in: ['completed', 'failed'] },
        updatedAt: {
          lt: cutoffDate
        }
      }
    });

    console.log(`🧹 Cleaned up ${result.count} old queue items`);
    return result.count;
  }

  /**
   * Retry failed items
   */
  async retryFailed(organizationId?: string): Promise<number> {
    const where: any = {
      status: 'failed',
      attempts: {
        lt: prisma.followerProcessingQueue.fields.maxAttempts
      }
    };

    if (organizationId) {
      where.organizationId = organizationId;
    }

    const result = await prisma.followerProcessingQueue.updateMany({
      where,
      data: {
        status: 'pending',
        scheduledAt: new Date(),
        errorMessage: null,
        updatedAt: new Date()
      }
    });

    console.log(`🔄 Retrying ${result.count} failed queue items`);
    return result.count;
  }
}

// Export singleton instance
export const followerProcessingQueue = new FollowerProcessingQueue();
