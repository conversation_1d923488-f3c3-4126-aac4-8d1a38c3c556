import { followerProcessingQueue } from './follower-processing-queue';

/**
 * Queue Manager - Handles initialization and management of all queues
 */
class QueueManager {
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('⚠️ Queue manager already initialized (DISABLED)');
      return;
    }

    try {
      console.log('🚀 Initializing Queue Manager - Starting follower processing');

      // Start the follower processing queue
      await followerProcessingQueue.start();

      this.isInitialized = true;
      console.log('✅ Queue Manager initialized successfully');

    } catch (error) {
      console.error('❌ Error initializing Queue Manager:', error);
      throw error;
    }
  }

  async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      console.log('🛑 Shutting down Queue Manager...');

      // Stop the follower processing queue
      await followerProcessingQueue.stop();

      this.isInitialized = false;
      console.log('✅ Queue Manager shut down successfully');

    } catch (error) {
      console.error('❌ Error shutting down Queue Manager:', error);
      throw error;
    }
  }

  getStatus(): { initialized: boolean } {
    return {
      initialized: this.isInitialized
    };
  }
}

// Export singleton instance
export const queueManager = new QueueManager();

// Auto-initialize queue manager
queueManager.initialize().catch(error => {
  console.error('❌ Failed to auto-initialize Queue Manager:', error);
});
