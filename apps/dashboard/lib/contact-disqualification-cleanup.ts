/**
 * Contact Disqualification Cleanup Utility
 * 
 * This module provides comprehensive cleanup logic for when Instagram contacts
 * are marked as "disqualified". It ensures all automation stops and resources are cleaned up.
 */

import { prisma } from '@workspace/database/client';
import { InstagramContactStage } from '@workspace/database';

export interface DisqualificationCleanupResult {
  success: boolean;
  contactId: string;
  actions: {
    pendingFollowUpsRemoved: number;
    externalFollowUpsRemoved: number;
    legacyFollowUpsCleared: number;
    attackListCleared: boolean;
    automationStopped: boolean;
    scheduledMessagesCleared: boolean;
  };
  error?: string;
}

/**
 * Comprehensive cleanup when a contact becomes disqualified
 * This function should be called whenever a contact's stage changes to 'disqualified'
 */
export async function handleContactDisqualification(
  contactId: string,
  organizationId: string,
  reason?: string
): Promise<DisqualificationCleanupResult> {
  const result: DisqualificationCleanupResult = {
    success: false,
    contactId,
    actions: {
      pendingFollowUpsRemoved: 0,
      externalFollowUpsRemoved: 0,
      legacyFollowUpsCleared: 0,
      attackListCleared: false,
      automationStopped: false,
      scheduledMessagesCleared: false,
    }
  };

  try {
    // Verify contact exists and belongs to organization
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: contactId,
        organizationId
      },
      select: {
        id: true,
        instagramNickname: true,
        stage: true,
        attackListStatus: true,
        batchMessageStatus: true,
        priority: true
      }
    });

    if (!contact) {
      result.error = 'Contact not found or does not belong to organization';
      return result;
    }

    console.log(`Starting disqualification cleanup for contact ${contact.instagramNickname} (${contactId})`);

    // Perform cleanup in a transaction to ensure consistency
    await prisma.$transaction(async (tx) => {
      // 1. Remove ALL pending follow-ups (new system)
      const deletePendingFollowUps = await tx.instagramFollowUp.deleteMany({
        where: {
          contactId,
          status: 'pending'
        }
      });
      result.actions.pendingFollowUpsRemoved = deletePendingFollowUps.count;

      // 2. Remove ALL external follow-ups (attack list items)
      const deleteExternalFollowUps = await tx.instagramFollowUp.deleteMany({
        where: {
          contactId,
          status: 'external'
        }
      });
      result.actions.externalFollowUpsRemoved = deleteExternalFollowUps.count;

      // 3. Clear legacy follow-up fields from InstagramContact table
      const legacyFieldsToUpdate: Record<string, any> = {};
      let legacyFollowUpsCleared = 0;

      // Clear all 6 legacy follow-up slots
      for (let i = 1; i <= 6; i++) {
        legacyFieldsToUpdate[`followUpMessage${i}`] = null;
        legacyFieldsToUpdate[`followUpTime${i}`] = null;
        legacyFieldsToUpdate[`followUpStatus${i}`] = null;
        legacyFollowUpsCleared++;
      }
      result.actions.legacyFollowUpsCleared = legacyFollowUpsCleared;

      // 4. Update contact to stop all automation and clear attack list
      await tx.instagramContact.update({
        where: { id: contactId },
        data: {
          // Clear legacy follow-up fields
          ...legacyFieldsToUpdate,
          
          // Stop attack list processing
          attackListStatus: 'disqualified',
          nextMessageAt: null,
          
          // Stop batch message processing
          batchMessageStatus: 'disqualified',
          currentMessageSequence: null,
          batchId: null,
          
          // Reset priority (no longer needs high priority processing)
          priority: 5, // Lowest priority since disqualified
          
          // Update stage if not already set
          stage: InstagramContactStage.disqualified,
          
          // Update timestamps
          updatedAt: new Date()
        }
      });

      result.actions.attackListCleared = true;
      result.actions.automationStopped = true;
      result.actions.scheduledMessagesCleared = true;

      // 5. Remove from any message queues if they exist
      const deletedQueueItems = await tx.messageQueue.deleteMany({
        where: {
          organizationId,
          senderId: contact.instagramNickname
        }
      });

      // 6. Remove from follower processing queue if exists
      await tx.followerProcessingQueue.deleteMany({
        where: {
          organizationId,
          InstagramFollower: {
            instagramNickname: contact.instagramNickname
          }
        }
      });

      console.log(`Disqualification cleanup completed for ${contact.instagramNickname}:
      - Pending follow-ups removed: ${result.actions.pendingFollowUpsRemoved}
      - External follow-ups removed: ${result.actions.externalFollowUpsRemoved}
      - Legacy follow-ups cleared: ${result.actions.legacyFollowUpsCleared}
      - Message queue items removed: ${deletedQueueItems.count}
      - Attack list status set to 'disqualified'
      - All automation stopped`);
    });

    result.success = true;
    return result;

  } catch (error) {
    console.error(`Error during disqualification cleanup for contact ${contactId}:`, error);
    result.error = error instanceof Error ? error.message : 'Unknown error during cleanup';
    return result;
  }
}

/**
 * Batch cleanup for multiple contacts becoming disqualified
 * Useful for bulk operations or automated AI analysis
 */
export async function handleBatchContactDisqualification(
  contactIds: string[],
  organizationId: string,
  reason?: string
): Promise<DisqualificationCleanupResult[]> {
  const results: DisqualificationCleanupResult[] = [];

  for (const contactId of contactIds) {
    const result = await handleContactDisqualification(contactId, organizationId, reason);
    results.push(result);
    
    // Add a small delay between operations to avoid overwhelming the database
    if (contactIds.length > 1) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`Batch disqualification cleanup completed: ${successful} successful, ${failed} failed`);
  
  return results;
}

/**
 * Get all contacts that are eligible for disqualification cleanup
 * This can be used for maintenance or verification purposes
 */
export async function getContactsNeedingDisqualificationCleanup(
  organizationId: string
): Promise<{
  id: string;
  instagramNickname: string;
  stage: string;
  pendingFollowUps: number;
  hasLegacyFollowUps: boolean;
  isInAttackList: boolean;
}[]> {
  const disqualifiedContacts = await prisma.instagramContact.findMany({
    where: {
      organizationId,
      stage: InstagramContactStage.disqualified,
      OR: [
        // Has pending follow-ups that should be cleaned
        {
          InstagramFollowUp: {
            some: {
              status: { in: ['pending', 'external'] }
            }
          }
        },
        // Has legacy follow-up data that should be cleared
        { followUpMessage1: { not: null } },
        { followUpMessage2: { not: null } },
        { followUpMessage3: { not: null } },
        { followUpMessage4: { not: null } },
        { followUpMessage5: { not: null } },
        { followUpMessage6: { not: null } },
        // Still in attack list
        { attackListStatus: { not: 'disqualified' } },
        // Still has scheduled messages
        { nextMessageAt: { not: null } }
      ]
    },
    select: {
      id: true,
      instagramNickname: true,
      stage: true,
      attackListStatus: true,
      nextMessageAt: true,
      followUpMessage1: true,
      followUpMessage2: true,
      followUpMessage3: true,
      followUpMessage4: true,
      followUpMessage5: true,
      followUpMessage6: true,
      _count: {
        select: {
          InstagramFollowUp: {
            where: {
              status: { in: ['pending', 'external'] }
            }
          }
        }
      }
    }
  });

  return disqualifiedContacts.map(contact => ({
    id: contact.id,
    instagramNickname: contact.instagramNickname,
    stage: contact.stage,
    pendingFollowUps: contact._count.InstagramFollowUp,
    hasLegacyFollowUps: !!(
      contact.followUpMessage1 || contact.followUpMessage2 || contact.followUpMessage3 ||
      contact.followUpMessage4 || contact.followUpMessage5 || contact.followUpMessage6
    ),
    isInAttackList: contact.attackListStatus !== 'disqualified' || !!contact.nextMessageAt
  }));
}

/**
 * Utility function to check if a stage change requires disqualification cleanup
 */
export function shouldTriggerDisqualificationCleanup(
  fromStage: string | null,
  toStage: string
): boolean {
  // Only trigger cleanup when moving TO disqualified stage
  // (not when moving FROM disqualified to another stage)
  return toStage === InstagramContactStage.disqualified && fromStage !== InstagramContactStage.disqualified;
}

/**
 * Get summary statistics for disqualification cleanup operations
 */
export async function getDisqualificationCleanupStats(
  organizationId: string
): Promise<{
  totalDisqualified: number;
  needingCleanup: number;
  pendingFollowUpsToClean: number;
  legacyFollowUpsToClean: number;
}> {
  const [totalDisqualified, contactsNeedingCleanup] = await Promise.all([
    prisma.instagramContact.count({
      where: {
        organizationId,
        stage: InstagramContactStage.disqualified
      }
    }),
    getContactsNeedingDisqualificationCleanup(organizationId)
  ]);

  const pendingFollowUpsToClean = contactsNeedingCleanup.reduce(
    (sum, contact) => sum + contact.pendingFollowUps, 0
  );

  const legacyFollowUpsToClean = contactsNeedingCleanup.filter(
    contact => contact.hasLegacyFollowUps
  ).length;

  return {
    totalDisqualified,
    needingCleanup: contactsNeedingCleanup.length,
    pendingFollowUpsToClean,
    legacyFollowUpsToClean
  };
}