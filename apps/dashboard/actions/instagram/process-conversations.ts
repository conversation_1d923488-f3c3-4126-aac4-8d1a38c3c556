'use server';

import { revalidatePath } from 'next/cache';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { generateInstagramResponse } from '@workspace/instagram-bot';

import { getAllConversations, getConversationMessages, getBusinessAccountInfo } from '~/lib/instagram-client';

interface ProcessResult {
  totalConversations: number;
  matchedFollowers: number;
  newContacts: number;
  processedMessages: number;
  aiAnalyzed: number;
  errors: string[];
}

export async function processInstagramConversations() {
  try {
    const ctx = await getAuthOrganizationContext();
    const result: ProcessResult = {
      totalConversations: 0,
      matchedFollowers: 0,
      newContacts: 0,
      processedMessages: 0,
      aiAnalyzed: 0,
      errors: []
    };

    // Get Instagram settings
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: { organizationId: ctx.organization.id },
      select: { instagramToken: true }
    });

    if (!instagramSettings?.instagramToken) {
      return {
        success: false,
        error: 'Instagram token not configured. Please configure your Instagram settings first.'
      };
    }

    // Step 1: Get business account info
    console.log('Step 1: Getting business account info...');
    let businessAccountInfo;
    try {
      businessAccountInfo = await getBusinessAccountInfo(instagramSettings.instagramToken);
      console.log(`Business Account: ${businessAccountInfo.username} (ID: ${businessAccountInfo.id})`);
    } catch (error: any) {
      console.error('Error getting business account info:', error);
      return {
        success: false,
        error: error.message || 'Failed to get business account info'
      };
    }

    // Step 2: Fetch all conversations
    console.log('Step 2: Fetching all conversations...');
    let conversations;
    try {
      conversations = await getAllConversations(instagramSettings.instagramToken);
      result.totalConversations = conversations.length;
      console.log(`Found ${conversations.length} conversations`);
    } catch (error: any) {
      console.error('Error fetching conversations:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch conversations from Instagram'
      };
    }

    // Step 3: Get all followers for matching
    const followers = await prisma.instagramFollower.findMany({
      where: { organizationId: ctx.organization.id },
      select: {
        id: true,
        instagramNickname: true,
        instagramId: true
      }
    });

    // Create maps for quick lookup
    const followersByUsername = new Map(followers.map(f => [f.instagramNickname, f]));
    const followersByInstagramId = new Map(followers.filter(f => f.instagramId).map(f => [f.instagramId!, f]));

    // Step 4: Process each conversation
    for (const conversation of conversations) {
      try {
        if (!conversation.participants?.data) continue;

        // Find the participant that's not the business account (the customer)
        // Note: Business account ID from /me endpoint might be different from participant ID
        // So we check both ID and username to identify the business account
        const customerParticipant = conversation.participants.data.find(
          (p: any) => {
            // Skip if this participant matches business account ID
            if (p.id === businessAccountInfo.id) return false;
            
            // Skip if this participant matches business account username
            if (p.username === businessAccountInfo.username) return false;
            
            // This must be a customer
            return true;
          }
        );

        if (!customerParticipant) {
          console.log(`Skipping conversation ${conversation.id} - no customer participant found`);
          continue;
        }

        const { username, id: participantId } = customerParticipant;
        console.log(`Processing conversation with customer: ${username || 'no username'} (ID: ${participantId})`);

        if (!username && !participantId) {
          console.log('Skipping - no username or participant ID');
          continue;
        }

        // Step 5: Match with follower and update contacted status
        let follower = followersByUsername.get(username) || followersByInstagramId.get(participantId);
        
        if (follower) {
          // Update follower with conversation info
          await prisma.instagramFollower.update({
            where: { id: follower.id },
            data: {
              conversationId: conversation.id,
              isContacted: true,
              instagramId: participantId, // Update Instagram ID if we didn't have it
              updatedAt: new Date()
            }
          });
          result.matchedFollowers++;
        }

        // Step 6: Check if contact already exists
        let contact = await prisma.instagramContact.findFirst({
          where: {
            organizationId: ctx.organization.id,
            OR: [
              { instagramNickname: username },
              { instagramId: participantId }
            ]
          }
        });

        if (!contact) {
          // Create new contact
          contact = await prisma.instagramContact.create({
            data: {
              organizationId: ctx.organization.id,
              userId: ctx.session.user.id,
              instagramNickname: username,
              instagramId: participantId,
              stage: 'new',
              priority: 3,
              conversationSource: 'api',
              lastInteractionAt: new Date(conversation.updated_time)
            }
          });
          result.newContacts++;
        }

        // Step 7: Download full conversation
        console.log(`Downloading conversation for ${username || participantId}...`);
        const conversationHistory = await getConversationMessages(
          conversation.id,
          instagramSettings.instagramToken
        );

        if (conversationHistory.data?.[0]?.messages?.data) {
          const messages = conversationHistory.data[0].messages.data;
          
          // Save messages to database
          for (const message of messages) {
            const existingMessage = await prisma.instagramMessage.findFirst({
              where: {
                contactId: contact.id,
                messageId: message.id
              }
            });

            if (!existingMessage) {
              const isFromUser = message.from?.id === participantId;
              
              await prisma.instagramMessage.create({
                data: {
                  contactId: contact.id,
                  messageId: message.id,
                  content: message.message || '',
                  isFromUser,
                  timestamp: new Date(parseInt(message.created_time) * 1000)
                }
              });
              result.processedMessages++;
            }
          }

          // Update message count
          const messageCount = await prisma.instagramMessage.count({
            where: { contactId: contact.id }
          });

          await prisma.instagramContact.update({
            where: { id: contact.id },
            data: { messageCount }
          });

          // Step 8: Pass conversation to AI for analysis
          if (messages.length > 0) {
            console.log(`Analyzing conversation with AI for ${username || participantId}...`);
            
            // Get last message timestamp for context
            const lastMessage = messages[messages.length - 1];
            const lastMessageTime = lastMessage ? new Date(parseInt(lastMessage.created_time) * 1000) : new Date();
            const timeSinceLastMessage = Math.round((Date.now() - lastMessageTime.getTime()) / (1000 * 60)); // minutes
            
            // Filtrowanie wiadomości: pomijaj extension
            const filteredMessages = messages.filter((msg: any) => !msg.isFromExtension);
            const conversationText = filteredMessages
              .map((msg: any) => `${msg.from?.id === participantId ? 'User' : 'Bot'}: ${msg.message || '[Media]'}`)
              .join('\n');
              
            // Add timing context to the conversation
            const conversationWithContext = `Last message was sent ${timeSinceLastMessage} minutes ago.

Conversation:
${conversationText}`;

            try {
              // Add small delay between AI calls to prevent overwhelming the system
              await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
              
              const aiResponse = await generateInstagramResponse({
                prompt: conversationWithContext,
                conversationHistory: conversationWithContext,
                organizationId: `${ctx.organization.id}-${contact.id}`, // Unique ID per contact to avoid circuit breaker
                contactId: contact.id
                // Keep cache enabled for cost savings on bulk processing
              });

              // Update contact based on AI analysis
              const updateData: any = { updatedAt: new Date() };
              
              if (aiResponse.stage) {
                updateData.stage = aiResponse.stage;
              }
              
              if (aiResponse.priority) {
                updateData.priority = aiResponse.priority;
              }
              
              if (Object.keys(updateData).length > 1) { // More than just updatedAt
                await prisma.instagramContact.update({
                  where: { id: contact.id },
                  data: updateData
                });
              }

              // Create follow-ups if suggested
              if (aiResponse.followUps && aiResponse.followUps.length > 0) {
                for (let i = 0; i < aiResponse.followUps.length; i++) {
                  const followUp = aiResponse.followUps[i];
                  const scheduledTime = new Date();
                  
                  // Handle both string and object follow-up formats
                  const message = typeof followUp === 'string' ? followUp : followUp.message;
                  const delayHours = typeof followUp === 'object' && followUp.delayHours ? followUp.delayHours : 24 * (i + 1);
                  
                  scheduledTime.setHours(scheduledTime.getHours() + delayHours);

                  await prisma.instagramFollowUp.create({
                    data: {
                      contactId: contact.id,
                      sequenceNumber: i + 1,
                      message: message,
                      scheduledTime,
                      status: 'pending'
                    }
                  });
                }

                // Update next message time for attack list
                const nextMessageTime = new Date();
                nextMessageTime.setHours(nextMessageTime.getHours() + 24);

                const nextMessageData: any = { nextMessageAt: nextMessageTime };
                
                if (aiResponse.priority) {
                  nextMessageData.priority = aiResponse.priority;
                }
                
                await prisma.instagramContact.update({
                  where: { id: contact.id },
                  data: nextMessageData
                });
              }

              result.aiAnalyzed++;
            } catch (aiError: any) {
              console.error(`AI analysis failed for ${username}:`, aiError);
              result.errors.push(`AI analysis failed for ${username}: ${aiError.message}`);
            }
          }
        }
      } catch (error: any) {
        console.error(`Error processing conversation:`, error);
        result.errors.push(`Error processing conversation: ${error.message}`);
      }
    }

    // Step 9: Mark unmatched followers as not contacted
    await prisma.instagramFollower.updateMany({
      where: {
        organizationId: ctx.organization.id,
        conversationId: null
      },
      data: {
        isContacted: false
      }
    });

    // Revalidate pages
    revalidatePath('/organizations/[slug]/instagram/followers', 'page');
    revalidatePath('/organizations/[slug]/instagram/contacts', 'page');
    revalidatePath('/organizations/[slug]/chrome-extension/attack-list', 'page');

    return {
      success: true,
      message: `Processed ${result.totalConversations} conversations`,
      result
    };

  } catch (error) {
    console.error('Error in processInstagramConversations:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to process conversations'
    };
  }
}