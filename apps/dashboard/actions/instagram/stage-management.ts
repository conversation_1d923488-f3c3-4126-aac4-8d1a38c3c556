'use server';

import { revalidateTag } from 'next/cache';
import { z } from 'zod';

import { prisma } from '@workspace/database/client';
import { InstagramContactStage } from '@workspace/database';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  Caching,
  OrganizationCacheKey
} from '~/data/caching';
import { 
  handleContactDisqualification, 
  shouldTriggerDisqualificationCleanup 
} from '~/lib/contact-disqualification-cleanup';

const manualStageChangeSchema = z.object({
  contactId: z.string().uuid(),
  newStage: z.nativeEnum(InstagramContactStage),
  reason: z.string().optional()
});

const getStageHistorySchema = z.object({
  contactId: z.string().uuid()
});

const getStageStatisticsSchema = z.object({
  organizationId: z.string().uuid().optional()
});

/**
 * Manually change a contact's stage (admin override)
 */
export const manualStageChange = authOrganizationActionClient
  .metadata({ actionName: 'manualStageChange' })
  .schema(manualStageChangeSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Only allow admins to manually change stages
    if (ctx.membership?.role !== 'ADMIN') {
      throw new Error('Only admins can manually change contact stages');
    }

    const { contactId, newStage, reason } = parsedInput;

    // Get current contact
    const contact = await prisma.instagramContact.findUnique({
      where: {
        id: contactId,
        organizationId: ctx.organization.id
      },
      select: { stage: true }
    });

    if (!contact) {
      throw new Error('Contact not found');
    }

    const currentStage = contact.stage;

    // Perform the stage change in a transaction
    await prisma.$transaction(async (tx) => {
      // Update contact stage
      await tx.instagramContact.update({
        where: { id: contactId },
        data: {
          stage: newStage,
          updatedAt: new Date()
        }
      });

      // Log the stage change in StageChangeLog
      await tx.stageChangeLog.create({
        data: {
          contactId,
          fromStage: currentStage,
          toStage: newStage,
          reason: reason || `Manual stage change by admin`,
          changedBy: ctx.session.user.id,
          metadata: {
            action: 'manual_admin_change',
            userId: ctx.session.user.id,
            timestamp: new Date().toISOString()
          }
        }
      });

      console.log(`Stage changed for contact ${contactId}: ${currentStage} -> ${newStage} by ${ctx.session.user.id}`, { reason });
    });

    // Revalidate cache
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.InstagramContacts,
        ctx.organization.id
      )
    );

    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.InstagramContact,
        ctx.organization.id,
        contactId
      )
    );

    let message = `Stage changed from ${currentStage} to ${newStage}`;
    let cleanupSummary = null;

    // Add cleanup information to response if disqualification occurred
    if (shouldTriggerDisqualificationCleanup(currentStage, newStage)) {
      const cleanupResult = await handleContactDisqualification(
        contactId, 
        ctx.organization.id, 
        `Manual disqualification by admin: ${reason || 'No reason provided'}`
      );

      if (cleanupResult.success) {
        const actions = cleanupResult.actions;
        const totalCleanedUp = actions.pendingFollowUpsRemoved + actions.externalFollowUpsRemoved + actions.legacyFollowUpsCleared;
        message += ` and cleaned up ${totalCleanedUp} follow-ups`;
        cleanupSummary = actions;
      }
    }

    return {
      success: true,
      message,
      cleanupSummary
    };
  });

/**
 * Get stage change history for a contact
 */
export const getStageHistory = authOrganizationActionClient
  .metadata({ actionName: 'getStageHistory' })
  .schema(getStageHistorySchema)
  .action(async ({ parsedInput, ctx }) => {
    const { contactId } = parsedInput;

    // Verify contact belongs to organization
    const contact = await prisma.instagramContact.findUnique({
      where: {
        id: contactId,
        organizationId: ctx.organization.id
      },
      select: { id: true }
    });

    if (!contact) {
      throw new Error('Contact not found');
    }

    // TODO: Implement stage history when StageChangeLog model is available
    // const history = await prisma.stageChangeLog.findMany({
    //   where: { contactId },
    //   orderBy: { createdAt: 'desc' },
    //   select: {
    //     id: true,
    //     fromStage: true,
    //     toStage: true,
    //     reason: true,
    //     changedBy: true,
    //     metadata: true,
    //     createdAt: true
    //   }
    // });

    return { history: [] };
  });

/**
 * Get stage statistics for the organization
 */
export const getStageStatistics = authOrganizationActionClient
  .metadata({ actionName: 'getStageStatistics' })
  .schema(getStageStatisticsSchema)
  .action(async ({ parsedInput, ctx }) => {
    const organizationId = parsedInput.organizationId || ctx.organization.id;

    // Get stage distribution
    const stageStats = await prisma.instagramContact.groupBy({
      by: ['stage'],
      where: { organizationId },
      _count: { stage: true }
    });

    const stageDistribution: Record<string, number> = {};
    stageStats.forEach(stat => {
      stageDistribution[stat.stage] = stat._count.stage;
    });

    // Get recent stage changes (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // TODO: Implement recent changes when StageChangeLog model is available
    const recentChanges: Array<{ toStage: string; changedBy: string; createdAt: Date }> = [];

    // Count changes by stage
    const stageChangeCounts: Record<string, number> = {};
    const aiChanges = recentChanges.filter((change: { changedBy: string }) => change.changedBy === 'ai').length;
    const manualChanges = recentChanges.filter((change: { changedBy: string }) => change.changedBy !== 'ai').length;

    recentChanges.forEach((change: { toStage: string }) => {
      stageChangeCounts[change.toStage] = (stageChangeCounts[change.toStage] || 0) + 1;
    });

    // Get conversion metrics
    const totalContacts = Object.values(stageDistribution).reduce((sum, count) => sum + count, 0);
    const convertedContacts = stageDistribution['converted'] || 0;
    const disqualifiedContacts = stageDistribution['disqualified'] || 0;
    const conversionRate = totalContacts > 0 ? (convertedContacts / totalContacts) * 100 : 0;
    const disqualificationRate = totalContacts > 0 ? (disqualifiedContacts / totalContacts) * 100 : 0;

    return {
      stageDistribution,
      stageChangeCounts,
      recentChanges: recentChanges.slice(0, 10), // Return only last 10 for display
      metrics: {
        totalContacts,
        convertedContacts,
        disqualifiedContacts,
        conversionRate: Math.round(conversionRate * 100) / 100,
        disqualificationRate: Math.round(disqualificationRate * 100) / 100,
        aiChanges,
        manualChanges
      }
    };
  });

/**
 * Get contacts that should stop processing
 */
export const getStoppedContacts = authOrganizationActionClient
  .metadata({ actionName: 'getStoppedContacts' })
  .schema(z.object({}))
  .action(async ({ ctx }) => {
    const stoppedStages: InstagramContactStage[] = ['converted', 'disqualified', 'blocked', 'suspicious'];

    const contacts = await prisma.instagramContact.findMany({
      where: {
        organizationId: ctx.organization.id,
        stage: { in: stoppedStages }
      },
      select: {
        id: true,
        instagramNickname: true,
        stage: true,
        updatedAt: true
      },
      orderBy: { updatedAt: 'desc' }
    });

    return { contacts };
  });
