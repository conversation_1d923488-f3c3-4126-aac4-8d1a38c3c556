'use server';

import { revalidatePath } from 'next/cache';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';

import { getAllFollowers } from '~/lib/instagram-client';

export async function fetchFollowersFromAPI(limit?: number) {
  try {
    const ctx = await getAuthOrganizationContext();

    // Get Instagram settings
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: { organizationId: ctx.organization.id },
      select: { instagramToken: true }
    });

    if (!instagramSettings?.instagramToken) {
      return {
        success: false,
        error: 'Instagram token not configured. Please configure your Instagram settings first.'
      };
    }

    // Fetch followers from Instagram API
    let followers;
    try {
      if (limit) {
        // Get limited number of followers
        const response = await getAllFollowers(instagramSettings.instagramToken);
        followers = response.slice(0, limit);
      } else {
        // Get all followers
        followers = await getAllFollowers(instagramSettings.instagramToken);
      }
    } catch (error: any) {
      console.error('Error fetching followers from Instagram:', error);
      return {
        success: false,
        error: error.message || 'Failed to fetch followers from Instagram API'
      };
    }

    if (!followers || followers.length === 0) {
      return {
        success: true,
        message: 'No followers found',
        created: 0,
        skipped: 0
      };
    }

    // Get the next batch number
    const latestBatch = await prisma.instagramFollower.findFirst({
      where: {
        organizationId: ctx.organization.id
      },
      select: { batchNumber: true },
      orderBy: { batchNumber: 'desc' }
    });

    const batchNumber = (latestBatch?.batchNumber || 0) + 1;

    // Check for existing followers
    const existingFollowers = await prisma.instagramFollower.findMany({
      where: {
        organizationId: ctx.organization.id,
        instagramNickname: {
          in: followers.map(f => f.username)
        }
      },
      select: { instagramNickname: true }
    });

    const existingSet = new Set(existingFollowers.map(f => f.instagramNickname));
    const newFollowers = followers.filter(f => !existingSet.has(f.username));

    if (newFollowers.length === 0) {
      return {
        success: true,
        message: 'All followers already exist in database',
        created: 0,
        skipped: followers.length,
        total: followers.length,
        batchNumber
      };
    }

    // Create new followers
    const followersToCreate = newFollowers.map(follower => ({
      userId: ctx.session.user.id,
      organizationId: ctx.organization.id,
      instagramNickname: follower.username,
      instagramId: follower.id,
      avatar: follower.profile_picture_url,
      followerCount: follower.followers_count || 0,
      isVerified: follower.is_verified || false,
      batchNumber,
      priority: 'normal' as const,
      status: 'pending' as const
    }));

    const result = await prisma.instagramFollower.createMany({
      data: followersToCreate,
      skipDuplicates: true
    });

    // Revalidate pages
    revalidatePath('/organizations/[slug]/instagram/followers', 'page');
    revalidatePath('/organizations/[slug]/home', 'page');

    return {
      success: true,
      message: `Successfully fetched and saved ${result.count} new followers from Instagram`,
      created: result.count,
      skipped: followers.length - newFollowers.length,
      total: followers.length,
      batchNumber
    };

  } catch (error) {
    console.error('Error in fetchFollowersFromAPI:', error);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch followers'
    };
  }
}