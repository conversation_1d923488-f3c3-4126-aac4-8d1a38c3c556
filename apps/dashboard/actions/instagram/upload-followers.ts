'use server';

import { revalidatePath } from 'next/cache';
import { z } from 'zod';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';

import type { InstagramFollowerBatchDto } from '~/types/dtos/instagram-follower-dto';

const UploadFollowersSchema = z.object({
  followers: z.array(z.object({
    instagramNickname: z.string().min(1),
    instagramId: z.string().optional(),
    avatar: z.string().url().optional(),
    followerCount: z.number().int().min(0).optional(),
    isVerified: z.boolean().default(false)
  })).min(1).max(1000),
  prioritizeNew: z.boolean().default(false)
});

export async function uploadFollowers(data: InstagramFollowerBatchDto) {
  try {
    const ctx = await getAuthOrganizationContext();
    const validatedData = UploadFollowersSchema.parse(data);

    // Get the next batch number
    const latestBatch = await prisma.instagramFollower.findFirst({
      where: {
        organizationId: ctx.organization.id
      },
      select: { batchNumber: true },
      orderBy: { batchNumber: 'desc' }
    });

    const batchNumber = (latestBatch?.batchNumber || 0) + 1;

    // Check for existing followers
    const existingNicknames = await prisma.instagramFollower.findMany({
      where: {
        organizationId: ctx.organization.id,
        instagramNickname: {
          in: validatedData.followers.map(f => f.instagramNickname)
        }
      },
      select: { instagramNickname: true }
    });

    const existingSet = new Set(existingNicknames.map(f => f.instagramNickname));
    const newFollowers = validatedData.followers.filter(
      f => !existingSet.has(f.instagramNickname)
    );

    if (newFollowers.length === 0) {
      return {
        success: true,
        message: 'All followers already exist',
        created: 0,
        skipped: validatedData.followers.length,
        batchNumber
      };
    }

    // Create new followers
    const followersToCreate = newFollowers.map(follower => ({
      userId: ctx.session.user.id,
      organizationId: ctx.organization.id,
      instagramNickname: follower.instagramNickname,
      instagramId: follower.instagramId,
      avatar: follower.avatar,
      followerCount: follower.followerCount,
      isVerified: follower.isVerified || false,
      batchNumber,
      priority: validatedData.prioritizeNew ? 'high' as const : 'normal' as const
    }));

    const result = await prisma.instagramFollower.createMany({
      data: followersToCreate,
      skipDuplicates: true
    });

    // Revalidate the followers page
    revalidatePath('/organizations/[slug]/instagram/followers', 'page');
    revalidatePath('/organizations/[slug]/home', 'page');

    return {
      success: true,
      message: `Successfully uploaded ${result.count} followers`,
      created: result.count,
      skipped: validatedData.followers.length - newFollowers.length,
      batchNumber
    };

  } catch (error) {
    console.error('Error uploading followers:', error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: 'Invalid data format',
        details: error.errors
      };
    }

    return {
      success: false,
      error: 'Failed to upload followers'
    };
  }
}

export async function updateFollowerPriority(followerId: string, priority: 'low' | 'normal' | 'high' | 'urgent') {
  try {
    const ctx = await getAuthOrganizationContext();

    await prisma.instagramFollower.update({
      where: {
        id: followerId,
        organizationId: ctx.organization.id
      },
      data: {
        priority,
        updatedAt: new Date()
      }
    });

    revalidatePath('/organizations/[slug]/instagram/followers', 'page');
    revalidatePath('/organizations/[slug]/home', 'page');

    return { success: true };
  } catch (error) {
    console.error('Error updating follower priority:', error);
    return { success: false, error: 'Failed to update priority' };
  }
}

export async function toggleFollowerAutomation(followerId: string, enabled: boolean) {
  try {
    const ctx = await getAuthOrganizationContext();

    await prisma.instagramFollower.update({
      where: {
        id: followerId,
        organizationId: ctx.organization.id
      },
      data: {
        automationEnabled: enabled,
        updatedAt: new Date()
      }
    });

    revalidatePath('/organizations/[slug]/instagram/followers', 'page');
    revalidatePath('/organizations/[slug]/home', 'page');

    return { success: true };
  } catch (error) {
    console.error('Error toggling follower automation:', error);
    return { success: false, error: 'Failed to toggle automation' };
  }
}

export async function clearAllFollowers() {
  try {
    const ctx = await getAuthOrganizationContext();

    const deleteResult = await prisma.instagramFollower.deleteMany({
      where: {
        organizationId: ctx.organization.id
      }
    });

    revalidatePath('/organizations/[slug]/instagram/followers', 'page');
    revalidatePath('/organizations/[slug]/home', 'page');

    return { 
      success: true, 
      message: `Successfully cleared ${deleteResult.count} followers`,
      deletedCount: deleteResult.count
    };
  } catch (error) {
    console.error('Error clearing followers:', error);
    return { success: false, error: 'Failed to clear followers' };
  }
}
