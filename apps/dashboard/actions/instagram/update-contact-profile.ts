'use server';

import { revalidateTag } from 'next/cache';
import { z } from 'zod';

import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';
import { getProfile } from '~/lib/instagram-client';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  Caching,
  OrganizationCacheKey
} from '~/data/caching';

const updateContactProfileSchema = z.object({
  contactId: z.string().uuid()
});

export const updateContactProfile = authOrganizationActionClient
  .metadata({ actionName: 'updateContactProfile' })
  .schema(updateContactProfileSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Find the contact and make sure it belongs to the organization
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: parsedInput.contactId,
        organizationId: ctx.organization.id
      },
      select: {
        id: true,
        instagramId: true,
        Organization: {
          select: {
            InstagramSettings: {
              select: {
                instagramToken: true
              }
            }
          }
        }
      }
    });

    if (!contact) {
      throw new NotFoundError('Contact not found');
    }

    const instagramToken = contact.Organization.InstagramSettings?.instagramToken;

    if (!instagramToken) {
      throw new Error('Instagram token not configured');
    }

    if (!contact.instagramId) {
      throw new Error('Contact Instagram ID not available');
    }

    try {
      // Get profile information from Instagram
      const profile = await getProfile(
        contact.instagramId,
        instagramToken
      );

      // Update the contact with profile information
      const updatedContact = await prisma.instagramContact.update({
        where: {
          id: contact.id
        },
        data: {
          instagramNickname: profile.username || profile.name || 'unknown',
          avatar: profile.profile_pic || null,
          followerCount: profile.follower_count || null,
          isUserFollowBusiness: profile.is_user_follow_business || null,
          isBusinessFollowUser: profile.is_business_follow_user || null,
          isVerifiedUser: profile.is_verified_user || null
        }
      });

      // Revalidate cache
      revalidateTag(
        Caching.createOrganizationTag(
          OrganizationCacheKey.InstagramContact,
          ctx.organization.id,
          contact.id
        )
      );

      revalidateTag(
        Caching.createOrganizationTag(
          OrganizationCacheKey.InstagramContacts,
          ctx.organization.id
        )
      );

      return {
        success: true,
        contact: updatedContact
      };
    } catch (error) {
      console.error('Error updating contact profile:', error);
      throw new Error('Failed to update contact profile');
    }
  });
