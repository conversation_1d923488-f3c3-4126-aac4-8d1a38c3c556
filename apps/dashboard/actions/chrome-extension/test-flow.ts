'use server';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { getUserIdFromUsername, getConversationHistory } from '~/lib/instagram-client';
import { generateInstagramResponse } from '@workspace/instagram-bot';

interface TestResult {
  step: string;
  success: boolean;
  message: string;
  data?: any;
}

export async function testChromeExtensionFlow() {
  const results: TestResult[] = [];

  try {
    const ctx = await getAuthOrganizationContext();

    // Test followers data
    const testFollowers = [
      {
        instagramNickname: 'norbert_rzepka_biznes',
        followerCount: 5000,
        isVerified: false
      },
      {
        instagramNickname: 'socialflow.pl',
        followerCount: 10000,
        isVerified: true
      },
      {
        instagramNickname: 'Maciej_Rak',
        followerCount: 2500,
        isVerified: false
      },
      {
        instagramNickname: 'ewe<PERSON><PERSON><PERSON>_',
        followerCount: 15000,
        isVerified: false
      },
      {
        instagramNickname: 'ig.was<PERSON><PERSON>',
        followerCount: 8000,
        isVerified: false
      }
    ];

    // Step 1: Upload followers
    try {
      // Get the next batch number
      const latestBatch = await prisma.instagramFollower.findFirst({
        where: { organizationId: ctx.organization.id },
        select: { batchNumber: true },
        orderBy: { batchNumber: 'desc' }
      });

      const batchNumber = (latestBatch?.batchNumber || 0) + 1;

      // Check for existing followers
      const existingNicknames = await prisma.instagramFollower.findMany({
        where: {
          organizationId: ctx.organization.id,
          instagramNickname: { in: testFollowers.map(f => f.instagramNickname) }
        },
        select: { instagramNickname: true }
      });

      const existingSet = new Set(existingNicknames.map(f => f.instagramNickname));
      const newFollowers = testFollowers.filter(f => !existingSet.has(f.instagramNickname));

      // Create new followers
      const createResult = await prisma.instagramFollower.createMany({
        data: newFollowers.map(follower => ({
          userId: ctx.session.user.id,
          organizationId: ctx.organization.id,
          instagramNickname: follower.instagramNickname,
          followerCount: follower.followerCount,
          isVerified: follower.isVerified || false,
          batchNumber,
          priority: 'normal' as const
        })),
        skipDuplicates: true
      });

      results.push({
        step: 'Upload Followers',
        success: true,
        message: `Created ${createResult.count} new followers, skipped ${testFollowers.length - newFollowers.length} existing`,
        data: { created: createResult.count, skipped: testFollowers.length - newFollowers.length, batchNumber }
      });
    } catch (error) {
      results.push({
        step: 'Upload Followers',
        success: false,
        message: error instanceof Error ? error.message : 'Failed to upload followers'
      });
    }

    // Step 2: Convert followers to contacts and analyze
    const analyzedContacts = [];

    for (const follower of testFollowers) {
      try {
        // Check if contact already exists
        let contact = await prisma.instagramContact.findFirst({
          where: {
            organizationId: ctx.organization.id,
            instagramNickname: follower.instagramNickname
          }
        });

        if (!contact) {
          // Try to get Instagram ID
          let instagramId = null;
          try {
            instagramId = await getUserIdFromUsername(follower.instagramNickname);
          } catch (error) {
            console.log(`Could not get Instagram ID for ${follower.instagramNickname}`);
          }

          // Create contact
          contact = await prisma.instagramContact.create({
            data: {
              organizationId: ctx.organization.id,
              userId: ctx.session.user.id,
              instagramNickname: follower.instagramNickname,
              instagramId: instagramId,
              stage: 'new',
              priority: 3, // Default priority for new contacts
              conversationSource: 'extension',
              lastInteractionAt: new Date()
            }
          });
        }

        analyzedContacts.push({
          username: contact.instagramNickname,
          id: contact.id,
          priority: contact.priority,
          stage: contact.stage
        });
      } catch (error) {
        console.error(`Error creating contact for ${follower.instagramNickname}:`, error);
      }
    }

    results.push({
      step: 'Analyze Conversations',
      success: analyzedContacts.length > 0,
      message: `Created/found ${analyzedContacts.length} contacts`,
      data: analyzedContacts
    });

    // Step 2.5: Sync conversations for each contact
    try {
      const instagramSettings = await prisma.instagramSettings.findFirst({
        where: { organizationId: ctx.organization.id },
        select: { instagramToken: true }
      });

      if (instagramSettings?.instagramToken) {
        let syncedCount = 0;

        for (const contact of analyzedContacts) {
          try {
            // Get the full contact with Instagram ID
            const fullContact = await prisma.instagramContact.findUnique({
              where: { id: contact.id },
              select: { id: true, instagramId: true }
            });

            if (fullContact?.instagramId) {
              const conversationHistory = await getConversationHistory(
                fullContact.instagramId,
                instagramSettings.instagramToken
              );

              if (conversationHistory.data?.[0]?.messages?.data) {
                const messages = conversationHistory.data[0].messages.data;
                let messageCount = 0;

                for (const message of messages) {
                  const existingMessage = await prisma.instagramMessage.findFirst({
                    where: {
                      contactId: fullContact.id,
                      messageId: message.id
                    }
                  });

                  if (!existingMessage) {
                    const isFromUser = message.from?.id === fullContact.instagramId;

                    await prisma.instagramMessage.create({
                      data: {
                        contactId: fullContact.id,
                        messageId: message.id,
                        content: message.message || '',
                        isFromUser,
                        isFromExtension: true,
                        timestamp: new Date(parseInt(message.created_time) * 1000)
                      }
                    });
                    messageCount++;
                  }
                }

                // Update message count
                await prisma.instagramContact.update({
                  where: { id: fullContact.id },
                  data: { messageCount }
                });

                if (messageCount > 0) syncedCount++;
              }
            }
          } catch (error) {
            console.error(`Error syncing conversation for ${contact.username}:`, error);
          }
        }

        results.push({
          step: 'Sync Conversations',
          success: true,
          message: `Synced conversations for ${syncedCount} contacts`,
          data: { syncedCount }
        });
      } else {
        results.push({
          step: 'Sync Conversations',
          success: false,
          message: 'Instagram token not configured'
        });
      }
    } catch (error) {
      results.push({
        step: 'Sync Conversations',
        success: false,
        message: error instanceof Error ? error.message : 'Failed to sync conversations'
      });
    }

    // Step 3: Get attack list
    try {
      const attackListContacts = await prisma.instagramContact.findMany({
        where: {
          organizationId: ctx.organization.id,
          conversationSource: { in: ['extension', 'api'] },
          stage: { notIn: ['converted', 'disqualified', 'blocked', 'suspicious'] }
        },
        select: {
          id: true,
          instagramNickname: true,
          priority: true,
          stage: true,
          attackListStatus: true
        },
        orderBy: [
          { nextMessageAt: 'asc' }, // Time first (null values first)
          { priority: 'desc' },     // Then priority (5=highest, 1=lowest)
          { createdAt: 'asc' }      // Finally creation time
        ],
        take: 10
      });

      // Get message batches for priority 3 contacts
      const messageBatches = await prisma.messageBatch.findMany({
        where: { organizationId: ctx.organization.id },
        include: {
          MessageBatchItem: {
            where: { sequenceNumber: 1 }
          }
        }
      });

      const attackList = attackListContacts.map(contact => {
        const item: any = {
          ...contact,
          username: contact.instagramNickname
        };

        // Add suggested message for priority 3 new contacts
        if (contact.priority === 3 && contact.stage === 'new' && messageBatches.length > 0) {
          const batchesWithMessages = messageBatches.filter(b => b.MessageBatchItem.length > 0);
          if (batchesWithMessages.length > 0) {
            const randomBatch = batchesWithMessages[Math.floor(Math.random() * batchesWithMessages.length)];
            item.suggestedMessage = {
              batchName: randomBatch.name,
              text: randomBatch.MessageBatchItem[0].messageText
            };
          }
        }

        return item;
      });

      // Count priorities
      const priorityBreakdown = {
        priority5: attackList.filter(c => c.priority === 5).length,
        priority4: attackList.filter(c => c.priority === 4).length,
        priority3: attackList.filter(c => c.priority === 3).length,
        priority2: attackList.filter(c => c.priority === 2).length,
        priority1: attackList.filter(c => c.priority === 1).length,
      };

      results.push({
        step: 'Get Attack List',
        success: true,
        message: `Found ${attackList.length} contacts in attack list`,
        data: { total: attackList.length, priorityBreakdown, contacts: attackList.slice(0, 5) }
      });
    } catch (error) {
      results.push({
        step: 'Get Attack List',
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get attack list'
      });
    }

    // Step 4: Get random message
    try {
      const messageBatches = await prisma.messageBatch.findMany({
        where: { organizationId: ctx.organization.id },
        include: {
          MessageBatchItem: {
            where: { sequenceNumber: 1 }
          }
        }
      });

      if (messageBatches.length > 0) {
        const batchesWithMessages = messageBatches.filter(b => b.MessageBatchItem.length > 0);

        if (batchesWithMessages.length > 0) {
          const randomBatch = batchesWithMessages[Math.floor(Math.random() * batchesWithMessages.length)];
          const firstMessage = randomBatch.MessageBatchItem[0];

          results.push({
            step: 'Get Random Message',
            success: true,
            message: 'Successfully retrieved random message',
            data: {
              batchName: randomBatch.name,
              messageText: firstMessage.messageText,
              sequenceNumber: firstMessage.sequenceNumber
            }
          });
        } else {
          results.push({
            step: 'Get Random Message',
            success: false,
            message: 'No message batches with messages found'
          });
        }
      } else {
        results.push({
          step: 'Get Random Message',
          success: false,
          message: 'No message batches found'
        });
      }
    } catch (error) {
      results.push({
        step: 'Get Random Message',
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get random message'
      });
    }

    return {
      success: true,
      results
    };

  } catch (error) {
    console.error('Test flow error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Test failed'
    };
  }
}