{"name": "dashboard", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "next dev --port 3000 --turbo", "build": "pnpm --filter @workspace/database generate && next build", "start": "next start --port 3000", "analyze": "BUNDLE_ANALYZE=both next build", "clean": "git clean -xdf .cache .next .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "6.3.1", "@dnd-kit/modifiers": "9.0.0", "@dnd-kit/sortable": "10.0.0", "@dnd-kit/utilities": "3.2.2", "@ebay/nice-modal-react": "1.2.13", "@hookform/resolvers": "4.1.3", "@t3-oss/env-nextjs": "0.12.0", "@tanstack/react-table": "8.21.2", "@workspace/analytics": "workspace:*", "@workspace/api-keys": "workspace:*", "@workspace/auth": "workspace:*", "@workspace/billing": "workspace:*", "@workspace/common": "workspace:*", "@workspace/database": "workspace:*", "@workspace/email": "workspace:*", "@workspace/image-processing": "workspace:*", "@workspace/instagram": "workspace:*", "@workspace/instagram-bot": "workspace:*", "@workspace/markdown": "workspace:*", "@workspace/monitoring": "workspace:*", "@workspace/openrouter": "workspace:*", "@workspace/routes": "workspace:*", "@workspace/ui": "workspace:*", "axios": "^1.6.7", "date-fns": "3.6.0", "exceljs": "4.4.0", "file-saver": "2.0.5", "lucide-react": "0.477.0", "next": "15.2.1", "next-safe-action": "7.10.4", "next-secure-headers": "2.2.0", "node-fetch": "^3.3.2", "nuqs": "2.4.0", "otplib": "12.0.1", "qrcode": "1.5.4", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "7.54.2", "react-is": "19.0.0", "recharts": "2.15.1", "server-only": "0.0.1", "sharp": "0.33.5", "stripe": "16.12.0", "uuid": "11.1.0", "vaul": "1.1.2", "zod": "3.24.2"}, "devDependencies": {"@aws-sdk/client-s3": "^3.0.0", "@next/bundle-analyzer": "15.2.1", "@svgr/webpack": "8.1.0", "@types/file-saver": "2.0.7", "@types/node": "22.13.9", "@types/qrcode": "1.5.5", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/uuid": "10.0.0", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/tailwind-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "autoprefixer": "10.4.20", "postcss": "8.5.3", "tailwindcss": "3.4.17", "tailwindcss-animate": "1.0.7"}, "prettier": "@workspace/prettier-config"}