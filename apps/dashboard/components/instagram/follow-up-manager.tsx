'use client';

import React from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { Button } from '@workspace/ui/components/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form';
import { Input } from '@workspace/ui/components/input';
import { Textarea } from '@workspace/ui/components/textarea';
import { useToast } from '@workspace/ui/hooks/use-toast';

const followUpTemplatesSchema = z.object({
  templates: z.array(z.object({
    messageText: z.string().min(1, 'Message is required'),
    sequenceNumber: z.number().int().min(1).max(3),
    variationNumber: z.number().int().min(1).max(5),
    delayHours: z.number().int().min(1).max(168).default(24)
  })).min(1, 'At least one follow-up is required').max(15, 'Maximum 15 templates allowed')
});

type FollowUpTemplatesFormValues = z.infer<typeof followUpTemplatesSchema>;

interface FollowUpTemplate {
  id: string;
  messageText: string;
  sequenceNumber: number;
  variationNumber: number;
  delayHours: number;
  isActive: boolean;
}

interface FollowUpSequence {
  sequenceNumber: number;
  variations: FollowUpTemplate[];
}

export function FollowUpManager(): React.JSX.Element {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = React.useState(false);
  const [templates, setTemplates] = React.useState<FollowUpTemplate[]>([]);
  const [sequences, setSequences] = React.useState<FollowUpSequence[]>([
    { sequenceNumber: 1, variations: [] },
    { sequenceNumber: 2, variations: [] },
    { sequenceNumber: 3, variations: [] }
  ]);

  const form = useForm<FollowUpTemplatesFormValues>({
    resolver: zodResolver(followUpTemplatesSchema),
    defaultValues: {
      templates: []
    }
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'templates'
  });

  // Load existing templates
  React.useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/instagram/follow-up-templates');
      const result = await response.json();

      if (result.success && result.data.length > 0) {
        setTemplates(result.data);

        // Organize templates by sequence
        const organizedSequences = [1, 2, 3].map(seqNum => ({
          sequenceNumber: seqNum,
          variations: result.data.filter((t: FollowUpTemplate) => t.sequenceNumber === seqNum)
        }));

        setSequences(organizedSequences);

        // Populate form with existing data
        form.reset({
          templates: result.data.map((template: FollowUpTemplate) => ({
            messageText: template.messageText,
            sequenceNumber: template.sequenceNumber,
            variationNumber: template.variationNumber,
            delayHours: template.delayHours
          }))
        });
      }
    } catch (error) {
      console.error('Error loading follow-up templates:', error);
    }
  };

  const onSubmit = async (values: FollowUpTemplatesFormValues) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/instagram/follow-up-templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'Success',
          description: 'Follow-up templates saved successfully'
        });

        loadTemplates();
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to save follow-up templates',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error saving follow-up templates:', error);
      toast({
        title: 'Error',
        description: 'Failed to save follow-up templates',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addVariation = (sequenceNumber: number) => {
    const sequence = sequences.find(s => s.sequenceNumber === sequenceNumber);
    if (sequence && sequence.variations.length < 5) {
      const nextVariationNumber = sequence.variations.length + 1;
      append({
        messageText: '',
        sequenceNumber,
        variationNumber: nextVariationNumber,
        delayHours: 24
      });
    }
  };

  const removeVariation = (sequenceNumber: number, variationNumber: number) => {
    const fieldIndex = fields.findIndex(
      f => f.sequenceNumber === sequenceNumber && f.variationNumber === variationNumber
    );
    if (fieldIndex !== -1) {
      remove(fieldIndex);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Follow-up Messages</CardTitle>
        <CardDescription>
          Configure up to 5 variations for each of the 3 follow-up sequences.
          You can customize the delay hours for each follow-up sequence.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {[1, 2, 3].map((sequenceNumber) => {
              const sequenceFields = fields.filter(f => f.sequenceNumber === sequenceNumber);
              const sequenceVariations = sequences.find(s => s.sequenceNumber === sequenceNumber)?.variations || [];

              return (
                <div key={sequenceNumber} className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-lg font-medium">
                      Follow-up {sequenceNumber}
                      <span className="ml-2 text-sm text-muted-foreground">
                        (Configurable delay)
                      </span>
                    </h4>
                    {sequenceFields.length < 5 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addVariation(sequenceNumber)}
                      >
                        Add Variation
                      </Button>
                    )}
                  </div>

                  <div className="space-y-3">
                    {sequenceFields.map((field, fieldIndex) => {
                      const globalIndex = fields.findIndex(f => f.id === field.id);

                      return (
                        <div key={field.id} className="border rounded p-3 space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">
                              Variation {field.variationNumber}
                            </span>
                            {sequenceFields.length > 1 && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeVariation(sequenceNumber, field.variationNumber)}
                              >
                                Remove
                              </Button>
                            )}
                          </div>

                          <FormField
                            control={form.control}
                            name={`templates.${globalIndex}.messageText`}
                            render={({ field: formField }) => (
                              <FormItem>
                                <FormLabel>Message Text</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder={`Enter follow-up ${sequenceNumber} variation ${field.variationNumber}...`}
                                    {...formField}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`templates.${globalIndex}.delayHours`}
                            render={({ field: formField }) => (
                              <FormItem>
                                <FormLabel>Delay Hours</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="1"
                                    max="168"
                                    placeholder="24"
                                    {...formField}
                                    onChange={(e) => formField.onChange(parseInt(e.target.value) || 24)}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Hours to wait before sending this follow-up (1-168 hours)
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Hidden fields */}
                          <FormField
                            control={form.control}
                            name={`templates.${globalIndex}.sequenceNumber`}
                            render={({ field: formField }) => (
                              <input type="hidden" {...formField} value={sequenceNumber} />
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`templates.${globalIndex}.variationNumber`}
                            render={({ field: formField }) => (
                              <input type="hidden" {...formField} value={field.variationNumber} />
                            )}
                          />

                          {/* Hidden field for delayHours is now handled above as visible input */}
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}

            <div className="pt-4">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Follow-up Templates'}
              </Button>
            </div>
          </form>
        </Form>

        {/* Show current templates organized by sequence */}
        {sequences.some(s => s.variations.length > 0) && (
          <div className="mt-6 pt-6 border-t">
            <h4 className="text-sm font-medium mb-3">Current Templates</h4>
            <div className="space-y-4">
              {sequences.map((sequence) => (
                sequence.variations.length > 0 && (
                  <div key={sequence.sequenceNumber}>
                    <h5 className="text-sm font-medium mb-2">
                      Follow-up {sequence.sequenceNumber}
                    </h5>
                    <div className="space-y-1 ml-4">
                      {sequence.variations.map((template) => (
                        <div key={template.id} className="text-sm text-muted-foreground">
                          <strong>Variation {template.variationNumber} (After {template.delayHours}h):</strong> {template.messageText}
                        </div>
                      ))}
                    </div>
                  </div>
                )
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
