'use client';

import React from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { Button } from '@workspace/ui/components/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form';
import { Input } from '@workspace/ui/components/input';
import { Textarea } from '@workspace/ui/components/textarea';
import { useToast } from '@workspace/ui/hooks/use-toast';

const messageBatchSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255),
  messages: z.array(z.object({
    messageText: z.string().min(1, 'Message is required'),
    sequenceNumber: z.number().int().min(1).max(3),
    delayMinutes: z.number().int().min(0).max(60).default(5)
  })).min(1, 'At least one message is required').max(3, 'Maximum 3 messages allowed')
});

type MessageBatchFormValues = z.infer<typeof messageBatchSchema>;

interface MessageBatch {
  id: string;
  name: string;
  isActive: boolean;
  createdAt: string;
  MessageBatchItem: {
    id: string;
    messageText: string;
    sequenceNumber: number;
    delayMinutes: number;
  }[];
}

export function MessageBatchManager(): React.JSX.Element {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = React.useState(false);
  const [messageBatches, setMessageBatches] = React.useState<MessageBatch[]>([]);

  const form = useForm<MessageBatchFormValues>({
    resolver: zodResolver(messageBatchSchema),
    defaultValues: {
      name: '',
      messages: [
        { messageText: '', sequenceNumber: 1, delayMinutes: 5 }
      ]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'messages'
  });

  // Load existing message batches
  React.useEffect(() => {
    loadMessageBatches();
  }, []);

  const loadMessageBatches = async () => {
    try {
      const response = await fetch('/api/instagram/message-batches');
      const result = await response.json();

      if (result.success) {
        setMessageBatches(result.data);
      }
    } catch (error) {
      console.error('Error loading message batches:', error);
    }
  };

  const deleteBatch = async (batchId: string) => {
    if (!confirm('Are you sure you want to delete this message batch?')) {
      return;
    }

    try {
      const response = await fetch(`/api/instagram/message-batches?id=${batchId}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'Success',
          description: 'Message batch deleted successfully'
        });
        loadMessageBatches();
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to delete message batch',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error deleting message batch:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete message batch',
        variant: 'destructive'
      });
    }
  };

  const onSubmit = async (values: MessageBatchFormValues) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/instagram/message-batches', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'Success',
          description: 'Message batch created successfully'
        });

        form.reset();
        loadMessageBatches();
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to create message batch',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error creating message batch:', error);
      toast({
        title: 'Error',
        description: 'Failed to create message batch',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addMessage = () => {
    if (fields.length < 3) {
      append({
        messageText: '',
        sequenceNumber: fields.length + 1,
        delayMinutes: 5
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Create New Message Batch */}
      <Card>
        <CardHeader>
          <CardTitle>Create Message Batch</CardTitle>
          <CardDescription>
            Create a sequence of 1-3 messages to send to new followers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Batch Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Friendly Approach" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Messages</h4>
                  {fields.length < 3 && (
                    <Button type="button" variant="outline" size="sm" onClick={addMessage}>
                      Add Message
                    </Button>
                  )}
                </div>

                {fields.map((field, index) => (
                  <div key={field.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <h5 className="text-sm font-medium">Message {index + 1}</h5>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => remove(index)}
                        >
                          Remove
                        </Button>
                      )}
                    </div>

                    <FormField
                      control={form.control}
                      name={`messages.${index}.messageText`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Textarea
                              placeholder="Enter your message..."
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />


                  </div>
                ))}
              </div>

              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Creating...' : 'Create Message Batch'}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Existing Message Batches */}
      <Card>
        <CardHeader>
          <CardTitle>Existing Message Batches</CardTitle>
        </CardHeader>
        <CardContent>
          {messageBatches.length === 0 ? (
            <p className="text-muted-foreground">No message batches created yet.</p>
          ) : (
            <div className="space-y-4">
              {messageBatches.map((batch) => (
                <div key={batch.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{batch.name}</h4>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => deleteBatch(batch.id)}
                    >
                      Delete
                    </Button>
                  </div>
                  <div className="mt-2 space-y-2">
                    {batch.MessageBatchItem.map((item) => (
                      <div key={item.id} className="text-sm text-muted-foreground">
                        <strong>Message {item.sequenceNumber}:</strong> {item.messageText}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
