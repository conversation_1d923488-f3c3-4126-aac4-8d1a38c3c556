'use client';

import * as React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { type LucideIcon } from 'lucide-react';

import { cn } from '@workspace/ui/lib/utils';

export interface TabsNavigationItem {
  label: string;
  href: string;
  icon: LucideIcon;
}

export interface TabsNavigationProps {
  tabs: TabsNavigationItem[];
}

export function TabsNavigation({ tabs }: TabsNavigationProps): React.JSX.Element {
  const pathname = usePathname();

  return (
    <div className="border-b">
      <nav className="flex space-x-8" aria-label="Tabs">
        {tabs.map((tab) => {
          const isActive = pathname === tab.href || 
            (tab.href !== '/' && pathname.startsWith(tab.href));
          
          return (
            <Link
              key={tab.href}
              href={tab.href}
              className={cn(
                'group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm',
                isActive
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
              )}
              aria-current={isActive ? 'page' : undefined}
            >
              <tab.icon
                className={cn(
                  'mr-2 h-4 w-4',
                  isActive
                    ? 'text-primary'
                    : 'text-muted-foreground group-hover:text-foreground'
                )}
                aria-hidden="true"
              />
              {tab.label}
            </Link>
          );
        })}
      </nav>
    </div>
  );
}