'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { Button } from '@workspace/ui/components/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@workspace/ui/components/form';
import { Input } from '@workspace/ui/components/input';
import { useToast } from '@workspace/ui/hooks/use-toast';

const testFollowersSchema = z.object({
  count: z.number().min(1).max(50).default(10)
});

type TestFollowersFormValues = z.infer<typeof testFollowersSchema>;

export function TestInstagramFollowers(): React.JSX.Element {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = React.useState(false);

  const form = useForm<TestFollowersFormValues>({
    resolver: zodResolver(testFollowersSchema),
    defaultValues: {
      count: 10
    }
  });

  const onSubmit = async (values: TestFollowersFormValues) => {
    setIsLoading(true);
    try {
      // Generate test followers data
      const testFollowers = Array.from({ length: values.count }, (_, index) => ({
        instagramNickname: `test_user_${Date.now()}_${index + 1}`,
        instagramId: `${Date.now()}${index + 1}`,
        avatar: `https://picsum.photos/150/150?random=${Date.now()}_${index + 1}`,
        followerCount: Math.floor(Math.random() * 10000) + 100,
        isVerified: Math.random() > 0.8 // 20% chance of being verified
      }));

      // Call the API endpoint
      const response = await fetch('/api/instagram/followers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          followers: testFollowers,
          prioritizeNew: false
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'Success',
          description: result.message || `Successfully created ${result.created} test followers`
        });

        // Reset form
        form.reset();
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to create test followers',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error creating test followers:', error);
      toast({
        title: 'Error',
        description: 'Failed to create test followers',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Test Instagram Followers API</CardTitle>
        <CardDescription>
          Test the Instagram followers API endpoint by creating fake followers with random data.
          This calls the POST /api/instagram/followers endpoint to simulate the Chrome extension functionality.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="count"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Number of Test Followers</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={1}
                      max={50}
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value, 10) || 1)}
                    />
                  </FormControl>
                  <FormDescription>
                    How many test followers to create via API (1-50). Each follower will have:
                    <br />• Random username and Instagram ID
                    <br />• Random avatar image from Picsum
                    <br />• Random follower count (100-10,000)
                    <br />• 20% chance of being verified
                    <br />• Tests the same endpoint used by the Chrome extension
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Testing API...' : 'Test API - Create Followers'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
