'use client';

import * as React from 'react';
import { Button } from '@workspace/ui/components/button';
import { useToast } from '@workspace/ui/hooks/use-toast';
import { Download, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@workspace/ui/components/dialog';
import { Input } from '@workspace/ui/components/input';
import { Label } from '@workspace/ui/components/label';

import { fetchFollowersFromAPI } from '~/actions/instagram/fetch-followers-from-api';

export function FetchFollowersButton(): React.JSX.Element {
  const [isOpen, setIsOpen] = React.useState(false);
  const [isFetching, setIsFetching] = React.useState(false);
  const [limit, setLimit] = React.useState('100');
  const { toast } = useToast();
  const router = useRouter();

  const handleFetch = async () => {
    setIsFetching(true);

    try {
      const result = await fetchFollowersFromAPI(limit ? parseInt(limit) : undefined);

      if (result.success) {
        toast({
          title: 'Followers fetched successfully',
          description: result.message,
        });
        
        // Close dialog and refresh
        setIsOpen(false);
        router.refresh();
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to fetch followers',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsFetching(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Download className="mr-2 h-4 w-4" />
          Fetch from Instagram
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Fetch Followers from Instagram</DialogTitle>
          <DialogDescription>
            Fetch your Instagram followers directly from the Instagram API. 
            This will add new followers to your database without duplicating existing ones.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="limit" className="text-right">
              Limit
            </Label>
            <Input
              id="limit"
              type="number"
              value={limit}
              onChange={(e) => setLimit(e.target.value)}
              placeholder="Leave empty for all"
              className="col-span-3"
            />
          </div>
          <p className="text-sm text-muted-foreground">
            Enter a number to limit how many followers to fetch, or leave empty to fetch all followers.
          </p>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isFetching}
          >
            Cancel
          </Button>
          <Button
            onClick={handleFetch}
            disabled={isFetching}
          >
            {isFetching ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Fetching...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Fetch Followers
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}