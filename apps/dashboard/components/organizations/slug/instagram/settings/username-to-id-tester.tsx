'use client';

import * as React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Loader2, Search, Copy, CheckCircle, AlertCircle, User, Users, Eye, EyeOff, ExternalLink } from 'lucide-react';

import { Button } from '@workspace/ui/components/button';
import { Badge } from '@workspace/ui/components/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@workspace/ui/components/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@workspace/ui/components/form';
import { Input } from '@workspace/ui/components/input';
import { useToast } from '@workspace/ui/hooks/use-toast';
import { Alert, AlertDescription } from '@workspace/ui/components/alert';

// Remove direct import since we'll use API endpoint

const usernameSchema = z.object({
  username: z.string().min(1, 'Username is required').regex(/^[a-zA-Z0-9._]+$/, 'Invalid Instagram username format')
});

type UsernameFormValues = z.infer<typeof usernameSchema>;

interface UserInfo {
  userId: string;
  username: string;
  fullName: string;
  followers: number;
  following: number;
  posts: number;
  isVerified: boolean;
  isPrivate: boolean;
  isBusiness: boolean;
  biography: string;
  externalUrl?: string;
  profilePicUrl: string;
}

export function UsernameToIdTester(): React.JSX.Element {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = React.useState(false);
  const [userInfo, setUserInfo] = React.useState<UserInfo | null>(null);
  const [error, setError] = React.useState<string | null>(null);
  const [copiedField, setCopiedField] = React.useState<string | null>(null);

  const form = useForm<UsernameFormValues>({
    resolver: zodResolver(usernameSchema),
    defaultValues: {
      username: ''
    }
  });

  const onSubmit = async (values: UsernameFormValues) => {
    setIsLoading(true);
    setError(null);
    setUserInfo(null);

    try {
      const response = await fetch('/api/instagram/username-to-id', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: values.username
        })
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.details || result.error || 'API request failed');
      }

      setUserInfo({
        userId: result.data.userId,
        username: result.data.username,
        fullName: 'N/A', // Basic API doesn't return full profile info
        followers: 0,
        following: 0,
        posts: 0,
        isVerified: false,
        isPrivate: false,
        isBusiness: false,
        biography: '',
        profilePicUrl: ''
      });

      toast({
        title: 'Success!',
        description: `Found Instagram ID for @${values.username}`,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setError(errorMessage);

      toast({
        title: 'Error',
        description: `Failed to get Instagram ID: ${errorMessage}`,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);

      toast({
        title: 'Copied!',
        description: `${fieldName} copied to clipboard`,
      });

      // Reset copied state after 2 seconds
      setTimeout(() => setCopiedField(null), 2000);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to copy to clipboard',
        variant: 'destructive'
      });
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="h-5 w-5" />
          Instagram Username to ID Converter
        </CardTitle>
        <CardDescription>
          Test tool to convert Instagram usernames to numeric user IDs using profile page parsing method (more reliable than API endpoints)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Instagram Username</FormLabel>
                  <div className="flex gap-2">
                    <FormControl>
                      <Input
                        placeholder="alexgodlewsky"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <Button type="submit" disabled={isLoading || !field.value}>
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Search className="h-4 w-4" />
                      )}
                      {isLoading ? 'Looking up...' : 'Get ID'}
                    </Button>
                  </div>
                  <FormDescription>
                    Enter an Instagram username (without @) to get the numeric user ID
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Error:</strong> {error}
              <br />
              <span className="text-sm text-muted-foreground mt-1 block">
                This might be due to rate limiting, invalid username, or private account.
              </span>
            </AlertDescription>
          </Alert>
        )}

        {userInfo && (
          <div className="space-y-4">
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Successfully found Instagram user ID!
              </AlertDescription>
            </Alert>

            <div className="grid gap-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">Instagram User ID</div>
                  <div className="text-sm text-muted-foreground font-mono">{userInfo.userId}</div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(userInfo.userId, 'Instagram ID')}
                >
                  {copiedField === 'Instagram ID' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">Username</div>
                  <div className="text-sm text-muted-foreground">@{userInfo.username}</div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(userInfo.username, 'Username')}
                >
                  {copiedField === 'Username' ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Usage Instructions:</h4>
              <ol className="text-sm text-muted-foreground space-y-1 list-decimal list-inside">
                <li>Copy the Instagram User ID above</li>
                <li>Use this numeric ID in your test data instead of fake IDs</li>
                <li>This ID can be used for Instagram API calls that require user_id parameter</li>
                <li>Store this ID in your InstagramContact.instagramId field</li>
              </ol>
            </div>

            <div className="p-4 border-l-4 border-blue-500 bg-blue-50">
              <h4 className="font-medium text-blue-900 mb-1">API Integration Note:</h4>
              <p className="text-sm text-blue-800">
                This numeric ID should be used when calling Instagram APIs that require the user_id parameter,
                such as conversation history endpoints.
              </p>
            </div>
          </div>
        )}

        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>Method:</strong> This tool fetches Instagram profile pages and extracts user IDs from HTML (based on ricardojoserf/instagram-user-id).</p>
          <p><strong>Rate Limits:</strong> Instagram may rate limit requests. Wait a few minutes between tests if you encounter errors.</p>
          <p><strong>Privacy:</strong> Only works with public Instagram accounts. Private accounts will return "not found".</p>
        </div>
      </CardContent>
    </Card>
  );
}
