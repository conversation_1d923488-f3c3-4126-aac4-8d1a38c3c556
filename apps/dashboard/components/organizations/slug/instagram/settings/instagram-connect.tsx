'use client';

import * as React from 'react';
import { Button } from '@workspace/ui/components/button';
import { Badge } from '@workspace/ui/components/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@workspace/ui/components/avatar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Instagram, CheckCircle, AlertTriangle, ExternalLink } from 'lucide-react';
import { useToast } from '@workspace/ui/hooks/use-toast';
import { useActiveOrganization } from '~/hooks/use-active-organization';

interface InstagramConnectProps {
  isConnected?: boolean;
  accountInfo?: {
    username?: string;
    name?: string;
    profilePicture?: string;
    accountId?: string;
  };
  tokenExpiresAt?: Date;
  onDisconnect?: () => void;
}

export function InstagramConnect({ 
  isConnected = false, 
  accountInfo,
  tokenExpiresAt,
  onDisconnect 
}: InstagramConnectProps): React.JSX.Element {
  const { toast } = useToast();
  const organization = useActiveOrganization();
  const [isConnecting, setIsConnecting] = React.useState(false);
  const [isDisconnecting, setIsDisconnecting] = React.useState(false);

  // Instagram OAuth URL with organization slug as state
  const getInstagramOAuthUrl = () => {
    const params = new URLSearchParams({
      force_reauth: 'true',
      client_id: '*****************',
      redirect_uri: 'https://app.aisetter.pl/api/instagram/oauth/callback',
      response_type: 'code',
      scope: 'instagram_business_basic,instagram_business_manage_messages,instagram_business_manage_comments,instagram_business_content_publish,instagram_business_manage_insights',
      state: organization?.slug || ''
    });
    
    return `https://www.instagram.com/oauth/authorize?${params.toString()}`;
  };

  const handleConnect = () => {
    if (!organization?.slug) {
      toast({
        title: 'Error',
        description: 'Unable to determine organization context',
        variant: 'destructive'
      });
      return;
    }

    setIsConnecting(true);
    
    // Open Instagram OAuth in new window
    const oauthUrl = getInstagramOAuthUrl();
    const popup = window.open(
      oauthUrl,
      'instagram-oauth',
      'width=600,height=700,scrollbars=yes,resizable=yes'
    );

    // Monitor the popup for completion
    const checkClosed = setInterval(() => {
      if (popup?.closed) {
        clearInterval(checkClosed);
        setIsConnecting(false);
        
        // Refresh the page to show updated connection status
        window.location.reload();
      }
    }, 1000);

    // Fallback timeout
    setTimeout(() => {
      if (!popup?.closed) {
        clearInterval(checkClosed);
        setIsConnecting(false);
        toast({
          title: 'Connection timeout',
          description: 'Please try connecting again',
          variant: 'destructive'
        });
      }
    }, 300000); // 5 minutes timeout
  };

  const handleDisconnect = async () => {
    if (!onDisconnect) return;
    
    setIsDisconnecting(true);
    try {
      await onDisconnect();
      toast({
        title: 'Disconnected',
        description: 'Instagram account has been disconnected'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to disconnect Instagram account',
        variant: 'destructive'
      });
    } finally {
      setIsDisconnecting(false);
    }
  };

  const isTokenExpired = tokenExpiresAt && new Date(tokenExpiresAt) < new Date();
  const isTokenExpiringSoon = tokenExpiresAt && new Date(tokenExpiresAt) < new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

  if (isConnected && accountInfo) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Instagram className="h-5 w-5" />
            Instagram Account
            {isTokenExpired ? (
              <Badge variant="destructive">Token Expired</Badge>
            ) : isTokenExpiringSoon ? (
              <Badge variant="outline" className="text-orange-600">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Expires Soon
              </Badge>
            ) : (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Connected
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Your Instagram business account is connected and ready to use
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-3">
            <Avatar>
              <AvatarImage src={accountInfo.profilePicture} alt={accountInfo.username} />
              <AvatarFallback>
                <Instagram className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium">@{accountInfo.username}</p>
              {accountInfo.name && (
                <p className="text-sm text-muted-foreground">{accountInfo.name}</p>
              )}
              <p className="text-xs text-muted-foreground">ID: {accountInfo.accountId}</p>
            </div>
          </div>

          {tokenExpiresAt && (
            <div className="text-sm text-muted-foreground">
              Token expires: {new Date(tokenExpiresAt).toLocaleDateString()}
            </div>
          )}

          <div className="flex gap-2">
            {(isTokenExpired || isTokenExpiringSoon) && (
              <Button 
                onClick={handleConnect}
                disabled={isConnecting}
                variant="default"
              >
                {isConnecting ? 'Reconnecting...' : 'Refresh Token'}
              </Button>
            )}
            
            <Button 
              onClick={handleDisconnect}
              disabled={isDisconnecting}
              variant="outline"
            >
              {isDisconnecting ? 'Disconnecting...' : 'Disconnect'}
            </Button>
            
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => window.open(`https://instagram.com/${accountInfo.username}`, '_blank')}
            >
              <ExternalLink className="h-4 w-4 mr-1" />
              View Profile
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Instagram className="h-5 w-5" />
          Connect Instagram Account
        </CardTitle>
        <CardDescription>
          Connect your Instagram business account to enable bot functionality
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            <p>To connect your Instagram account, you'll need:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>An Instagram Business or Creator account</li>
              <li>Admin access to manage messages</li>
              <li>The account should be eligible for the Instagram API</li>
            </ul>
          </div>
          
          <Button 
            onClick={handleConnect}
            disabled={isConnecting}
            className="w-full"
          >
            {isConnecting ? (
              'Connecting...'
            ) : (
              <>
                <Instagram className="h-4 w-4 mr-2" />
                Connect Instagram Account
              </>
            )}
          </Button>
          
          <p className="text-xs text-muted-foreground text-center">
            By connecting, you agree to Instagram's API Terms of Service
          </p>
        </div>
      </CardContent>
    </Card>
  );
}