'use client';

import * as React from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';
import { Button } from '@workspace/ui/components/button';
import { Input } from '@workspace/ui/components/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@workspace/ui/components/table';
import { Trash2, ArrowUpDown } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useToast } from '@workspace/ui/hooks/use-toast';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';

interface ConversationNotGathered {
  id: string;
  instagramConversationId: string;
  participantUsername: string;
  participantId: string;
  updatedTime: Date;
  isGathered: boolean;
  createdAt: Date;
}

interface ConversationsNotGatheredTableProps {
  conversations: ConversationNotGathered[];
  total: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

export function ConversationsNotGatheredTable({ 
  conversations,
  total,
  totalPages,
  currentPage,
  pageSize
}: ConversationsNotGatheredTableProps): React.JSX.Element {
  const { toast } = useToast();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isClearing, setIsClearing] = React.useState(false);
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);

  const handleClearAll = async () => {
    if (!confirm('Are you sure you want to clear all conversations? This action cannot be undone.')) {
      return;
    }

    setIsClearing(true);
    try {
      const response = await fetch('/api/instagram/conversations-not-gathered/clear', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'All conversations cleared successfully',
        });
        // Refresh the page to update the table
        router.refresh();
      } else {
        throw new Error('Failed to clear conversations');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to clear conversations. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsClearing(false);
    }
  };

  const columns: ColumnDef<ConversationNotGathered>[] = [
    {
      accessorKey: 'participantUsername',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Username
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return <div className="font-medium">@{row.getValue('participantUsername')}</div>;
      },
    },
    {
      accessorKey: 'participantId',
      header: 'Participant ID',
      cell: ({ row }) => {
        return (
          <code className="text-xs bg-muted px-1 py-0.5 rounded">
            {row.getValue('participantId')}
          </code>
        );
      },
    },
    {
      accessorKey: 'instagramConversationId',
      header: 'Conversation ID',
      cell: ({ row }) => {
        return (
          <code className="text-xs bg-muted px-1 py-0.5 rounded break-all">
            {row.getValue('instagramConversationId')}
          </code>
        );
      },
    },
    {
      accessorKey: 'updatedTime',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Last Updated
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return (
          <div className="text-sm text-muted-foreground">
            {formatDistanceToNow(new Date(row.getValue('updatedTime')), { addSuffix: true })}
          </div>
        );
      },
    },
    {
      accessorKey: 'isGathered',
      header: 'Status',
      cell: ({ row }) => {
        const isGathered = row.getValue('isGathered') as boolean;
        return (
          <Badge variant={isGathered ? 'default' : 'secondary'}>
            {isGathered ? 'Gathered' : 'Not Gathered'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Discovered
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        return (
          <div className="text-sm text-muted-foreground">
            {formatDistanceToNow(new Date(row.getValue('createdAt')), { addSuffix: true })}
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: conversations,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
    },
    initialState: {
      pagination: {
        pageSize: pageSize,
      },
    },
  });

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', newPage.toString());
    router.push(`${pathname}?${params.toString()}`);
  };

  if (conversations.length === 0 && total === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Instagram Conversations Not Gathered</CardTitle>
          <CardDescription>
            No conversations found. Run the conversation gathering test to populate this table.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="space-y-1">
          <CardTitle>Instagram Conversations Not Gathered</CardTitle>
          <CardDescription>
            Conversations discovered from Instagram API that haven't been processed yet ({total} total)
          </CardDescription>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleClearAll}
          disabled={isClearing}
          className="text-destructive hover:text-destructive"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          {isClearing ? 'Clearing...' : 'Clear All'}
        </Button>
      </CardHeader>
      <CardContent>
        <div className="flex items-center py-4">
          <Input
            placeholder="Filter by username..."
            value={(table.getColumn('participantUsername')?.getFilterValue() as string) ?? ''}
            onChange={(event) =>
              table.getColumn('participantUsername')?.setFilterValue(event.target.value)
            }
            className="max-w-sm"
          />
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-end space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, total)} of {total} conversation(s)
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage <= 1}
            >
              Previous
            </Button>
            <div className="flex items-center gap-1">
              <span className="text-sm">Page</span>
              <span className="text-sm font-medium">{currentPage}</span>
              <span className="text-sm">of</span>
              <span className="text-sm font-medium">{totalPages}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage >= totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}