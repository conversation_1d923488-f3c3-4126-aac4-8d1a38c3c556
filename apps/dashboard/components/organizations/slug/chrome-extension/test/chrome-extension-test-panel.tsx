'use client';

import * as React from 'react';
import { Button } from '@workspace/ui/components/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Alert, AlertDescription } from '@workspace/ui/components/alert';
import { useToast } from '@workspace/ui/hooks/use-toast';
import { Loader2, CheckCircle, XCircle, Users, MessageSquare, Target, Shuffle, Brain, Zap } from 'lucide-react';
import { testChromeExtensionFlow } from '~/actions/chrome-extension/test-flow';
import { RadioGroup, RadioGroupItem } from '@workspace/ui/components/radio-group';
import { Label } from '@workspace/ui/components/label';

interface TestResult {
  step: string;
  success: boolean;
  message: string;
  data?: any;
}

export function ChromeExtensionTestPanel(): React.JSX.Element {
  const [isRunning, setIsRunning] = React.useState(false);
  const [results, setResults] = React.useState<TestResult[]>([]);
  const { toast } = useToast();

  const testFollowers = [
    'norbert_rzepka_biznes',
    'socialflow.pl',
    'Maciej_Rak',
    'ewelinasocha_',
    'ig.wasilewski',
    'martyna_konieczna',
    'paulina_g111',
    'godles6'
  ];

  const [testType, setTestType] = React.useState<'basic' | 'conversation-gathering'>('basic');

  const runTest = async () => {
    setIsRunning(true);
    setResults([]);

    try {
      const response = testType === 'conversation-gathering'
        ? await testConversationGatheringFlow()
        : await testChromeExtensionFlow();

      if (response.success && response.results) {
        setResults(response.results);

        const failedSteps = response.results.filter((r: any) => !r.success).length;

        if (failedSteps === 0) {
          toast({
            title: 'Test completed successfully',
            description: 'All steps passed!',
          });
        } else {
          toast({
            title: 'Test completed with errors',
            description: `${failedSteps} steps failed`,
            variant: 'destructive'
          });
        }
      } else {
        toast({
          title: 'Test failed',
          description: response.error || 'Unknown error occurred',
          variant: 'destructive'
        });
      }
    } catch (error) {
      toast({
        title: 'Test error',
        description: 'Failed to run test',
        variant: 'destructive'
      });
    } finally {
      setIsRunning(false);
    }
  };

  const testConversationGatheringFlow = async () => {
    // This will call a new action for conversation gathering test
    const response = await fetch('/api/test/conversation-gathering-flow', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ testFollowers })
    });
    return await response.json();
  };

  const getStepIcon = (step: string) => {
    switch (step) {
      case 'Upload Followers':
        return <Users className="h-4 w-4" />;
      case 'Analyze Conversations':
        return <MessageSquare className="h-4 w-4" />;
      case 'Get Attack List':
        return <Target className="h-4 w-4" />;
      case 'Get Random Message':
        return <Shuffle className="h-4 w-4" />;
      case 'Conversation Gathering Analysis':
        return <MessageSquare className="h-4 w-4" />;
      case 'Process Users with NO Conversation (Priority 3 + Batch Messages)':
        return <Users className="h-4 w-4" />;
      case 'Process Users WITH Conversation (Ready for AI)':
        return <MessageSquare className="h-4 w-4" />;
      case 'Verify Attack List Sorting':
        return <Target className="h-4 w-4" />;
      case 'Test Batch Messages':
        return <Shuffle className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Chrome Extension Flow Test</CardTitle>
          <CardDescription>
            Test different aspects of the Instagram follower processing workflow.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-3">Test Type:</h4>
            <RadioGroup value={testType} onValueChange={(value) => setTestType(value as 'basic' | 'conversation-gathering')}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="basic" id="basic" />
                <Label htmlFor="basic" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Basic Flow Test
                  <span className="text-xs text-muted-foreground">(Upload, Attack List, Messages)</span>
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="conversation-gathering" id="conversation-gathering" />
                <Label htmlFor="conversation-gathering" className="flex items-center gap-2">
                  <Brain className="h-4 w-4" />
                  Conversation Gathering Test
                  <span className="text-xs text-muted-foreground">(Priority assignment, AI analysis, Follow-ups)</span>
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Test Followers:</h4>
            <div className="flex flex-wrap gap-2">
              {testFollowers.map((follower) => (
                <span key={follower} className="px-2 py-1 bg-muted rounded text-sm">
                  @{follower}
                </span>
              ))}
            </div>
          </div>

          {testType === 'conversation-gathering' && (
            <Alert>
              <Brain className="h-4 w-4" />
              <AlertDescription>
                This test follows the REAL conversation gathering workflow:
                <ul className="mt-2 ml-4 list-disc text-sm">
                  <li><strong>1. Upload followers:</strong> Chrome extension uploads followers to database</li>
                  <li><strong>2. Gather conversation list:</strong> Get all existing conversations from database</li>
                  <li><strong>3. Check followers in conversation list:</strong></li>
                  <li className="ml-4">• <strong>If NO conversation:</strong> Priority 3 → Batch messages → Attack list</li>
                  <li className="ml-4">• <strong>If YES conversation:</strong> Gather conversation → (Pass to AI) → Attack list</li>
                  <li><strong>4. Verify attack list:</strong> Proper sorting by time first, then priority 5→4→3→2→1</li>
                </ul>
              </AlertDescription>
            </Alert>
          )}

          <Button
            onClick={runTest}
            disabled={isRunning}
            className="w-full"
          >
            {isRunning ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Running {testType === 'conversation-gathering' ? 'Conversation Gathering' : 'Basic Flow'} Test...
              </>
            ) : (
              <>
                {testType === 'conversation-gathering' ? <Brain className="mr-2 h-4 w-4" /> : <Zap className="mr-2 h-4 w-4" />}
                Run {testType === 'conversation-gathering' ? 'Conversation Gathering' : 'Basic Flow'} Test
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {results.map((result, index) => (
              <div key={index} className="border-l-2 border-muted pl-4 space-y-2">
                <div className="flex items-center gap-2">
                  {result.success ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                  <div className="flex items-center gap-2">
                    {getStepIcon(result.step)}
                    <span className="font-medium">{result.step}</span>
                  </div>
                </div>

                <p className="text-sm text-muted-foreground">{result.message}</p>

                {result.data && (
                  <Alert>
                    <AlertDescription>
                      <pre className="text-xs overflow-x-auto">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
}