'use client';

import * as React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { Button } from '@workspace/ui/components/button';
import { Badge } from '@workspace/ui/components/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@workspace/ui/components/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@workspace/ui/components/form';
import { Input } from '@workspace/ui/components/input';
import { Switch } from '@workspace/ui/components/switch';
import { Slider } from '@workspace/ui/components/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@workspace/ui/components/select';
import { useToast } from '@workspace/ui/hooks/use-toast';

const chromeExtensionSettingsSchema = z.object({
  timeBetweenDMsRange: z.array(z.number().min(1).max(99)).length(2).default([3, 8]),
  messagesBeforeBreakRange: z.array(z.number().min(1).max(99)).length(2).default([8, 15]),
  breakDurationRange: z.array(z.number().min(1).max(99)).length(2).default([10, 20]),
  pauseStart: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).default("00:30"),
  pauseStop: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).default("07:00"),
  smartFocus: z.boolean().default(true),
}).refine((data) => {
  return data.timeBetweenDMsRange[0] <= data.timeBetweenDMsRange[1] &&
         data.messagesBeforeBreakRange[0] <= data.messagesBeforeBreakRange[1] &&
         data.breakDurationRange[0] <= data.breakDurationRange[1];
}, {
  message: "Minimum values must be less than or equal to maximum values",
});

type ChromeExtensionSettingsFormValues = z.infer<typeof chromeExtensionSettingsSchema>;

interface ConnectionStatus {
  isConnected: boolean;
  lastConnectionAt?: string | null;
  extensionStatus?: string;
  currentActivity?: string | null;
  lastActivityAt?: string | null;
}

interface ScrapingStatus {
  isEligible: boolean;
  nextScrapingAllowedAt?: string | null;
  waitTimeHuman?: string;
  totalFollowersScraped?: number;
  lastScrapingSession?: string | null;
  scrapingIntervalDays?: number;
  canReset?: boolean;
}

// Generate time options in 15-minute intervals
const generateTimeOptions = (): string[] => {
  const times: string[] = [];
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 15) {
      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      times.push(timeString);
    }
  }
  return times;
};

export function ChromeExtensionSettingsForm(): React.JSX.Element {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = React.useState(false);
  const [isResetting, setIsResetting] = React.useState(false);
  const [connectionStatus, setConnectionStatus] = React.useState<ConnectionStatus>({
    isConnected: false,
    lastConnectionAt: null
  });
  const [scrapingStatus, setScrapingStatus] = React.useState<ScrapingStatus>({
    isEligible: true,
    canReset: false
  });

  const form = useForm<ChromeExtensionSettingsFormValues>({
    resolver: zodResolver(chromeExtensionSettingsSchema),
    defaultValues: {
      timeBetweenDMsRange: [3, 8],
      messagesBeforeBreakRange: [8, 15],
      breakDurationRange: [10, 20],
      pauseStart: "00:30",
      pauseStop: "07:00",
      smartFocus: true,
    }
  });

  // Load scraping status
  const loadScrapingStatus = async () => {
    try {
      const response = await fetch('/api/admin/reset-scraping-interval');
      const result = await response.json();

      if (result.success) {
        setScrapingStatus(result.data);
      }
    } catch (error) {
      console.error('Error loading scraping status:', error);
    }
  };

  // Load settings on component mount
  React.useEffect(() => {
    const loadSettings = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/chrome-extension/settings');
        const result = await response.json();

        if (result.success) {
          form.reset({
            timeBetweenDMsRange: [result.data.timeBetweenDMsMin, result.data.timeBetweenDMsMax],
            messagesBeforeBreakRange: [result.data.messagesBeforeBreakMin, result.data.messagesBeforeBreakMax],
            breakDurationRange: [result.data.breakDurationMin, result.data.breakDurationMax],
            pauseStart: result.data.pauseStart,
            pauseStop: result.data.pauseStop,
            smartFocus: result.data.smartFocus,
          });
          setConnectionStatus({
            isConnected: result.data.isConnected,
            lastConnectionAt: result.data.lastConnectionAt,
            extensionStatus: result.data.extensionStatus,
            currentActivity: result.data.currentActivity,
            lastActivityAt: result.data.lastActivityAt
          });
        }
      } catch (error) {
        console.error('Error loading settings:', error);
        toast({
          title: 'Error',
          description: 'Failed to load settings.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
    loadScrapingStatus();
  }, [form, toast]);

  const onSubmit = async (values: ChromeExtensionSettingsFormValues) => {
    try {
      setIsLoading(true);

      const requestData = {
        timeBetweenDMsMin: values.timeBetweenDMsRange[0],
        timeBetweenDMsMax: values.timeBetweenDMsRange[1],
        messagesBeforeBreakMin: values.messagesBeforeBreakRange[0],
        messagesBeforeBreakMax: values.messagesBeforeBreakRange[1],
        breakDurationMin: values.breakDurationRange[0],
        breakDurationMax: values.breakDurationRange[1],
        pauseStart: values.pauseStart,
        pauseStop: values.pauseStop,
        smartFocus: values.smartFocus,
      };

      const response = await fetch('/api/chrome-extension/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to save settings');
      }

      toast({
        title: 'Settings saved',
        description: 'Chrome Extension settings have been updated successfully.'
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetScrapingInterval = async () => {
    try {
      setIsResetting(true);

      const response = await fetch('/api/admin/reset-scraping-interval', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to reset scraping interval');
      }

      toast({
        title: 'Scraping interval reset',
        description: result.message || 'You can now scrape immediately.'
      });

      // 🚨 ENHANCEMENT: Notify Chrome extension to clear local storage if needed
      if (result.data?.shouldClearExtensionWait) {
        try {
          // Send message to Chrome extension to clear local storage
          if (typeof window !== 'undefined' && window.chrome?.runtime?.sendMessage) {
            await window.chrome.runtime.sendMessage({ type: 'clearScrapingWait' });
            console.log('✅ Notified Chrome extension to clear local scraping wait');
          }
        } catch (error) {
          console.log('ℹ️ Could not notify Chrome extension (extension may not be active):', error);
          // This is not critical - the extension will sync on next API call anyway
        }
      }

      // Reload scraping status
      await loadScrapingStatus();
    } catch (error) {
      console.error('Error resetting scraping interval:', error);
      toast({
        title: 'Error',
        description: 'Failed to reset scraping interval. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Chrome Extension Configuration</CardTitle>
              <CardDescription>
                Configure general settings for the Instagram Chrome Extension
              </CardDescription>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground mb-1">Connection Status:</div>
              <Badge variant={connectionStatus.isConnected ? "default" : "secondary"}>
                {connectionStatus.isConnected ? "CONNECTED" : "DISCONNECTED"}
              </Badge>
              {connectionStatus.lastConnectionAt && (
                <div className="text-xs text-muted-foreground mt-1">
                  Last: {new Date(connectionStatus.lastConnectionAt).toLocaleString()}
                </div>
              )}

              {connectionStatus.extensionStatus && (
                <div className="mt-3">
                  <div className="text-sm text-muted-foreground mb-1">Extension Status:</div>
                  <Badge variant="outline">
                    {connectionStatus.extensionStatus.replace('_', ' ')}
                  </Badge>
                  {connectionStatus.currentActivity && (
                    <div className="text-xs text-muted-foreground mt-1">
                      {connectionStatus.currentActivity}
                    </div>
                  )}
                  {connectionStatus.lastActivityAt && (
                    <div className="text-xs text-muted-foreground mt-1">
                      Last activity: {new Date(connectionStatus.lastActivityAt).toLocaleString()}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="timeBetweenDMsRange"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Time between DMs (minutes)</FormLabel>
                  <FormControl>
                    <div className="px-3">
                      <Slider
                        min={1}
                        max={99}
                        step={1}
                        value={field.value}
                        onValueChange={field.onChange}
                        className="w-full"
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Range: {field.value[0]} - {field.value[1]} minutes
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="messagesBeforeBreakRange"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>How many DMs before break</FormLabel>
                  <FormControl>
                    <div className="px-3">
                      <Slider
                        min={1}
                        max={99}
                        step={1}
                        value={field.value}
                        onValueChange={field.onChange}
                        className="w-full"
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Range: {field.value[0]} - {field.value[1]} messages
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="breakDurationRange"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>How much break should take? (minutes)</FormLabel>
                  <FormControl>
                    <div className="px-3">
                      <Slider
                        min={1}
                        max={99}
                        step={1}
                        value={field.value}
                        onValueChange={field.onChange}
                        className="w-full"
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Range: {field.value[0]} - {field.value[1]} minutes
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="pauseStart"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Natural pause start</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select start time" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {generateTimeOptions().map((time) => (
                          <SelectItem key={time} value={time}>
                            {time}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      When to start natural pause (e.g., 00:30)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pauseStop"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Natural pause stop</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select stop time" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {generateTimeOptions().map((time) => (
                          <SelectItem key={time} value={time}>
                            {time}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      When to stop natural pause (e.g., 07:00)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="smartFocus"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      Focus on new followers
                    </FormLabel>
                    <FormDescription>
                      Prioritize messaging new followers over existing contacts
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <CardFooter className="px-0">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Settings'}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>

    {/* Scraping Interval Reset Section */}
    <Card>
      <CardHeader>
        <CardTitle>Follower Scraping Control</CardTitle>
        <CardDescription>
          Manage the 5-day interval between follower scraping sessions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm font-medium">Scraping Status</div>
            <Badge variant={scrapingStatus.isEligible ? "default" : "destructive"}>
              {scrapingStatus.isEligible ? "✅ Ready to Scrape" : "⏳ Waiting"}
            </Badge>
            {scrapingStatus.totalFollowersScraped !== undefined && (
              <div className="text-xs text-muted-foreground">
                Total scraped: {scrapingStatus.totalFollowersScraped} followers
              </div>
            )}
          </div>

          <div className="space-y-2">
            <div className="text-sm font-medium">Next Allowed</div>
            {scrapingStatus.nextScrapingAllowedAt ? (
              <div className="text-sm">
                <div>{new Date(scrapingStatus.nextScrapingAllowedAt).toLocaleString()}</div>
                {scrapingStatus.waitTimeHuman && (
                  <div className="text-xs text-muted-foreground">
                    Wait: {scrapingStatus.waitTimeHuman}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-sm text-green-600">Now</div>
            )}
          </div>
        </div>

        {scrapingStatus.lastScrapingSession && (
          <div className="pt-2 border-t">
            <div className="text-sm font-medium mb-1">Last Scraping Session</div>
            <div className="text-sm text-muted-foreground">
              {new Date(scrapingStatus.lastScrapingSession).toLocaleString()}
            </div>
          </div>
        )}

        {!scrapingStatus.isEligible && (
          <div className="pt-4 border-t">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium">Emergency Reset</div>
                <div className="text-xs text-muted-foreground">
                  Reset the 5-day wait period to allow immediate scraping
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetScrapingInterval}
                disabled={isResetting}
              >
                {isResetting ? 'Resetting...' : 'Reset Wait Period'}
              </Button>
            </div>
          </div>
        )}

        {scrapingStatus.isEligible && scrapingStatus.totalFollowersScraped && scrapingStatus.totalFollowersScraped > 0 && (
          <div className="pt-4 border-t">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm font-medium">Reset Counter</div>
                <div className="text-xs text-muted-foreground">
                  Reset follower count and scraping history
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetScrapingInterval}
                disabled={isResetting}
              >
                {isResetting ? 'Resetting...' : 'Reset Counter'}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  </div>
  );
}
