import { z } from 'zod';

export const keys = () => ({
  server: {
    CRON_API_KEY: z.string().min(1),
    ANTHROPIC_API_KEY: z.string().min(1).optional(),
    CLAUDE_MODEL: z.string().default('claude-sonnet-4-20250514'),
    OPENROUTER_API_KEY: z.string().min(1).optional()
  },
  runtimeEnv: {
    CRON_API_KEY: process.env.CRON_API_KEY,
    ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,
    CLAUDE_MODEL: process.env.CLAUDE_MODEL,
    OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY
  }
});
