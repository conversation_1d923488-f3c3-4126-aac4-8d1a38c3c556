import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { followerProcessingQueue } from '~/lib/follower-processing-queue';

/**
 * Manual trigger for follower processing queue
 * GET: Get queue stats
 * POST: Process queue manually
 */

export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get queue statistics
    const stats = await followerProcessingQueue.getStats();

    return NextResponse.json({
      success: true,
      data: {
        stats,
        isProcessing: followerProcessingQueue['isProcessing'] || false
      }
    });

  } catch (error) {
    console.error('Error getting queue stats:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { action } = body;

    switch (action) {
      case 'process':
        // Manually trigger queue processing
        await followerProcessingQueue.processQueue();
        return NextResponse.json({
          success: true,
          message: 'Queue processing triggered'
        });

      case 'start':
        // Start the queue processor
        await followerProcessingQueue.start();
        return NextResponse.json({
          success: true,
          message: 'Queue processor started'
        });

      case 'stop':
        // Stop the queue processor
        await followerProcessingQueue.stop();
        return NextResponse.json({
          success: true,
          message: 'Queue processor stopped'
        });

      case 'cleanup':
        // Clean up old items
        const cleanupDays = body.days || 7;
        const cleanedCount = await followerProcessingQueue.cleanup(cleanupDays);
        return NextResponse.json({
          success: true,
          message: `Cleaned up ${cleanedCount} old items`
        });

      case 'retry':
        // Retry failed items
        const retriedCount = await followerProcessingQueue.retryFailed();
        return NextResponse.json({
          success: true,
          message: `Retrying ${retriedCount} failed items`
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error processing queue action:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
