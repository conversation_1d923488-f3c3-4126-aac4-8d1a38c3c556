import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

/**
 * Get follower processing queue statistics
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Get queue statistics
    const stats = await prisma.followerProcessingQueue.groupBy({
      by: ['status'],
      where: {
        organizationId
      },
      _count: {
        id: true
      }
    });

    // Get processing type breakdown
    const typeStats = await prisma.followerProcessingQueue.groupBy({
      by: ['processingType'],
      where: {
        organizationId,
        status: 'pending'
      },
      _count: {
        id: true
      }
    });

    // Get recent queue items
    const recentItems = await prisma.followerProcessingQueue.findMany({
      where: {
        organizationId
      },
      include: {
        InstagramFollower: {
          select: {
            instagramNickname: true,
            avatar: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    // Format stats
    const formattedStats = {
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0
    };

    stats.forEach(stat => {
      formattedStats[stat.status as keyof typeof formattedStats] = stat._count.id;
    });

    const formattedTypeStats = {
      batch: 0,
      conversation: 0
    };

    typeStats.forEach(stat => {
      formattedTypeStats[stat.processingType as keyof typeof formattedTypeStats] = stat._count.id;
    });

    return NextResponse.json({
      success: true,
      data: {
        stats: formattedStats,
        typeStats: formattedTypeStats,
        recentItems: recentItems.map(item => ({
          id: item.id,
          status: item.status,
          processingType: item.processingType,
          hasConversation: item.hasConversation,
          priority: item.priority,
          attempts: item.attempts,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
          errorMessage: item.errorMessage,
          follower: {
            nickname: item.InstagramFollower.instagramNickname,
            avatar: item.InstagramFollower.avatar
          }
        }))
      }
    });

  } catch (error) {
    console.error('Error getting queue stats:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
