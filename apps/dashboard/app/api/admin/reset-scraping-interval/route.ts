import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

/**
 * Reset scraping interval counter - Admin only
 * This allows immediate scraping by clearing the nextScrapingAllowedAt field
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;
    let userId: string | undefined;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const { verifyApiKey } = await import('@workspace/api-keys');
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401 }
        );
      }
      organizationId = result.organizationId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }

      userId = session.user.id;

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404 }
        );
      }

      organizationId = membership.organizationId;
    }

    // Get current settings before reset
    const currentSettings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    // Reset the scraping interval counter
    const updatedSettings = await prisma.chromeExtensionSettings.upsert({
      where: { organizationId },
      update: {
        nextScrapingAllowedAt: null, // Clear the wait time
        extensionStatus: 'FRESH_START', // Reset status
        currentActivity: 'Scraping interval reset - ready to scrape',
        lastActivityAt: new Date(),
        updatedAt: new Date(),
        // Reset all scraping counters and history
        totalFollowersScraped: 0,
        lastScrapedPosition: 0,
        lastScrapedUsernames: [],
        scrapingTargetReached: false,
        allFollowersScraped: false,
        lastScrapingSession: null
      },
      create: {
        organizationId,
        nextScrapingAllowedAt: null,
        extensionStatus: 'FRESH_START',
        currentActivity: 'Scraping interval reset - ready to scrape',
        lastActivityAt: new Date(),
        scrapingIntervalDays: 5,
        // Default settings
        timeBetweenDMsMin: 3,
        timeBetweenDMsMax: 8,
        messagesBeforeBreakMin: 8,
        messagesBeforeBreakMax: 15,
        breakDurationMin: 10,
        breakDurationMax: 20,
        pauseStart: "00:30",
        pauseStop: "07:00",
        smartFocus: true,
        isConnected: false,
        totalFollowersScraped: 0,
        lastScrapedPosition: 0,
        lastScrapedUsernames: [],
        scrapingTargetReached: false,
        allFollowersScraped: false
      }
    });

    // Log the reset action
    console.log(`🔄 Scraping interval reset by user ${userId || 'API'} for organization ${organizationId}`);
    console.log(`Previous state: nextScrapingAllowedAt = ${currentSettings?.nextScrapingAllowedAt}`);
    console.log(`New state: nextScrapingAllowedAt = null (immediate scraping allowed)`);

    // 🚨 ENHANCEMENT: Notify Chrome extension to clear local storage
    // This ensures extension local storage doesn't override the API reset
    console.log(`🔄 Attempting to notify Chrome extension to clear local scraping wait...`);
    
    return NextResponse.json({
      success: true,
      message: 'Scraping interval reset successfully. You can now scrape immediately.',
      data: {
        previousState: {
          nextScrapingAllowedAt: currentSettings?.nextScrapingAllowedAt,
          extensionStatus: currentSettings?.extensionStatus,
          totalFollowersScraped: currentSettings?.totalFollowersScraped
        },
        newState: {
          nextScrapingAllowedAt: null,
          extensionStatus: updatedSettings.extensionStatus,
          totalFollowersScraped: updatedSettings.totalFollowersScraped,
          isScrapingEligible: true
        },
        // Signal to frontend to notify extension
        shouldClearExtensionWait: true
      }
    });

  } catch (error) {
    console.error('Error resetting scraping interval:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Get current scraping status - for admin interface
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const { verifyApiKey } = await import('@workspace/api-keys');
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401 }
        );
      }
      organizationId = result.organizationId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404 }
        );
      }

      organizationId = membership.organizationId;
    }

    // Get current settings
    const settings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    const now = new Date();
    let isEligible = true;
    let waitTimeMs = 0;
    let waitTimeHuman = '';

    if (settings?.nextScrapingAllowedAt && settings.nextScrapingAllowedAt > now) {
      isEligible = false;
      waitTimeMs = settings.nextScrapingAllowedAt.getTime() - now.getTime();
      
      const days = Math.floor(waitTimeMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor((waitTimeMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((waitTimeMs % (1000 * 60 * 60)) / (1000 * 60));
      
      if (days > 0) {
        waitTimeHuman = `${days} day${days > 1 ? 's' : ''}, ${hours} hour${hours > 1 ? 's' : ''}`;
      } else if (hours > 0) {
        waitTimeHuman = `${hours} hour${hours > 1 ? 's' : ''}, ${minutes} minute${minutes > 1 ? 's' : ''}`;
      } else {
        waitTimeHuman = `${minutes} minute${minutes > 1 ? 's' : ''}`;
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        isEligible,
        nextScrapingAllowedAt: settings?.nextScrapingAllowedAt,
        waitTimeMs,
        waitTimeHuman,
        extensionStatus: settings?.extensionStatus || 'FRESH_START',
        totalFollowersScraped: settings?.totalFollowersScraped || 0,
        lastScrapingSession: settings?.lastScrapingSession,
        scrapingIntervalDays: settings?.scrapingIntervalDays || 5,
        canReset: !isEligible // Only show reset button if currently waiting
      }
    });

  } catch (error) {
    console.error('Error getting scraping status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
