import { NextRequest, NextResponse } from 'next/server';
import { verifyApiKey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';
import { isWithin24HourWindow } from '@workspace/instagram-bot';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

/**
 * Get pending follow-ups for external handling
 * This endpoint is used by the Chrome extension to fetch follow-up messages
 * that are outside the 24-hour window and need external handling
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Get API key from header
    const apiKey = req.headers.get('X-API-Key');

    if (!apiKey) {
      return NextResponse.json(
        { success: false, message: 'API key is missing' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Verify the API key
    const result = await verifyApiKey(apiKey);

    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;

    // Get current time
    const now = new Date();

    // Get follow-ups that should be handled externally (only pending status, exclude external)
    const newFollowUps = await prisma.instagramFollowUp.findMany({
      where: {
        status: 'pending', // Only pending status - external status should only appear in attack list
        InstagramContact: {
          organizationId: organizationId,
          isIgnored: false
        }
      },
      include: {
        InstagramContact: {
          select: {
            instagramId: true,
            instagramNickname: true,
            lastInteractionAt: true
          }
        }
      },
      orderBy: [
        { scheduledTime: 'asc' },
        { sequenceNumber: 'asc' }
      ]
    });

    // Get pending follow-ups from the LEGACY InstagramContact table
    const legacyContactsWithFollowUps = await prisma.instagramContact.findMany({
      where: {
        organizationId: organizationId,
        isIgnored: false,
        OR: [
          { followUpStatus1: 'pending', followUpTime1: { not: null }, followUpMessage1: { not: null } },
          { followUpStatus2: 'pending', followUpTime2: { not: null }, followUpMessage2: { not: null } },
          { followUpStatus3: 'pending', followUpTime3: { not: null }, followUpMessage3: { not: null } },
          { followUpStatus4: 'pending', followUpTime4: { not: null }, followUpMessage4: { not: null } }
        ]
      },
      select: {
        id: true,
        instagramId: true,
        instagramNickname: true,
        lastInteractionAt: true,
        followUpMessage1: true,
        followUpTime1: true,
        followUpStatus1: true,
        followUpMessage2: true,
        followUpTime2: true,
        followUpStatus2: true,
        followUpMessage3: true,
        followUpTime3: true,
        followUpStatus3: true,
        followUpMessage4: true,
        followUpTime4: true,
        followUpStatus4: true
      }
    });

    // Format the follow-ups
    const pendingFollowUps = [];

    // Add NEW follow-ups (only pending status that are outside 24h window)
    for (const followUp of newFollowUps) {
      // Only include pending follow-ups that are outside 24h window
      const isWithinWindow = isWithin24HourWindow(
        followUp.InstagramContact.lastInteractionAt,
        followUp.scheduledTime
      );

      if (!isWithinWindow) { // Include if outside 24h window
        pendingFollowUps.push({
          id: followUp.id,
          contactId: followUp.contactId,
          recipientId: followUp.InstagramContact.instagramId,
          username: followUp.InstagramContact.instagramNickname,
          message: followUp.message,
          scheduledTime: followUp.scheduledTime,
          followUpNumber: followUp.sequenceNumber
        });
      }
    }

    // Add LEGACY follow-ups (only those outside 24h window)
    for (const contact of legacyContactsWithFollowUps) {
      // Check follow-up 1
      if (contact.followUpStatus1 === 'pending' && contact.followUpTime1 && contact.followUpMessage1) {
        const isWithinWindow = isWithin24HourWindow(contact.lastInteractionAt, contact.followUpTime1);
        if (!isWithinWindow) {
          pendingFollowUps.push({
            id: `${contact.id}_fu1`,
            contactId: contact.id,
            recipientId: contact.instagramId,
            username: contact.instagramNickname,
            message: contact.followUpMessage1,
            scheduledTime: contact.followUpTime1,
            followUpNumber: 1
          });
        }
      }

      // Check follow-up 2
      if (contact.followUpStatus2 === 'pending' && contact.followUpTime2 && contact.followUpMessage2) {
        const isWithinWindow = isWithin24HourWindow(contact.lastInteractionAt, contact.followUpTime2);
        if (!isWithinWindow) {
          pendingFollowUps.push({
            id: `${contact.id}_fu2`,
            contactId: contact.id,
            recipientId: contact.instagramId,
            username: contact.instagramNickname,
            message: contact.followUpMessage2,
            scheduledTime: contact.followUpTime2,
            followUpNumber: 2
          });
        }
      }

      // Check follow-up 3
      if (contact.followUpStatus3 === 'pending' && contact.followUpTime3 && contact.followUpMessage3) {
        const isWithinWindow = isWithin24HourWindow(contact.lastInteractionAt, contact.followUpTime3);
        if (!isWithinWindow) {
          pendingFollowUps.push({
            id: `${contact.id}_fu3`,
            contactId: contact.id,
            recipientId: contact.instagramId,
            username: contact.instagramNickname,
            message: contact.followUpMessage3,
            scheduledTime: contact.followUpTime3,
            followUpNumber: 3
          });
        }
      }

      // Check follow-up 4
      if (contact.followUpStatus4 === 'pending' && contact.followUpTime4 && contact.followUpMessage4) {
        const isWithinWindow = isWithin24HourWindow(contact.lastInteractionAt, contact.followUpTime4);
        if (!isWithinWindow) {
          pendingFollowUps.push({
            id: `${contact.id}_fu4`,
            contactId: contact.id,
            recipientId: contact.instagramId,
            username: contact.instagramNickname,
            message: contact.followUpMessage4,
            scheduledTime: contact.followUpTime4,
            followUpNumber: 4
          });
        }
      }
    }

    // Sort all follow-ups by scheduled time
    pendingFollowUps.sort((a, b) => new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime());

    return NextResponse.json({
      success: true,
      data: pendingFollowUps
    }, {
      headers: getCorsHeaders()
    });
  } catch (error) {
    console.error('Error fetching pending follow-ups:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
