import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { followerProcessingQueue } from '~/lib/follower-processing-queue';

/**
 * Test endpoint for follower processing queue
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;
    const body = await req.json();
    const { action } = body;

    switch (action) {
      case 'create_test_follower':
        // Create a test follower and add to queue
        const testFollower = await prisma.instagramFollower.create({
          data: {
            organizationId,
            userId: session.user.id,
            instagramNickname: `test_user_${Date.now()}`,
            instagramId: `test_id_${Date.now()}`,
            priority: 'normal',
            status: 'pending',
            isTargeted: false,
            automationEnabled: true
          }
        });

        // Add to queue
        const queueId = await followerProcessingQueue.enqueue(
          organizationId,
          testFollower.id,
          1,
          false // No conversation for test
        );

        return NextResponse.json({
          success: true,
          data: {
            followerId: testFollower.id,
            queueId,
            nickname: testFollower.instagramNickname
          }
        });

      case 'process_queue':
        // Manually trigger queue processing
        await followerProcessingQueue.processQueue();
        return NextResponse.json({
          success: true,
          message: 'Queue processing triggered'
        });

      case 'get_stats':
        // Get queue stats
        const stats = await followerProcessingQueue.getStats();
        return NextResponse.json({
          success: true,
          data: stats
        });

      case 'start_queue':
        // Start queue processor
        await followerProcessingQueue.start();
        return NextResponse.json({
          success: true,
          message: 'Queue processor started'
        });

      case 'stop_queue':
        // Stop queue processor
        await followerProcessingQueue.stop();
        return NextResponse.json({
          success: true,
          message: 'Queue processor stopped'
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in test endpoint:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
