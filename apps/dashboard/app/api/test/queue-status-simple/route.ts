import { NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';

export async function GET(): Promise<Response> {
  try {
    // Check if queue manager is imported and working
    let queueManagerStatus = 'unknown';
    let followerQueueStatus = 'unknown';
    
    try {
      // Try to access queue manager (this will fail if not imported)
      const { queueManager } = await import('~/lib/queue-manager');
      const status = queueManager.getStatus();
      queueManagerStatus = status.initialized ? 'initialized' : 'not_initialized';
      
      // Try to access follower processing queue
      const { followerProcessingQueue } = await import('~/lib/follower-processing-queue');
      // Check if queue is running by trying to get stats
      try {
        const stats = await followerProcessingQueue.getStats();
        followerQueueStatus = 'running';
      } catch (error) {
        followerQueueStatus = 'stopped';
      }
    } catch (error) {
      queueManagerStatus = 'error: ' + (error as Error).message;
      followerQueueStatus = 'error: ' + (error as Error).message;
    }

    // Get basic database stats
    const totalConversations = await prisma.instagramConversationsNotGathered.count({
      where: {
        organizationId: '93ed4ff1-7ee6-4375-b987-7bc4fb6dc181'
      }
    });

    const gatheredConversations = await prisma.instagramConversationsNotGathered.count({
      where: {
        organizationId: '93ed4ff1-7ee6-4375-b987-7bc4fb6dc181',
        isGathered: true
      }
    });

    const pendingConversations = await prisma.instagramConversationsNotGathered.count({
      where: {
        organizationId: '93ed4ff1-7ee6-4375-b987-7bc4fb6dc181',
        isGathered: false
      }
    });

    return NextResponse.json({
      success: true,
      queueStatus: {
        queueManager: queueManagerStatus,
        followerQueue: followerQueueStatus
      },
      databaseStatus: {
        totalConversations,
        gatheredConversations,
        pendingConversations,
        percentComplete: totalConversations > 0 ? Math.round((gatheredConversations / totalConversations) * 100) : 0
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error checking queue status:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check queue status' },
      { status: 500 }
    );
  }
} 