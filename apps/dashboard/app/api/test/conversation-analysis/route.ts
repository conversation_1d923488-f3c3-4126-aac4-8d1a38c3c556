import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';
import { generateInstagramResponse } from '@workspace/instagram-bot';
import { logger, createTimer } from '@workspace/common/logger';

/**
 * Test endpoint for conversation analysis - no authentication required
 * This is for testing purposes only
 */
export async function GET(req: NextRequest): Promise<Response> {
  const timer = createTimer('Test Conversation Analysis API');

  try {
    const username = req.nextUrl.searchParams.get('username') || 'alexgodlewsky';
    const orgId = req.nextUrl.searchParams.get('orgId');

    logger.instagramConversation('Starting test conversation analysis', {
      operation: 'GET /api/test/conversation-analysis',
      username,
      orgId: orgId || 'test'
    });

    // Sample conversation data for testing
    const sampleMessages = [
      {
        content: "Hey! I saw your post about AI automation. Really interesting stuff!",
        isFromUser: true,
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        content: "Hi! Thanks for reaching out! Yes, we help businesses automate their Instagram DMs. What kind of business do you run?",
        isFromUser: false,
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000).toISOString()
      },
      {
        content: "I run a digital marketing agency. We have about 50 clients and managing all their social media is getting overwhelming.",
        isFromUser: true,
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 45 * 60 * 1000).toISOString()
      },
      {
        content: "That sounds like a perfect fit! Our AI can handle initial conversations, qualify leads, and even schedule appointments. How many Instagram accounts are you managing?",
        isFromUser: false,
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString()
      },
      {
        content: "Around 30 Instagram accounts. We're getting hundreds of DMs daily and can't keep up. Do you have any case studies?",
        isFromUser: true,
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      },
      {
        content: "Absolutely! I can share some case studies. One agency similar to yours increased their lead response rate by 300% and saved 20 hours per week. Would you like to see a demo?",
        isFromUser: false,
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000 + 15 * 60 * 1000).toISOString()
      },
      {
        content: "Yes, I'd love to see a demo! When would be a good time?",
        isFromUser: true,
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
      }
    ];

    // Calculate time since last message
    const lastMessage = sampleMessages[sampleMessages.length - 1];
    const lastMessageDate = new Date(lastMessage.timestamp);
    const now = new Date();
    const responseTimeHours = Math.floor((now.getTime() - lastMessageDate.getTime()) / (1000 * 60 * 60));

    // Filtrowanie wiadomości: pomijaj extension
    const filteredMessages = sampleMessages.filter((msg: any) => !msg.isFromExtension);
    // Format conversation for AI analysis
    const conversationText = filteredMessages
      .map((msg: any) => `${msg.isFromUser ? 'User' : 'Bot'}: ${msg.content}`)
      .join('\n');

    // Create AI prompt for conversation analysis using the existing system
    const analysisPrompt = `
You are an AI conversation analyst for Instagram DM automation. Your task is to analyze conversations and determine priority and status.

CONVERSATION TO ANALYZE:
${conversationText}

CONVERSATION METADATA:
- Last message date: ${lastMessageDate.toISOString()}
- Hours since last message: ${responseTimeHours}
- Total messages: ${sampleMessages.length}
- User messages: ${sampleMessages.filter(m => m.isFromUser).length}

ANALYSIS TASK:
Analyze this conversation and respond with a JSON object containing:

{
  "priority": 1-5, // 5=urgent (demo request), 4=high engagement, 3=normal, 2=low, 1=follow-up
  "status": "new|initial|engaged|qualified|formsent|converted|disqualified",
  "reason": "Brief explanation for priority and status",
  "engagementLevel": "low|medium|high",
  "recommendations": ["action1", "action2", "action3"]
}

PRIORITY GUIDELINES:
- 5: User requested demo/meeting, ready to convert
- 4: Highly engaged, asking detailed questions
- 3: Normal engagement, responding regularly
- 2: Low engagement, short responses
- 1: Non-responsive, needs follow-up

STATUS GUIDELINES:
- new: Just started, no real engagement
- initial: Basic back-and-forth established
- engaged: Active conversation, showing interest
- qualified: Expressed clear interest in services
- formsent: Ready for next step (demo/meeting)
- converted: Successfully became customer
- disqualified: Not a good fit

Respond ONLY with the JSON object, no additional text.
`;

    let analysis;
    let aiError = null;

    try {
      // Use the existing Claude implementation for conversation analysis
      const claudeResponse = await generateInstagramResponse({
        prompt: analysisPrompt,
        conversationHistory: conversationText,
        organizationId: orgId || 'test-org',
        disableCache: true // Disable cache for testing
      });

      // Try to parse the Claude response as our analysis format
      let parsedAnalysis;
      try {
        // Claude might return the analysis in the message field
        const responseText = claudeResponse.message || claudeResponse.messages?.[0] || '';

        // Try to extract JSON from the response
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          parsedAnalysis = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in Claude response');
        }
      } catch (parseError) {
        // If parsing fails, create analysis from Claude's stage determination
        parsedAnalysis = {
          priority: claudeResponse.stage === 'qualified' ? 4 : 3,
          status: claudeResponse.stage || 'engaged',
          reason: `Claude analysis: ${claudeResponse.message?.substring(0, 100)}...`,
          engagementLevel: 'medium'
        };
      }

      analysis = {
        priority: parsedAnalysis.priority || 3,
        status: parsedAnalysis.status || 'engaged',
        reason: parsedAnalysis.reason || 'AI analysis completed',
        lastMessageDate: lastMessageDate.toISOString(),
        engagementLevel: parsedAnalysis.engagementLevel || 'medium',
        responseTime: responseTimeHours,
        recommendations: parsedAnalysis.recommendations || [
          'Continue conversation',
          'Ask qualifying questions',
          'Share relevant information'
        ]
      };

      logger.instagramConversation('Claude analysis completed', {
        operation: 'conversation-analysis',
        username,
        priority: analysis.priority,
        status: analysis.status,
        claudeStage: claudeResponse.stage
      });

    } catch (error) {
      aiError = error instanceof Error ? error.message : 'Unknown AI error';

      logger.warn('Claude analysis failed, using fallback', {
        operation: 'conversation-analysis',
        username,
        error: aiError
      });

      // Fallback analysis based on smart heuristics
      const hasDemo = sampleMessages.some(msg =>
        msg.content.toLowerCase().includes('demo') ||
        msg.content.toLowerCase().includes('schedule')
      );

      const hasQuestions = sampleMessages.filter(msg =>
        msg.isFromUser && msg.content.includes('?')
      ).length;

      const userMessages = sampleMessages.filter(msg => msg.isFromUser);
      const avgMessageLength = userMessages.reduce((sum, msg) => sum + msg.content.length, 0) / userMessages.length;

      analysis = {
        priority: hasDemo ? 5 : (responseTimeHours < 4 ? 4 : 3),
        status: hasDemo ? 'qualified' : (sampleMessages.length > 5 ? 'engaged' : 'initial'),
        reason: hasDemo
          ? 'User requested demo - high priority conversion opportunity'
          : `Fallback analysis: ${sampleMessages.length} messages, ${hasQuestions} questions, avg length ${Math.round(avgMessageLength)} chars`,
        lastMessageDate: lastMessageDate.toISOString(),
        engagementLevel: hasQuestions > 2 || avgMessageLength > 50 ? 'high' : 'medium',
        responseTime: responseTimeHours,
        recommendations: hasDemo ? [
          'Schedule demo immediately - user is ready',
          'Send calendar link within 1 hour',
          'Follow up with case studies mentioned'
        ] : [
          'Continue engagement conversation',
          'Ask qualifying questions',
          'Share relevant case studies'
        ]
      };
    }

    // Try to find existing contact if orgId is provided
    let existingContact = null;
    if (orgId) {
      try {
        existingContact = await prisma.instagramContact.findFirst({
          where: {
            organizationId: orgId,
            instagramNickname: username
          }
        });
      } catch (dbError) {
        // Database error is not critical for this test
        logger.warn('Database query failed in test endpoint', {
          operation: 'test-conversation-analysis',
          error: dbError instanceof Error ? dbError.message : 'Unknown DB error'
        });
      }
    }

    timer.end({
      username,
      messageCount: sampleMessages.length,
      priority: analysis.priority,
      status: analysis.status,
      success: true,
      aiError: aiError ? true : false
    });

    return NextResponse.json({
      success: true,
      testMode: true,
      username,
      analysis,
      sampleConversation: sampleMessages,
      existingContact: existingContact ? {
        id: existingContact.id,
        instagramNickname: existingContact.instagramNickname,
        stage: existingContact.stage,
        createdAt: existingContact.createdAt
      } : null,
      aiError,
      metadata: {
        conversationLength: sampleMessages.length,
        timeSpan: `${Math.floor((new Date(sampleMessages[sampleMessages.length - 1].timestamp).getTime() - new Date(sampleMessages[0].timestamp).getTime()) / (1000 * 60 * 60))} hours`,
        lastMessageAge: `${responseTimeHours} hours ago`
      }
    });

  } catch (error) {
    logger.error('Error in test conversation analysis', {
      operation: 'test-conversation-analysis'
    }, error as Error);

    timer.end({
      success: false,
      error: true
    });

    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error',
      testMode: true
    }, { status: 500 });
  }
}

/**
 * POST endpoint for testing with custom conversation data
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const body = await req.json();
    const { username = 'alexgodlewsky', messages, orgId } = body;

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json({
        error: 'Messages array is required',
        testMode: true
      }, { status: 400 });
    }

    // Use the same analysis logic as GET but with custom messages
    const lastMessage = messages[messages.length - 1];
    const lastMessageDate = new Date(lastMessage.timestamp);
    const responseTimeHours = Math.floor((Date.now() - lastMessageDate.getTime()) / (1000 * 60 * 60));

    const conversationText = messages
      .map(msg => `${msg.isFromUser ? 'User' : 'Bot'}: ${msg.content}`)
      .join('\n');

    // Simple heuristic analysis for testing
    const analysis = {
      priority: messages.length > 5 ? 4 : 3,
      status: messages.some(m => m.content.toLowerCase().includes('demo')) ? 'qualified' : 'engaged',
      reason: `Analysis based on ${messages.length} messages. User engagement level appears high.`,
      lastMessageDate: lastMessageDate.toISOString(),
      engagementLevel: messages.length > 3 ? 'high' : 'medium',
      responseTime: responseTimeHours,
      recommendations: [
        'Follow up within 24 hours',
        'Provide requested information',
        'Move conversation forward'
      ]
    };

    return NextResponse.json({
      success: true,
      testMode: true,
      username,
      analysis,
      providedMessages: messages,
      metadata: {
        messageCount: messages.length,
        responseTimeHours
      }
    });

  } catch (error) {
    return NextResponse.json({
      error: 'Failed to process custom conversation',
      details: error instanceof Error ? error.message : 'Unknown error',
      testMode: true
    }, { status: 500 });
  }
}
