import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

/**
 * Test endpoint to simulate marking messages as sent
 * This helps test the message status tracking functionality
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { contactId, messageSequence = 1 } = body;

    if (!contactId) {
      return NextResponse.json(
        { success: false, error: 'Contact ID is required' },
        { status: 400 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Find the contact
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: contactId,
        organizationId
      }
    });

    if (!contact) {
      return NextResponse.json(
        { success: false, error: 'Contact not found' },
        { status: 404 }
      );
    }

    // Simulate marking message as sent by calling the actual message status API
    const messageStatusResponse = await fetch(`${req.nextUrl.origin}/api/chrome-extension/message-status`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': req.headers.get('cookie') || ''
      },
      body: JSON.stringify({
        contactId: contactId,
        status: 'sent',
        messageType: messageSequence === 1 ? 'initial' : 'follow_up',
        messageSequence: messageSequence
      })
    });

    const messageStatusResult = await messageStatusResponse.json();

    if (!messageStatusResult.success) {
      return NextResponse.json(
        { success: false, error: messageStatusResult.error || 'Failed to update message status' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Message ${messageSequence} marked as sent for contact ${contact.instagramNickname}`,
      data: {
        contactId: contact.id,
        username: contact.instagramNickname,
        messageSequence: messageSequence,
        updatedStatus: messageStatusResult.data
      }
    });

  } catch (error) {
    console.error('Error in test mark message sent:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to list contacts available for testing
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Get contacts in attack list
    const contacts = await prisma.instagramContact.findMany({
      where: {
        organizationId,
        attackListStatus: 'pending',
        stage: { not: 'disqualified' }
      },
      select: {
        id: true,
        instagramNickname: true,
        priority: true,
        stage: true,
        attackListStatus: true,
        currentMessageSequence: true,
        batchMessageStatus: true
      },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' }
      ],
      take: 10
    });

    return NextResponse.json({
      success: true,
      message: 'Available contacts for testing',
      data: contacts,
      count: contacts.length
    });

  } catch (error) {
    console.error('Error getting test contacts:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
