import { NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';

export async function GET(): Promise<Response> {
  try {
    // Get all queue items with attempts info
    const allQueueItems = await prisma.followerProcessingQueue.findMany({
      where: {
        organizationId: '93ed4ff1-7ee6-4375-b987-7bc4fb6dc181'
      },
      select: {
        id: true,
        followerId: true,
        status: true,
        attempts: true,
        maxAttempts: true,
        scheduledAt: true,
        hasConversation: true,
        processingType: true,
        errorMessage: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    // Analyze the queue
    const analysis = {
      total: allQueueItems.length,
      byStatus: {} as Record<string, number>,
      byAttempts: {} as Record<string, number>,
      byProcessingType: {} as Record<string, number>,
      stuckItems: allQueueItems.filter(item => item.attempts >= item.maxAttempts),
      eligibleItems: allQueueItems.filter(item => 
        item.status === 'pending' && 
        item.attempts < item.maxAttempts &&
        item.scheduledAt <= new Date()
      ),
      pendingItems: allQueueItems.filter(item => item.status === 'pending'),
      failedItems: allQueueItems.filter(item => item.status === 'failed'),
      completedItems: allQueueItems.filter(item => item.status === 'completed')
    };

    // Count by status
    allQueueItems.forEach(item => {
      analysis.byStatus[item.status] = (analysis.byStatus[item.status] || 0) + 1;
      analysis.byAttempts[`${item.attempts}/${item.maxAttempts}`] = (analysis.byAttempts[`${item.attempts}/${item.maxAttempts}`] || 0) + 1;
      analysis.byProcessingType[item.processingType] = (analysis.byProcessingType[item.processingType] || 0) + 1;
    });

    return NextResponse.json({
      success: true,
      analysis,
      sampleItems: allQueueItems.slice(0, 10), // First 10 items
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error debugging queue:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to debug queue' },
      { status: 500 }
    );
  }
} 