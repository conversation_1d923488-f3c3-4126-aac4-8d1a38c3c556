import { NextRequest, NextResponse } from 'next/server';
import { verifyApiKey } from '@workspace/api-keys';

export async function POST(req: NextRequest): Promise<Response> {
  try {
    const body = await req.json();
    const { apiKey } = body;
    
    if (!apiKey) {
      return NextResponse.json({ 
        success: false, 
        error: 'Missing apiKey parameter' 
      }, { status: 400 });
    }

    console.log('🔍 DEBUG: Verifying API key...');
    console.log('🔍 DEBUG: API key prefix:', apiKey.substring(0, 8));
    console.log('🔍 DEBUG: API key length:', apiKey.length);

    // Verify the API key
    const result = await verifyApiKey(apiKey);
    
    if (!result.success) {
      return NextResponse.json({
        success: false,
        error: result.errorMessage
      });
    }

    console.log('🔍 DEBUG: API key verification successful');
    console.log('🔍 DEBUG: Organization ID:', result.organizationId);
    console.log('🔍 DEBUG: Organization ID length:', result.organizationId.length);

    return NextResponse.json({
      success: true,
      data: {
        organizationId: result.organizationId,
        organizationIdLength: result.organizationId.length,
        isValidUUID: result.organizationId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i) !== null
      }
    });

  } catch (error) {
    console.error('Error getting organization ID:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 