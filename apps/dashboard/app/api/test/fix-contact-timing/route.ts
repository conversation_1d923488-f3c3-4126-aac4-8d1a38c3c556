import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

/**
 * Test endpoint to fix contacts that have future nextMessageAt dates
 * This fixes the issue where new followers without conversation should be available NOW
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;
    const now = new Date();

    // Find contacts that are new followers (priority 3, stage new) with future nextMessageAt
    const contactsToFix = await prisma.instagramContact.findMany({
      where: {
        organizationId,
        priority: 3,
        stage: 'new',
        conversationSource: 'extension',
        nextMessageAt: { gt: now }, // Future dates
        attackListStatus: 'pending'
      }
    });

    console.log(`Found ${contactsToFix.length} contacts with future nextMessageAt dates`);

    const results = {
      fixed: 0,
      contacts: [] as string[]
    };

    // Update each contact to have nextMessageAt = NOW
    for (const contact of contactsToFix) {
      await prisma.instagramContact.update({
        where: { id: contact.id },
        data: {
          nextMessageAt: now, // Set to NOW so they're available immediately
          updatedAt: new Date()
        }
      });

      results.fixed++;
      results.contacts.push(contact.instagramNickname);
      console.log(`Fixed ${contact.instagramNickname}: nextMessageAt set to NOW`);
    }

    return NextResponse.json({
      success: true,
      message: `Fixed ${results.fixed} contacts - they are now available for messaging immediately`,
      data: results
    });

  } catch (error) {
    console.error('Error fixing contact timing:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
