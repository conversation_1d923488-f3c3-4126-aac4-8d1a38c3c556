import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { followerProcessingQueue } from '~/lib/follower-processing-queue';
import { queueManager } from '~/lib/queue-manager';

export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Get queue statistics
    const queueStats = await followerProcessingQueue.getStats();

    // Get conversation gathering status
    const totalConversations = await prisma.instagramConversationsNotGathered.count({
      where: { organizationId }
    });

    const gatheredConversations = await prisma.instagramConversationsNotGathered.count({
      where: { 
        organizationId,
        isGathered: true 
      }
    });

    const pendingConversations = totalConversations - gatheredConversations;

    // Get Chrome extension settings
    const extensionSettings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    return NextResponse.json({
      success: true,
      data: {
        queueManager: {
          initialized: queueManager.getStatus().initialized
        },
        followerQueue: {
          stats: queueStats,
          isProcessing: followerProcessingQueue['isProcessing'] || false
        },
        conversationGathering: {
          total: totalConversations,
          gathered: gatheredConversations,
          pending: pendingConversations,
          percentComplete: totalConversations > 0 ? Math.round((gatheredConversations / totalConversations) * 100) : 0
        },
        extensionStatus: extensionSettings?.extensionStatus || 'FRESH_START',
        currentActivity: extensionSettings?.currentActivity || null
      }
    });

  } catch (error) {
    console.error('Error getting queue status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 