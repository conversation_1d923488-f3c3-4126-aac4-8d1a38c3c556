import { NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';

export async function GET(): Promise<Response> {
  try {
    // Get queue items for the organization
    const queueItems = await prisma.followerProcessingQueue.findMany({
      where: {
        organizationId: '93ed4ff1-7ee6-4375-b987-7bc4fb6dc181'
      },
      orderBy: {
        scheduledAt: 'asc'
      },
      take: 20, // Show first 20 items
      select: {
        id: true,
        followerId: true,
        status: true,
        scheduledAt: true,
        attempts: true,
        hasConversation: true,
        processingType: true,
        createdAt: true,
        updatedAt: true
      }
    });

    // Get counts by status
    const statusCounts = await prisma.followerProcessingQueue.groupBy({
      by: ['status'],
      where: {
        organizationId: '93ed4ff1-7ee6-4375-b987-7bc4fb6dc181'
      },
      _count: {
        status: true
      }
    });

    // Get counts by processing type
    const typeCounts = await prisma.followerProcessingQueue.groupBy({
      by: ['processingType'],
      where: {
        organizationId: '93ed4ff1-7ee6-4375-b987-7bc4fb6dc181'
      },
      _count: {
        processingType: true
      }
    });

    return NextResponse.json({
      success: true,
      queueItems,
      statusCounts,
      typeCounts,
      totalItems: queueItems.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error checking queue items:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check queue items' },
      { status: 500 }
    );
  }
} 