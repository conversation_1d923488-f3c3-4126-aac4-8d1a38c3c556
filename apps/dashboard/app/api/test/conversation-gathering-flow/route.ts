import { NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { getAllConversations, getConversationMessages } from '~/lib/instagram-client';
import { generateInstagramResponse } from '@workspace/instagram-bot';

interface TestResult {
  step: string;
  success: boolean;
  message: string;
  data?: any;
}

// Generate a smaller batch number for testing
const TEST_BATCH_NUMBER = Math.floor(Date.now() / 1000);

// Rate limiting configuration
const CONVERSATION_PROCESSING_DELAY_MS = 10000; // 10 seconds between conversations

// Small test set for real testing
const EXAMPLE_FOLLOWERS = [
  {
    instagramNickname: 'norbert_rzepka_biznes',
    instagramId: `${TEST_BATCH_NUMBER}001`,
    avatar: 'https://picsum.photos/150/150?random=norbert',
    followerCount: 5000,
    isVerified: false
  },
  {
    instagramNickname: 'socialflow.pl',
    instagramId: `${TEST_BATCH_NUMBER}002`,
    avatar: 'https://picsum.photos/150/150?random=socialflow',
    followerCount: 10000,
    isVerified: true
  },
  {
    instagramNickname: 'ig.wasilewski',
    instagramId: `${TEST_BATCH_NUMBER}003`,
    avatar: 'https://picsum.photos/150/150?random=igor',
    followerCount: 4100,
    isVerified: false
  }
];

export async function POST(): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Use the example followers data for this test
    const followersToProcess = EXAMPLE_FOLLOWERS;

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: { userId: session.user.id }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const results: TestResult[] = [];
    const organizationId = membership.organizationId;

    // Step 1: Upload Instagram followers
    try {
      console.log('Step 1: Uploading Instagram followers...');

      let uploadedCount = 0;
      let skippedCount = 0;

      for (const follower of followersToProcess) {
        const existingFollower = await prisma.instagramFollower.findFirst({
          where: {
            organizationId,
            instagramNickname: follower.instagramNickname
          }
        });

        if (!existingFollower) {
          await prisma.instagramFollower.create({
            data: {
              organizationId,
              userId: session.user.id,
              instagramNickname: follower.instagramNickname,
              instagramId: follower.instagramId,
              avatar: follower.avatar,
              followerCount: follower.followerCount,
              isVerified: follower.isVerified,
              batchNumber: TEST_BATCH_NUMBER
            }
          });
          uploadedCount++;
        } else {
          skippedCount++;
        }
      }

      results.push({
        step: 'Upload Instagram Followers',
        success: true,
        message: `Uploaded ${uploadedCount} new followers, skipped ${skippedCount} existing`,
        data: {
          uploaded: uploadedCount,
          skipped: skippedCount,
          total: followersToProcess.length,
          followers: followersToProcess.map(f => f.instagramNickname)
        }
      });

    } catch (error) {
      results.push({
        step: 'Upload Instagram Followers',
        success: false,
        message: error instanceof Error ? error.message : 'Failed to upload followers'
      });
    }

    // Step 2: Gather ALL conversations from Instagram API with pagination
    try {
      console.log('Step 2A: Gathering ALL conversations from Instagram API with pagination...');

      // Get Instagram settings for the organization to get access token
      const instagramSettings = await prisma.instagramSettings.findFirst({
        where: {
          organizationId,
          instagramToken: { not: null }
        }
      });

      if (!instagramSettings?.instagramToken) {
        results.push({
          step: 'Conversation Gathering Analysis',
          success: false,
          message: 'No Instagram token found. Please configure Instagram settings first.'
        });

        return NextResponse.json({
          success: false,
          results,
          message: 'Instagram token required for conversation gathering'
        });
      }

      console.log('Using real Instagram API to gather conversations...');

      // Call the real Instagram API to get ALL conversations with pagination
      const instagramApiResponse = await getAllConversations(instagramSettings.instagramToken);
      const instagramApiConversations = instagramApiResponse.data || [];

      console.log(`✅ Successfully gathered ${instagramApiConversations.length} conversations from Instagram API`);

      // Save conversations to InstagramConversationsNotGathered table
      console.log('Step 2B: Saving conversations to database...');

      let savedCount = 0;
      for (const conversation of instagramApiConversations) {
        // Get participants from the conversation
        const participants = conversation.participants || [];

        console.log(`Processing conversation ${conversation.id} with ${participants.length} participants:`,
          participants.map((p: any) => p.username));
        console.log(`Full conversation ID: ${conversation.id}`);

        // According to Instagram API documentation and your example:
        // The business account owner is always the FIRST participant in the participants array
        // So we skip the first participant and take the second one (the actual conversation partner)

        if (participants.length >= 2) {
          // Skip the first participant (business account) and take the second one
          const participant = participants[1]; // The actual conversation partner

          console.log(`Business account (skipped): ${participants[0].username}`);
          console.log(`Conversation partner: ${participant.username}`);

          try {
            await prisma.instagramConversationsNotGathered.upsert({
              where: {
                instagramConversationId: conversation.id
              },
              update: {
                participantUsername: participant.username,
                participantId: participant.id,
                updatedTime: new Date(conversation.updated_time),
                isGathered: false
              },
              create: {
                organizationId,
                instagramConversationId: conversation.id,
                participantUsername: participant.username,
                participantId: participant.id,
                updatedTime: new Date(conversation.updated_time),
                isGathered: false
              }
            });
            savedCount++;
            console.log(`✅ Saved conversation for ${participant.username} (ID: ${participant.id})`);
          } catch (error) {
            console.error(`❌ Error saving conversation for ${participant.username}:`, error);
          }
        } else {
          console.log(`⚠️ Conversation ${conversation.id} has only ${participants.length} participants (need at least 2)`);
        }
      }

      console.log(`💾 Saved ${savedCount} conversation records to database`);

      console.log(`Instagram API returned ${instagramApiConversations.length} conversations`);

      // Create a map of usernames that have conversations
      const conversationMap = new Map();
      instagramApiConversations.forEach((conversation: any) => {
        // Get participants and skip the first one (business account)
        const participants = conversation.participants || [];

        if (participants.length >= 2) {
          // Skip the first participant (business account) and take the second one
          const participant = participants[1];

          if (participant.username) {
            conversationMap.set(participant.username, {
              conversationId: conversation.id,
              lastUpdated: conversation.updated_time,
              participantId: participant.id
            });
          }
        }
      });

      console.log('Step 2C: Checking if our followers are in conversation list...');

      const conversationAnalysis = [];

      for (const follower of followersToProcess) {
        const followerUsername = follower.instagramNickname;

        // Check if this follower is in our conversation list
        const isInConversationList = conversationMap.has(followerUsername);
        const conversationData = conversationMap.get(followerUsername) || {};

        console.log(`${followerUsername}: ${isInConversationList ? `Found in conversation list (ID: ${conversationData.conversationId})` : 'NOT in conversation list'}`);

        conversationAnalysis.push({
          username: followerUsername,
          isInConversationList: isInConversationList,
          conversationId: conversationData.conversationId || null,
          lastUpdated: conversationData.lastUpdated || null,
          participantId: conversationData.participantId || null,
          category: isInConversationList ? 'has_conversation' : 'no_conversation'
        });
      }

      const noConversationUsers = conversationAnalysis.filter(u => u.category === 'no_conversation');
      const hasConversationUsers = conversationAnalysis.filter(u => u.category === 'has_conversation');

      results.push({
        step: 'Conversation Gathering Analysis',
        success: true,
        message: `Analyzed ${followersToProcess.length} followers: ${noConversationUsers.length} not in conversation list, ${hasConversationUsers.length} in conversation list`,
        data: {
          noConversationUsers,
          hasConversationUsers,
          analysis: conversationAnalysis,
          totalConversationsGathered: instagramApiConversations.length,
          savedConversationsCount: savedCount
        }
      });

    } catch (error) {
      results.push({
        step: 'Conversation Gathering Analysis',
        success: false,
        message: error instanceof Error ? error.message : 'Failed to analyze conversations'
      });
    }

    // Step 3: Process Followers WITHOUT Conversations (Priority 3)
    try {
      console.log('Step 3: Processing followers WITHOUT conversations...');

      const noConversationUsers = results.find(r => r.step === 'Conversation Gathering Analysis')?.data?.noConversationUsers || [];
      let addedToAttackList = 0;

      for (const user of noConversationUsers) {
        const followerUsername = user.username;

        // Check if this follower already exists as an Instagram contact
        const existingContact = await prisma.instagramContact.findFirst({
          where: {
            organizationId,
            instagramNickname: followerUsername
          }
        });

        if (!existingContact) {
          // Get the follower data to get Instagram ID
          const followerData = followersToProcess.find(f => f.instagramNickname === followerUsername);

          if (followerData) {
            // Get available message batches for random assignment
            const messageBatches = await prisma.messageBatch.findMany({
              where: {
                organizationId
              },
              include: {
                MessageBatchItem: {
                  where: {
                    sequenceNumber: 1 // Only get first messages for initial assignment
                  }
                }
              }
            });

            // Select a random message batch if available
            let selectedBatchId = null;
            if (messageBatches.length > 0) {
              const randomIndex = Math.floor(Math.random() * messageBatches.length);
              selectedBatchId = messageBatches[randomIndex].id;
              console.log(`Selected random message batch ${selectedBatchId} for ${followerUsername}`);
            }

            // Create new Instagram contact with priority 3 (new followers)
            const newContact = await prisma.instagramContact.create({
              data: {
                organizationId,
                userId: session.user.id,
                instagramId: followerData.instagramId,
                instagramNickname: followerUsername,
                avatar: followerData.avatar,
                stage: 'new',
                priority: 3, // New followers get priority 3
                status: 'pending',
                messageCount: 0,
                isIgnored: false,
                isTakeControl: false,
                isConversionLinkSent: false,
                nextMessageAt: new Date(), // Ready to message immediately
                attackListStatus: 'pending',
                conversationSource: 'api'
              }
            });

            // If we have a message batch, create follow-up messages from the batch
            if (selectedBatchId && messageBatches.length > 0) {
              const selectedBatch = messageBatches.find(b => b.id === selectedBatchId);
              if (selectedBatch && selectedBatch.MessageBatchItem.length > 0) {
                // Create follow-ups for all messages in the batch (starting from sequence 2)
                const allBatchItems = await prisma.messageBatchItem.findMany({
                  where: {
                    messageBatchId: selectedBatchId
                  },
                  orderBy: {
                    sequenceNumber: 'asc'
                  }
                });

                for (const batchItem of allBatchItems) {
                  if (batchItem.sequenceNumber > 1) { // Skip first message (it's the initial message)
                    await prisma.instagramFollowUp.create({
                      data: {
                        contactId: newContact.id,
                        message: batchItem.messageText,
                        scheduledTime: new Date(Date.now() + ((batchItem.sequenceNumber - 1) * 24 * 60 * 60 * 1000)), // 24h intervals
                        status: 'external', // Mark as external so it only appears in attack list, not system follow-ups
                        sequenceNumber: batchItem.sequenceNumber - 1 // Adjust sequence for follow-ups
                      }
                    });
                  }
                }
                console.log(`✅ Created ${allBatchItems.length - 1} follow-up messages from batch for ${followerUsername}`);
              }
            }

            addedToAttackList++;
            console.log(`✅ Added ${followerUsername} to attack list with priority 3 (new follower)${selectedBatchId ? ` with message batch ${selectedBatchId}` : ' (no message batch available)'}`);
          }
        } else {
          console.log(`⚠️ ${followerUsername} already exists as contact, skipping`);
        }
      }

      results.push({
        step: 'Process Followers WITHOUT Conversations',
        success: true,
        message: `Added ${addedToAttackList} new followers to attack list with priority 3`,
        data: {
          processedUsers: noConversationUsers.length,
          addedToAttackList,
          skippedExisting: noConversationUsers.length - addedToAttackList
        }
      });

    } catch (error) {
      results.push({
        step: 'Process Followers WITHOUT Conversations',
        success: false,
        message: error instanceof Error ? error.message : 'Failed to process followers without conversations'
      });
    }

    // Step 4: Process Followers WITH Conversations (AI Analysis)
    try {
      console.log(`Step 4: Processing followers WITH conversations (with ${CONVERSATION_PROCESSING_DELAY_MS / 1000}s delays between each)...`);

      const hasConversationUsers = results.find(r => r.step === 'Conversation Gathering Analysis')?.data?.hasConversationUsers || [];
      let processedWithAI = 0;

      // Get Instagram settings for API calls
      const instagramSettings = await prisma.instagramSettings.findFirst({
        where: {
          organizationId,
          instagramToken: { not: null }
        }
      });

      if (!instagramSettings?.instagramToken) {
        results.push({
          step: 'Process Followers WITH Conversations',
          success: false,
          message: 'No Instagram token found for gathering conversation messages'
        });

        return NextResponse.json({
          success: false,
          results,
          message: 'Instagram token required for conversation message gathering'
        });
      }

      for (let i = 0; i < hasConversationUsers.length; i++) {
        const user = hasConversationUsers[i];
        const followerUsername = user.username;
        const conversationId = user.conversationId;

        console.log(`Processing ${followerUsername} with conversation ID: ${conversationId} (${i + 1}/${hasConversationUsers.length})`);

        try {
          // Step 4A: Gather full conversation messages
          console.log(`Gathering conversation messages for ${followerUsername}...`);
          const conversationResponse = await getConversationMessages(conversationId, instagramSettings.instagramToken);

          console.log(`Raw conversation response for ${followerUsername}:`, JSON.stringify(conversationResponse, null, 2));

          // Format conversation history for AI
          let conversationHistory = '';

          // The response structure is: { data: [{ id: conversationId, messages: { data: [...] } }] }
          if (conversationResponse && conversationResponse.data && conversationResponse.data.length > 0) {
            const conversation = conversationResponse.data[0];
            const messages = conversation.messages?.data || [];

            console.log(`Found ${messages.length} messages for ${followerUsername}`);

            if (messages.length > 0) {
              // Sort messages by timestamp to ensure proper order
              const sortedMessages = messages.sort((a: any, b: any) =>
                new Date(a.created_time).getTime() - new Date(b.created_time).getTime()
              );

              conversationHistory = sortedMessages.map((msg: any) => {
                const sender = msg.from?.username || msg.from?.id || 'Unknown';
                const messageText = msg.message || '[Media/Attachment]';
                return `${sender}: ${messageText}`;
              }).join('\n');

              // Find the last user message (not from business account)
              // We need to identify the business account dynamically from the conversation
              const lastUserMessage = sortedMessages
                .filter((msg: any) => {
                  // Filter out messages where the sender is the first participant (business account)
                  // We can identify this by checking if it's not the follower we're processing
                  return msg.from?.username === followerUsername;
                })
                .pop(); // Get the last one

              if (lastUserMessage) {
                const lastInteractionTime = new Date(lastUserMessage.created_time);
                conversationHistory += `\n\nLAST USER INTERACTION: ${lastInteractionTime.toISOString()}`;
                console.log(`Last user interaction for ${followerUsername}: ${lastInteractionTime.toISOString()}`);
              }
            } else {
              conversationHistory = 'No messages found in conversation';
            }
          } else {
            conversationHistory = 'No messages found in conversation';
          }

          console.log(`Formatted conversation history for ${followerUsername}:`, conversationHistory.substring(0, 300) + '...');

          // Step 4B: Pass to AI for analysis with CONVERSATION GATHERING mode
          console.log(`Analyzing conversation with AI for ${followerUsername}...`);
          const aiResponse = await generateInstagramResponse({
            prompt: "CONVERSATION GATHERING",
            conversationHistory: conversationHistory,
            organizationId: organizationId
          });

          console.log(`AI analysis for ${followerUsername}:`, {
            stage: aiResponse.stage,
            priority: aiResponse.priority,
            followUpsCount: aiResponse.followUps?.length || 0
          });

          // Step 4C: Create or update Instagram contact with AI analysis
          const followerData = followersToProcess.find(f => f.instagramNickname === followerUsername);

          if (followerData) {
            const existingContact = await prisma.instagramContact.findFirst({
              where: {
                organizationId,
                instagramNickname: followerUsername
              }
            });

            if (!existingContact) {
              // Create new contact with AI-determined priority and stage
              const newContact = await prisma.instagramContact.create({
                data: {
                  organizationId,
                  userId: session.user.id,
                  instagramId: followerData.instagramId,
                  instagramNickname: followerUsername,
                  avatar: followerData.avatar,
                  stage: (aiResponse.stage as any) || 'initial',
                  priority: aiResponse.priority || 3,
                  status: 'pending',
                  messageCount: conversationResponse?.data?.[0]?.messages?.data?.length || 0,
                  isIgnored: false,
                  isTakeControl: false,
                  isConversionLinkSent: false,
                  nextMessageAt: new Date(), // Ready for follow-up
                  attackListStatus: 'pending',
                  conversationSource: 'api'
                }
              });

              // Save conversation messages to database
              const messages = conversationResponse?.data?.[0]?.messages?.data || [];
              if (messages.length > 0) {
                console.log(`Saving ${messages.length} messages to database for ${followerUsername}...`);

                for (const msg of messages) {
                  try {
                    await prisma.instagramMessage.create({
                      data: {
                        contactId: newContact.id,
                        messageId: msg.id,
                        content: msg.message || '[Media/Attachment]',
                        isFromUser: msg.from?.username === followerUsername, // True if from the follower (not business account)
                        timestamp: new Date(msg.created_time),
                        mediaType: msg.attachments?.[0]?.mime_type || null,
                        mediaUrl: msg.attachments?.[0]?.file_url || null
                      }
                    });
                  } catch (error) {
                    console.error(`Error saving message ${msg.id}:`, error);
                  }
                }

                console.log(`✅ Saved ${messages.length} messages for ${followerUsername}`);
              }

              // Add follow-ups if AI provided them
              if (aiResponse.followUps && aiResponse.followUps.length > 0) {
                for (let i = 0; i < aiResponse.followUps.length; i++) {
                  const followUp = aiResponse.followUps[i];
                  await prisma.instagramFollowUp.create({
                    data: {
                      contactId: newContact.id,
                      message: followUp.message,
                      scheduledTime: new Date(followUp.delayHours ? Date.now() + (followUp.delayHours * 60 * 60 * 1000) : Date.now() + (24 * 60 * 60 * 1000)),
                      status: 'external', // Mark as external so it only appears in attack list, not system follow-ups
                      sequenceNumber: i + 1
                    }
                  });
                }
              }

              // Update Instagram follower to mark as contacted
              await prisma.instagramFollower.updateMany({
                where: {
                  organizationId,
                  instagramNickname: followerUsername
                },
                data: {
                  isContacted: true
                }
              });

              // Mark conversation as gathered in InstagramConversationsNotGathered
              await prisma.instagramConversationsNotGathered.updateMany({
                where: {
                  organizationId,
                  instagramConversationId: conversationId
                },
                data: {
                  isGathered: true
                }
              });

              processedWithAI++;
              console.log(`✅ Added ${followerUsername} to attack list with AI priority ${aiResponse.priority} and ${aiResponse.followUps?.length || 0} follow-ups`);
            } else {
              console.log(`⚠️ ${followerUsername} already exists as contact, skipping`);
            }
          }

        } catch (error) {
          console.error(`❌ Error processing ${followerUsername}:`, error);
        }

        // Add delay between conversations to avoid rate limiting
        if (i < hasConversationUsers.length - 1) { // Don't delay after the last one
          console.log(`⏳ Waiting ${CONVERSATION_PROCESSING_DELAY_MS / 1000} seconds before processing next conversation...`);
          await new Promise(resolve => setTimeout(resolve, CONVERSATION_PROCESSING_DELAY_MS));
        }
      }

      results.push({
        step: 'Process Followers WITH Conversations',
        success: true,
        message: `Processed ${processedWithAI} followers with AI conversation analysis (with ${CONVERSATION_PROCESSING_DELAY_MS / 1000}s delays)`,
        data: {
          totalUsersWithConversations: hasConversationUsers.length,
          processedWithAI,
          skipped: hasConversationUsers.length - processedWithAI,
          processingDelaySeconds: CONVERSATION_PROCESSING_DELAY_MS / 1000
        }
      });

    } catch (error) {
      results.push({
        step: 'Process Followers WITH Conversations',
        success: false,
        message: error instanceof Error ? error.message : 'Failed to analyze conversations'
      });
    }

    // Step 5: Final Attack List Summary
    try {
      console.log('Step 5: Generating final attack list summary...');

      // Get all Instagram contacts for this organization sorted by attack list criteria
      const attackList = await prisma.instagramContact.findMany({
        where: {
          organizationId,
          isIgnored: false,
          status: 'pending'
        },
        include: {
          InstagramFollowUp: {
            where: {
              status: { in: ['pending', 'external'] } // Include both regular and conversation gathering follow-ups
            },
            orderBy: {
              scheduledTime: 'asc'
            }
          }
        },
        orderBy: [
          { nextMessageAt: 'asc' }, // Time first (null values first)
          { priority: 'desc' }      // Then priority (5=highest, 1=lowest)
        ]
      });

      // Categorize contacts
      const newFollowers = attackList.filter(c => c.priority === 3 && c.stage === 'new');
      const aiAnalyzedContacts = attackList.filter(c => c.priority !== 3 || c.stage !== 'new');

      // Count follow-ups
      const totalFollowUps = attackList.reduce((sum, contact) => sum + contact.InstagramFollowUp.length, 0);

      results.push({
        step: 'Final Attack List Summary',
        success: true,
        message: `Attack list ready: ${attackList.length} total contacts (${newFollowers.length} new followers, ${aiAnalyzedContacts.length} AI-analyzed)`,
        data: {
          totalContacts: attackList.length,
          newFollowers: newFollowers.length,
          aiAnalyzedContacts: aiAnalyzedContacts.length,
          totalFollowUps: totalFollowUps,
          attackList: attackList.map(contact => ({
            username: contact.instagramNickname,
            priority: contact.priority,
            stage: contact.stage,
            nextMessageAt: contact.nextMessageAt,
            followUpsCount: contact.InstagramFollowUp.length,
            category: contact.priority === 3 && contact.stage === 'new' ? 'new_follower' : 'ai_analyzed'
          }))
        }
      });

    } catch (error) {
      results.push({
        step: 'Final Attack List Summary',
        success: false,
        message: error instanceof Error ? error.message : 'Failed to generate attack list summary'
      });
    }

    return NextResponse.json({
      success: true,
      results,
      message: 'Conversation gathering flow test completed'
    });

  } catch (error) {
    console.error('Error in conversation gathering flow test:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}