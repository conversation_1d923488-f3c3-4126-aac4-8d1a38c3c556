import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

/**
 * Test endpoint to verify the new batch sequence flow
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Test the messages-to-send API
    const messagesResponse = await fetch(`${req.nextUrl.origin}/api/chrome-extension/messages-to-send`, {
      headers: {
        'X-API-Key': 'test-key' // You'll need to use a real API key
      }
    });

    let messagesData = null;
    if (messagesResponse.ok) {
      messagesData = await messagesResponse.json();
    }

    // Get sample contacts
    const sampleContacts = await prisma.instagramContact.findMany({
      where: {
        organizationId,
        priority: 3,
        stage: 'new',
        conversationSource: 'extension'
      },
      include: {
        InstagramFollowUp: {
          where: {
            status: { in: ['pending', 'external'] }
          }
        }
      },
      take: 5
    });

    // Get message batches
    const messageBatches = await prisma.messageBatch.findMany({
      where: { organizationId },
      include: {
        MessageBatchItem: {
          orderBy: { sequenceNumber: 'asc' }
        }
      }
    });

    // Get follow-up templates
    const followUpTemplates = await prisma.followUpTemplate.findMany({
      where: { organizationId },
      orderBy: [
        { sequenceNumber: 'asc' },
        { variationNumber: 'asc' }
      ]
    });

    return NextResponse.json({
      success: true,
      data: {
        organizationId,
        messageBatches: messageBatches.map(batch => ({
          id: batch.id,
          name: batch.name,
          messageCount: batch.MessageBatchItem.length,
          messages: batch.MessageBatchItem.map(item => ({
            sequence: item.sequenceNumber,
            text: item.messageText,
            delay: item.delayMinutes
          }))
        })),
        followUpTemplates: followUpTemplates.map(template => ({
          sequence: template.sequenceNumber,
          variation: template.variationNumber,
          delayHours: template.delayHours,
          text: template.messageText
        })),
        sampleContacts: sampleContacts.map(contact => ({
          id: contact.id,
          username: contact.instagramNickname,
          priority: contact.priority,
          stage: contact.stage,
          nextMessageAt: contact.nextMessageAt,
          followUpsCount: contact.InstagramFollowUp.length
        })),
        messagesApiResponse: messagesData
      }
    });

  } catch (error) {
    console.error('Error testing batch sequence flow:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Test marking a batch sequence as sent
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { contactId, batchCompleted = true } = body;

    if (!contactId) {
      return NextResponse.json(
        { success: false, error: 'contactId required' },
        { status: 400 }
      );
    }

    // Test the mark-sent API
    const markSentResponse = await fetch(`${req.nextUrl.origin}/api/chrome-extension/mark-sent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'test-key' // You'll need to use a real API key
      },
      body: JSON.stringify({
        contactId,
        messageType: 'batch_sequence',
        batchCompleted,
        sequenceNumber: 3 // Assuming 3 messages in batch
      })
    });

    let markSentData = null;
    if (markSentResponse.ok) {
      markSentData = await markSentResponse.json();
    }

    // Check if follow-ups were created
    const followUps = await prisma.instagramFollowUp.findMany({
      where: {
        contactId,
        status: { in: ['pending', 'external'] }
      },
      orderBy: { scheduledTime: 'asc' }
    });

    return NextResponse.json({
      success: true,
      data: {
        contactId,
        batchCompleted,
        markSentResponse: markSentData,
        followUpsCreated: followUps.length,
        followUps: followUps.map(fu => ({
          id: fu.id,
          message: fu.message,
          scheduledTime: fu.scheduledTime,
          sequenceNumber: fu.sequenceNumber,
          status: fu.status
        }))
      }
    });

  } catch (error) {
    console.error('Error testing mark sent:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
