import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

/**
 * Test endpoint to check and create follow-up templates if needed
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: userId
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Check existing templates
    const existingTemplates = await prisma.followUpTemplate.findMany({
      where: {
        organizationId
      },
      orderBy: [
        { sequenceNumber: 'asc' },
        { variationNumber: 'asc' }
      ]
    });

    return NextResponse.json({
      success: true,
      data: {
        organizationId,
        userId: userId,
        existingTemplates: existingTemplates.length,
        templates: existingTemplates
      }
    });

  } catch (error) {
    console.error('Error checking follow-up templates:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Create default follow-up templates if none exist
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: userId
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Check if templates already exist
    const existingCount = await prisma.followUpTemplate.count({
      where: { organizationId }
    });

    if (existingCount > 0) {
      return NextResponse.json({
        success: false,
        message: `${existingCount} templates already exist. Delete them first if you want to recreate.`
      });
    }

    // Create default templates
    const defaultTemplates = [
      // Sequence 1 (24h after first message)
      {
        messageText: "Hey! Just wanted to follow up on my previous message. Did you get a chance to check it out?",
        sequenceNumber: 1,
        variationNumber: 1,
        delayHours: 24
      },
      {
        messageText: "Hi again! I sent you a message yesterday. Would love to hear your thoughts!",
        sequenceNumber: 1,
        variationNumber: 2,
        delayHours: 24
      },
      // Sequence 2 (48h after first message)
      {
        messageText: "Hope you're doing well! Still interested in connecting?",
        sequenceNumber: 2,
        variationNumber: 1,
        delayHours: 48
      },
      {
        messageText: "Just checking in one more time. Let me know if you'd like to chat!",
        sequenceNumber: 2,
        variationNumber: 2,
        delayHours: 48
      },
      // Sequence 3 (72h after first message)
      {
        messageText: "Last follow-up from me! If you're interested, feel free to reach out anytime.",
        sequenceNumber: 3,
        variationNumber: 1,
        delayHours: 72
      }
    ];

    const created = await prisma.followUpTemplate.createMany({
      data: defaultTemplates.map(template => ({
        userId: userId,
        organizationId,
        messageText: template.messageText,
        sequenceNumber: template.sequenceNumber,
        variationNumber: template.variationNumber,
        delayHours: template.delayHours,
        isActive: true
      }))
    });

    return NextResponse.json({
      success: true,
      message: `Created ${created.count} default follow-up templates`,
      data: defaultTemplates
    });

  } catch (error) {
    console.error('Error creating follow-up templates:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
