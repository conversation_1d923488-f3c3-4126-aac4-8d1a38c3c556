import { NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';

export async function GET(): Promise<Response> {
  try {
    // Get basic queue statistics
    const totalConversations = await prisma.instagramConversationsNotGathered.count({
      where: {
        organizationId: '93ed4ff1-7ee6-4375-b987-7bc4fb6dc181'
      }
    });

    const gatheredConversations = await prisma.instagramConversationsNotGathered.count({
      where: {
        organizationId: '93ed4ff1-7ee6-4375-b987-7bc4fb6dc181',
        isGathered: true
      }
    });

    const pendingConversations = await prisma.instagramConversationsNotGathered.count({
      where: {
        organizationId: '93ed4ff1-7ee6-4375-b987-7bc4fb6dc181',
        isGathered: false
      }
    });

    // Get some sample pending conversations
    const samplePending = await prisma.instagramConversationsNotGathered.findMany({
      where: {
        organizationId: '93ed4ff1-7ee6-4375-b987-7bc4fb6dc181',
        isGathered: false
      },
      take: 5,
      select: {
        id: true,
        participantUsername: true,
        updatedTime: true,
        isGathered: true
      }
    });

    return NextResponse.json({
      success: true,
      queueStatus: {
        totalConversations,
        gatheredConversations,
        pendingConversations,
        percentComplete: totalConversations > 0 ? Math.round((gatheredConversations / totalConversations) * 100) : 0
      },
      samplePending,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error checking queue status:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check queue status' },
      { status: 500 }
    );
  }
} 