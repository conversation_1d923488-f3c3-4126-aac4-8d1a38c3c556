import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { followerProcessingQueue } from '~/lib/follower-processing-queue';

export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { action } = body;

    switch (action) {
      case 'start':
        // Start the queue processor
        await followerProcessingQueue.start();
        return NextResponse.json({
          success: true,
          message: 'Queue processor started successfully'
        });

      case 'stop':
        // Stop the queue processor
        await followerProcessingQueue.stop();
        return NextResponse.json({
          success: true,
          message: 'Queue processor stopped successfully'
        });

      case 'process_once':
        // Process queue once manually
        await followerProcessingQueue.processQueue();
        return NextResponse.json({
          success: true,
          message: 'Queue processing triggered manually'
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action. Use: start, stop, or process_once' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error with queue action:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 