import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

/**
 * Test endpoint for 5-day scraping interval functionality
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Get current settings
    const settings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    const now = new Date();
    
    // Test scenarios
    const scenarios = [];

    // Scenario 1: Fresh start (no settings)
    if (!settings) {
      scenarios.push({
        scenario: 'Fresh Start',
        isEligible: true,
        reason: 'No settings exist - first time scraping allowed'
      });
    } else {
      // Scenario 2: Check current eligibility
      const isCurrentlyEligible = !settings.nextScrapingAllowedAt || settings.nextScrapingAllowedAt <= now;
      scenarios.push({
        scenario: 'Current Status',
        isEligible: isCurrentlyEligible,
        nextAllowedAt: settings.nextScrapingAllowedAt,
        totalScraped: settings.totalFollowersScraped,
        lastSession: settings.lastScrapingSession,
        reason: isCurrentlyEligible ? 'Scraping allowed now' : 'Must wait for interval to pass'
      });

      // Scenario 3: Simulate completing 250 followers
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + (settings.scrapingIntervalDays || 5));
      
      scenarios.push({
        scenario: 'After 250 Followers Completed',
        nextAllowedAt: futureDate,
        intervalDays: settings.scrapingIntervalDays || 5,
        reason: `Next scraping would be allowed in ${settings.scrapingIntervalDays || 5} days`
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        organizationId,
        currentTime: now,
        settings: settings ? {
          extensionStatus: settings.extensionStatus,
          totalFollowersScraped: settings.totalFollowersScraped,
          lastScrapingSession: settings.lastScrapingSession,
          nextScrapingAllowedAt: settings.nextScrapingAllowedAt,
          scrapingIntervalDays: settings.scrapingIntervalDays,
          allFollowersScraped: settings.allFollowersScraped
        } : null,
        scenarios
      }
    });

  } catch (error) {
    console.error('Error testing scraping interval:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Reset scraping eligibility for testing
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const body = await req.json();
    const { action } = body;

    const organizationId = membership.organizationId;

    if (action === 'reset') {
      // Reset scraping eligibility (clear nextScrapingAllowedAt)
      await prisma.chromeExtensionSettings.upsert({
        where: { organizationId },
        update: {
          nextScrapingAllowedAt: null,
          extensionStatus: 'FRESH_START',
          totalFollowersScraped: 0,
          lastScrapingSession: null,
          allFollowersScraped: false,
          updatedAt: new Date()
        },
        create: {
          organizationId,
          nextScrapingAllowedAt: null,
          extensionStatus: 'FRESH_START',
          totalFollowersScraped: 0,
          scrapingIntervalDays: 5,
          timeBetweenDMsMin: 3,
          timeBetweenDMsMax: 8,
          messagesBeforeBreakMin: 8,
          messagesBeforeBreakMax: 15,
          breakDurationMin: 10,
          breakDurationMax: 20,
          pauseStart: "00:30",
          pauseStop: "07:00",
          smartFocus: true,
          isConnected: false
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Scraping eligibility reset - can scrape immediately'
      });
    }

    if (action === 'simulate_wait') {
      // Simulate having to wait 5 days
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 5);

      await prisma.chromeExtensionSettings.upsert({
        where: { organizationId },
        update: {
          nextScrapingAllowedAt: futureDate,
          extensionStatus: 'SCRAPED_250',
          totalFollowersScraped: 250,
          lastScrapingSession: new Date(),
          updatedAt: new Date()
        },
        create: {
          organizationId,
          nextScrapingAllowedAt: futureDate,
          extensionStatus: 'SCRAPED_250',
          totalFollowersScraped: 250,
          lastScrapingSession: new Date(),
          scrapingIntervalDays: 5,
          timeBetweenDMsMin: 3,
          timeBetweenDMsMax: 8,
          messagesBeforeBreakMin: 8,
          messagesBeforeBreakMax: 15,
          breakDurationMin: 10,
          breakDurationMax: 20,
          pauseStart: "00:30",
          pauseStop: "07:00",
          smartFocus: true,
          isConnected: false
        }
      });

      return NextResponse.json({
        success: true,
        message: `Simulated 5-day wait - next scraping allowed at ${futureDate.toISOString()}`
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action. Use "reset" or "simulate_wait"' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in scraping interval test:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
