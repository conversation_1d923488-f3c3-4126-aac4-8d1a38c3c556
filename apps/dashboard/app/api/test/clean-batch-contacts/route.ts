import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

/**
 * Clean up contacts that have incorrect follow-ups from batch processing
 * This fixes the inconsistent display issue
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Find contacts that are new followers (priority 3, stage new) with follow-ups
    // These should NOT have follow-ups yet - they should show batch messages
    const contactsToClean = await prisma.instagramContact.findMany({
      where: {
        organizationId,
        priority: 3,
        stage: 'new',
        conversationSource: 'extension'
      },
      include: {
        InstagramFollowUp: {
          where: {
            status: { in: ['pending', 'external'] }
          }
        }
      }
    });

    const results = {
      contactsChecked: contactsToClean.length,
      contactsCleaned: 0,
      followUpsRemoved: 0,
      contactsFixed: [] as string[]
    };

    for (const contact of contactsToClean) {
      if (contact.InstagramFollowUp.length > 0) {
        // This contact has follow-ups but shouldn't (it's a new follower without conversation)
        console.log(`Cleaning contact ${contact.instagramNickname}: removing ${contact.InstagramFollowUp.length} incorrect follow-ups`);

        // Remove the incorrect follow-ups
        const deletedFollowUps = await prisma.instagramFollowUp.deleteMany({
          where: {
            contactId: contact.id,
            status: { in: ['pending', 'external'] }
          }
        });

        // Reset the contact to be ready for immediate messaging
        await prisma.instagramContact.update({
          where: { id: contact.id },
          data: {
            nextMessageAt: new Date(), // Available NOW
            attackListStatus: 'pending',
            batchMessageStatus: null, // Reset batch status
            currentMessageSequence: null, // Reset sequence
            priority: 3, // Ensure priority is correct
            updatedAt: new Date()
          }
        });

        results.contactsCleaned++;
        results.followUpsRemoved += contact.InstagramFollowUp.length;
        results.contactsFixed.push(contact.instagramNickname);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Cleaned ${results.contactsCleaned} contacts, removed ${results.followUpsRemoved} incorrect follow-ups`,
      data: results
    });

  } catch (error) {
    console.error('Error cleaning batch contacts:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Get current state of batch contacts for debugging
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Get all new follower contacts
    const contacts = await prisma.instagramContact.findMany({
      where: {
        organizationId,
        priority: 3,
        stage: 'new',
        conversationSource: 'extension'
      },
      include: {
        InstagramFollowUp: {
          where: {
            status: { in: ['pending', 'external'] }
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    return NextResponse.json({
      success: true,
      data: {
        organizationId,
        contactsCount: contacts.length,
        contacts: contacts.map(contact => ({
          id: contact.id,
          username: contact.instagramNickname,
          priority: contact.priority,
          stage: contact.stage,
          nextMessageAt: contact.nextMessageAt,
          attackListStatus: contact.attackListStatus,
          batchMessageStatus: contact.batchMessageStatus,
          currentMessageSequence: contact.currentMessageSequence,
          followUpsCount: contact.InstagramFollowUp.length,
          followUps: contact.InstagramFollowUp.map(fu => ({
            id: fu.id,
            message: fu.message.substring(0, 50) + '...',
            scheduledTime: fu.scheduledTime,
            status: fu.status,
            sequenceNumber: fu.sequenceNumber
          }))
        }))
      }
    });

  } catch (error) {
    console.error('Error getting batch contacts state:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
