import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';

export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Find martyna_konieczna contact
    const contact = await prisma.instagramContact.findFirst({
      where: {
        instagramNickname: 'martyna_konieczna'
      },
      include: {
        InstagramFollowUp: true
      }
    });

    if (!contact) {
      return NextResponse.json({
        success: false,
        error: 'Contact not found'
      });
    }

    // Get follow-up templates for the organization
    const followUpTemplates = await prisma.followUpTemplate.findMany({
      where: {
        organizationId: contact.organizationId
      },
      orderBy: [
        { sequenceNumber: 'asc' },
        { variationNumber: 'asc' }
      ]
    });

    return NextResponse.json({
      success: true,
      data: {
        contact: {
          id: contact.id,
          instagramNickname: contact.instagramNickname,
          priority: contact.priority,
          batchMessageStatus: contact.batchMessageStatus,
          currentMessageSequence: contact.currentMessageSequence,
          lastMessageSentAt: contact.lastMessageSentAt,
          attackListStatus: contact.attackListStatus,
          nextMessageAt: contact.nextMessageAt
        },
        existingFollowUps: contact.InstagramFollowUp,
        followUpTemplates: followUpTemplates
      }
    });

  } catch (error) {
    console.error('Error debugging martyna contact:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest): Promise<Response> {
  try {
    const body = await req.json();
    const { action } = body;

    if (action === 'trigger_followups') {
      // Find martyna_konieczna contact
      const contact = await prisma.instagramContact.findFirst({
        where: {
          instagramNickname: 'martyna_konieczna'
        }
      });

      if (!contact) {
        return NextResponse.json({
          success: false,
          error: 'Contact not found'
        });
      }

      // Get follow-up templates for the organization
      const followUpTemplates = await prisma.followUpTemplate.findMany({
        where: {
          organizationId: contact.organizationId
        },
        orderBy: [
          { sequenceNumber: 'asc' },
          { variationNumber: 'asc' }
        ]
      });

      console.log(`Found ${followUpTemplates.length} follow-up templates for organization ${contact.organizationId}`);

      // Check if follow-ups already exist
      const existingFollowUps = await prisma.instagramFollowUp.findMany({
        where: {
          contactId: contact.id,
          status: { in: ['pending', 'external'] }
        }
      });

      if (existingFollowUps.length > 0) {
        return NextResponse.json({
          success: false,
          error: 'Follow-ups already exist for this contact',
          data: { existingFollowUps }
        });
      }

      // Group templates by sequence number
      const templatesBySequence = followUpTemplates.reduce((acc, template) => {
        if (!acc[template.sequenceNumber]) {
          acc[template.sequenceNumber] = [];
        }
        acc[template.sequenceNumber].push(template);
        return acc;
      }, {} as Record<number, typeof followUpTemplates>);

      const sentTime = new Date();
      const createdFollowUps = [];

      // Create follow-ups for each sequence
      for (let sequenceNumber = 1; sequenceNumber <= 3; sequenceNumber++) {
        const sequenceTemplates = templatesBySequence[sequenceNumber];
        if (sequenceTemplates && sequenceTemplates.length > 0) {
          // Pick a random variation for this sequence
          const randomTemplate = sequenceTemplates[Math.floor(Math.random() * sequenceTemplates.length)];

          // Calculate scheduled time based on template delay
          const scheduledTime = new Date(sentTime.getTime() + (randomTemplate.delayHours * 60 * 60 * 1000));

          const followUp = await prisma.instagramFollowUp.create({
            data: {
              contactId: contact.id,
              message: randomTemplate.messageText,
              scheduledTime: scheduledTime,
              status: 'external', // External so it appears in attack list
              sequenceNumber: sequenceNumber + 1 // Follow-up sequence starts from 2
            }
          });

          createdFollowUps.push(followUp);
        }
      }

      // Update contact status
      const nextFollowUp = await prisma.instagramFollowUp.findFirst({
        where: {
          contactId: contact.id,
          status: { in: ['pending', 'external'] }
        },
        orderBy: { scheduledTime: 'asc' }
      });

      const updateData: any = {
        priority: 1, // Change priority to 1 for follow-ups
        batchMessageStatus: 'sent',
        lastInteractionAt: sentTime,
        lastMessageSentAt: sentTime,
        updatedAt: sentTime
      };

      if (nextFollowUp) {
        updateData.nextMessageAt = nextFollowUp.scheduledTime;
        updateData.attackListStatus = 'pending';
      } else {
        updateData.attackListStatus = 'completed';
        updateData.nextMessageAt = null;
      }

      const updatedContact = await prisma.instagramContact.update({
        where: { id: contact.id },
        data: updateData
      });

      return NextResponse.json({
        success: true,
        message: `Created ${createdFollowUps.length} follow-ups for martyna_konieczna`,
        data: {
          createdFollowUps,
          updatedContact: {
            id: updatedContact.id,
            priority: updatedContact.priority,
            attackListStatus: updatedContact.attackListStatus,
            nextMessageAt: updatedContact.nextMessageAt
          }
        }
      });
    }

    return NextResponse.json({
      success: false,
      error: 'Invalid action'
    });

  } catch (error) {
    console.error('Error in POST debug martyna:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
