import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';

export async function GET(req: NextRequest): Promise<Response> {
  try {
    const organizationSlug = req.nextUrl.searchParams.get('slug');
    
    console.log('🔍 DEBUG: Looking for organization with slug:', organizationSlug);
    
    if (!organizationSlug) {
      return NextResponse.json({ 
        success: false, 
        error: 'Missing slug parameter' 
      }, { status: 400 });
    }

    // Find organization by slug
    const organization = await prisma.organization.findFirst({
      where: { 
        slug: organizationSlug 
      },
      select: {
        id: true,
        name: true,
        slug: true
      }
    });

    if (!organization) {
      return NextResponse.json({
        success: false,
        error: `Organization with slug '${organizationSlug}' not found`
      }, { status: 404 });
    }

    console.log('✅ Found organization:', {
      id: organization.id,
      name: organization.name,
      slug: organization.slug
    });

    return NextResponse.json({
      success: true,
              organization: {
          id: organization.id,
          name: organization.name,
          slug: organization.slug
        }
    });

  } catch (error) {
    console.error('❌ Error getting organization by slug:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
} 