import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { getAllConversations, getConversationMessages } from '~/lib/instagram-client';
import { generateInstagramResponse } from '@workspace/instagram-bot';

export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Get Instagram settings
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: {
        organizationId,
        instagramToken: { not: null }
      }
    });

    if (!instagramSettings?.instagramToken) {
      return NextResponse.json(
        { success: false, error: 'Instagram token not configured' },
        { status: 400 }
      );
    }

    // Get all conversations that are not gathered
    const notGatheredConversations = await prisma.instagramConversationsNotGathered.findMany({
      where: {
        organizationId,
        isGathered: false
      },
      orderBy: {
        updatedTime: 'desc'
      }
    });

    console.log(`🔄 Found ${notGatheredConversations.length} conversations to process`);

    if (notGatheredConversations.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No conversations to process',
        processed: 0
      });
    }

    let processedCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    // Process each conversation
    for (const conversation of notGatheredConversations) {
      try {
        console.log(`🔄 Processing conversation for ${conversation.participantUsername}...`);

        // Get conversation messages from Instagram API
        const conversationResponse = await getConversationMessages(
          conversation.instagramConversationId,
          instagramSettings.instagramToken
        );

        if (!conversationResponse?.data?.[0]?.messages?.data) {
          console.log(`⚠️ No messages found for ${conversation.participantUsername}`);
          
          // Mark as gathered even if no messages (to avoid infinite retry)
          await prisma.instagramConversationsNotGathered.update({
            where: { id: conversation.id },
            data: { isGathered: true }
          });
          
          processedCount++;
          continue;
        }

        const messages = conversationResponse.data[0].messages.data;
        console.log(`📝 Found ${messages.length} messages for ${conversation.participantUsername}`);

        // Format conversation history for AI analysis
        const sortedMessages = messages.sort((a: any, b: any) =>
          new Date(a.created_time).getTime() - new Date(b.created_time).getTime()
        );

        // Filter out extension messages
        const filteredMessages = sortedMessages.filter((msg: any) => !msg.isFromExtension);
        
        let conversationHistory = filteredMessages.map((msg: any) => {
          const sender = msg.from?.username || msg.from?.id || 'Unknown';
          const messageText = msg.message || '[Media/Attachment]';
          return `${sender}: ${messageText}`;
        }).join('\n');

        // Add last user interaction timestamp
        const lastUserMessage = sortedMessages
          .filter((msg: any) => msg.from?.username === conversation.participantUsername)
          .pop();

        if (lastUserMessage) {
          const lastInteractionTime = new Date(lastUserMessage.created_time);
          conversationHistory += `\n\nLAST USER INTERACTION: ${lastInteractionTime.toISOString()}`;
        }

        // Analyze with AI
        console.log(`🤖 Analyzing conversation with AI for ${conversation.participantUsername}...`);
        const aiResponse = await generateInstagramResponse({
          prompt: "CONVERSATION_GATHERING",
          conversationHistory: conversationHistory,
          organizationId: organizationId
        });

        console.log(`✅ AI analysis for ${conversation.participantUsername}:`, {
          stage: aiResponse.stage,
          priority: aiResponse.priority,
          followUpsCount: aiResponse.followUps?.length || 0
        });

        // Check if contact already exists
        const existingContact = await prisma.instagramContact.findFirst({
          where: {
            organizationId,
            instagramNickname: conversation.participantUsername
          }
        });

        if (existingContact) {
          console.log(`⚠️ Contact already exists for ${conversation.participantUsername}, updating...`);
          
          // Update existing contact with new conversation data
          await prisma.instagramContact.update({
            where: { id: existingContact.id },
            data: {
              stage: (aiResponse.stage as any) || existingContact.stage,
              priority: aiResponse.priority || existingContact.priority,
              lastInteractionAt: new Date(conversation.updatedTime),
              updatedAt: new Date()
            }
          });

          // Save messages to database
          for (const msg of messages) {
            try {
              const existingMessage = await prisma.instagramMessage.findFirst({
                where: {
                  contactId: existingContact.id,
                  messageId: msg.id
                }
              });

              if (!existingMessage) {
                await prisma.instagramMessage.create({
                  data: {
                    contactId: existingContact.id,
                    messageId: msg.id,
                    content: msg.message || '[Media/Attachment]',
                    isFromUser: msg.from?.username === conversation.participantUsername,
                    isFromExtension: msg.isFromExtension || false,
                    timestamp: new Date(msg.created_time),
                    mediaType: msg.attachments?.[0]?.mime_type || null,
                    mediaUrl: msg.attachments?.[0]?.file_url || null
                  }
                });
              }
            } catch (error) {
              console.error(`Error saving message ${msg.id}:`, error);
            }
          }
        } else {
          // Create new contact
          console.log(`✅ Creating new contact for ${conversation.participantUsername}...`);
          
          const newContact = await prisma.instagramContact.create({
            data: {
              organizationId,
              userId: session.user.id,
              instagramNickname: conversation.participantUsername,
              instagramId: conversation.participantId,
              stage: (aiResponse.stage as any) || 'new',
              priority: aiResponse.priority || 3,
              conversationSource: 'api',
              lastInteractionAt: new Date(conversation.updatedTime)
            }
          });

          // Save messages to database
          for (const msg of messages) {
            try {
              await prisma.instagramMessage.create({
                data: {
                  contactId: newContact.id,
                  messageId: msg.id,
                  content: msg.message || '[Media/Attachment]',
                  isFromUser: msg.from?.username === conversation.participantUsername,
                  isFromExtension: msg.isFromExtension || false,
                  timestamp: new Date(msg.created_time),
                  mediaType: msg.attachments?.[0]?.mime_type || null,
                  mediaUrl: msg.attachments?.[0]?.file_url || null
                }
              });
            } catch (error) {
              console.error(`Error saving message ${msg.id}:`, error);
            }
          }

          // Create follow-ups if AI generated them
          if (aiResponse.followUps && aiResponse.followUps.length > 0) {
            for (let i = 0; i < aiResponse.followUps.length; i++) {
              const followUp = aiResponse.followUps[i];
              const scheduledTime = new Date();
              scheduledTime.setHours(scheduledTime.getHours() + (followUp.delayHours || 24));
              
              await prisma.instagramFollowUp.create({
                data: {
                  contactId: newContact.id,
                  sequenceNumber: i + 1,
                  message: followUp.message,
                  scheduledTime: scheduledTime,
                  status: 'pending'
                }
              });
            }
          }
        }

        // Mark conversation as gathered
        await prisma.instagramConversationsNotGathered.update({
          where: { id: conversation.id },
          data: { isGathered: true }
        });

        processedCount++;
        console.log(`✅ Successfully processed conversation for ${conversation.participantUsername}`);

        // Add delay between conversations to avoid rate limiting
        if (processedCount < notGatheredConversations.length) {
          console.log(`⏳ Waiting 2 seconds before processing next conversation...`);
          await new Promise(resolve => setTimeout(resolve, 2000));
        }

      } catch (error) {
        console.error(`❌ Error processing conversation for ${conversation.participantUsername}:`, error);
        errorCount++;
        errors.push(`${conversation.participantUsername}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Processed ${processedCount} conversations, ${errorCount} errors`,
      data: {
        processed: processedCount,
        errors: errorCount,
        errorDetails: errors
      }
    });

  } catch (error) {
    console.error('Error processing stuck conversations:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 