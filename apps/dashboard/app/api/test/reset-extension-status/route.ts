import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';

export async function POST(req: NextRequest): Promise<Response> {
  try {
    const body = await req.json();
    const { organizationId, newStatus = 'FRESH_START' } = body;
    
    if (!organizationId) {
      return NextResponse.json({ 
        success: false, 
        error: 'Missing organizationId parameter' 
      }, { status: 400 });
    }

    // Reset Chrome Extension settings
    const settings = await prisma.chromeExtensionSettings.upsert({
      where: { organizationId },
      update: {
        extensionStatus: newStatus,
        currentActivity: 'Status reset by admin',
        lastActivityAt: new Date(),
        isConnected: true,
        lastConnectionAt: new Date(),
        // Reset tracking fields
        totalFollowersScraped: 0,
        lastScrapedPosition: 0,
        lastScrapedUsernames: [],
        scrapingTargetReached: false,
        allFollowersScraped: false,
        lastScrapingSession: null
      },
      create: {
        organizationId,
        extensionStatus: newStatus,
        currentActivity: 'Status reset by admin',
        lastActivityAt: new Date(),
        isConnected: true,
        lastConnectionAt: new Date(),
        // Default tracking fields
        totalFollowersScraped: 0,
        lastScrapedPosition: 0,
        lastScrapedUsernames: [],
        scrapingTargetReached: false,
        allFollowersScraped: false,
        lastScrapingSession: null,
        // Default settings
        timeBetweenDMsMin: 3,
        timeBetweenDMsMax: 8,
        messagesBeforeBreakMin: 8,
        messagesBeforeBreakMax: 15,
        breakDurationMin: 10,
        breakDurationMax: 20,
        pauseStart: "00:30",
        pauseStop: "07:00",
        smartFocus: true
      }
    });

    // Clear conversation gathering data if requested
    if (newStatus === 'FRESH_START') {
      await prisma.instagramConversationsNotGathered.deleteMany({
        where: { organizationId }
      });
      
      await prisma.followerProcessingQueue.deleteMany({
        where: { organizationId }
      });
    }

    return NextResponse.json({
      success: true,
      message: `Extension status reset to ${newStatus}`,
      data: {
        organizationId,
        newStatus: settings.extensionStatus,
        currentActivity: settings.currentActivity,
        lastActivityAt: settings.lastActivityAt
      }
    });

  } catch (error) {
    console.error('Error resetting extension status:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 