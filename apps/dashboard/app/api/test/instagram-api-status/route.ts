import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@workspace/database/client';
import { getAllConversations, getBusinessAccountInfo } from '~/lib/instagram-client';

export async function GET(req: NextRequest): Promise<Response> {
  try {
    const organizationId = req.nextUrl.searchParams.get('organizationId');
    
    console.log('🔍 DEBUG: Received organizationId:', organizationId);
    console.log('🔍 DEBUG: organizationId type:', typeof organizationId);
    console.log('🔍 DEBUG: organizationId length:', organizationId?.length);
    console.log('🔍 DEBUG: organizationId first 10 chars:', organizationId?.substring(0, 10));
    
    if (!organizationId) {
      return NextResponse.json({ 
        success: false, 
        error: 'Missing organizationId parameter' 
      }, { status: 400 });
    }
    
    // Validate UUID format
    if (!organizationId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      return NextResponse.json({ 
        success: false, 
        error: `Invalid organizationId format. Expected UUID, got: "${organizationId}" (length: ${organizationId.length})` 
      }, { status: 400 });
    }

    // Get Instagram settings
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: { 
        organizationId,
        instagramToken: { not: null }
      }
    });

    if (!instagramSettings?.instagramToken) {
      return NextResponse.json({
        success: false,
        error: 'No Instagram token found for this organization'
      });
    }

    const results = {
      tokenExists: !!instagramSettings.instagramToken,
      tokenLength: instagramSettings.instagramToken?.length || 0,
      tokenPrefix: instagramSettings.instagramToken?.slice(0, 8) || 'N/A',
      businessAccountTest: null as any,
      conversationsTest: null as any,
      databaseStatus: null as any
    };

    // Test 1: Business Account Info
    try {
      console.log('Testing business account info...');
      const businessAccount = await getBusinessAccountInfo(instagramSettings.instagramToken);
      results.businessAccountTest = {
        success: true,
        data: businessAccount
      };
    } catch (error: any) {
      console.error('Business account test failed:', error);
      results.businessAccountTest = {
        success: false,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      };
    }

    // Test 2: Conversations API
    try {
      console.log('Testing conversations API...');
      const conversations = await getAllConversations(instagramSettings.instagramToken);
      results.conversationsTest = {
        success: true,
        totalConversations: conversations.data?.length || 0,
        hasData: !!conversations.data,
        rawResponse: conversations
      };
    } catch (error: any) {
      console.error('Conversations test failed:', error);
      results.conversationsTest = {
        success: false,
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      };
    }

    // Test 3: Database Status
    try {
      const [totalFollowers, totalContacts, totalConversationsNotGathered, gatheredConversations] = await Promise.all([
        prisma.instagramFollower.count({ where: { organizationId } }),
        prisma.instagramContact.count({ where: { organizationId } }),
        prisma.instagramConversationsNotGathered.count({ where: { organizationId } }),
        prisma.instagramConversationsNotGathered.count({ 
          where: { 
            organizationId,
            isGathered: true 
          } 
        })
      ]);

      results.databaseStatus = {
        success: true,
        totalFollowers,
        totalContacts,
        totalConversationsNotGathered,
        gatheredConversations,
        pendingConversations: totalConversationsNotGathered - gatheredConversations
      };
    } catch (error: any) {
      console.error('Database status test failed:', error);
      results.databaseStatus = {
        success: false,
        error: error.message
      };
    }

    return NextResponse.json({
      success: true,
      organizationId,
      results
    });

  } catch (error) {
    console.error('Instagram API status test failed:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 