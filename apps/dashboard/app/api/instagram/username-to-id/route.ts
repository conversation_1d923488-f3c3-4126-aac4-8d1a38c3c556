import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { getUserIdFromUsername, getUsernameFromUserId } from '~/lib/instagram-client';

const UsernameRequestSchema = z.object({
  username: z.string().min(1, 'Username is required').regex(/^[a-zA-Z0-9._]+$/, 'Invalid Instagram username format')
});

/**
 * Convert Instagram username to numeric user ID
 * POST /api/instagram/username-to-id
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = UsernameRequestSchema.parse(body);

    console.log('Converting Instagram username to ID:', validatedData.username);

    // Call the Instagram client function
    const userId = await getUserIdFromUsername(validatedData.username);

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found or Instagram API error',
          details: 'The username might not exist, be private, or Instagram is rate limiting requests'
        },
        { status: 404 }
      );
    }

    console.log(`Successfully converted @${validatedData.username} to ID: ${userId}`);

    return NextResponse.json({
      success: true,
      data: {
        username: validatedData.username,
        userId: userId,
        convertedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in username-to-id API:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: error.errors.map(e => e.message).join(', ')
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
