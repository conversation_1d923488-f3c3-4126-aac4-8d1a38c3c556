import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

const CreateFollowUpTemplatesSchema = z.object({
  templates: z.array(z.object({
    messageText: z.string().min(1),
    sequenceNumber: z.number().int().min(1).max(3),
    variationNumber: z.number().int().min(1).max(5),
    delayHours: z.number().int().min(1).max(168).default(24) // max 1 week
  })).min(1).max(15) // 3 sequences * 5 variations = max 15 templates
});

/**
 * Create or update follow-up templates
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const body = await req.json();
    const validatedData = CreateFollowUpTemplatesSchema.parse(body);

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: userId
      },
      include: {
        organization: true
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    // Use transaction to ensure atomicity and prevent race conditions
    const templates = await prisma.$transaction(async (tx) => {
      // Delete existing templates for this organization
      await tx.followUpTemplate.deleteMany({
        where: {
          organizationId: membership.organizationId
        }
      });

      // Create new templates
      return await tx.followUpTemplate.createMany({
        data: validatedData.templates.map(template => ({
          userId: userId,
          organizationId: membership.organizationId,
          messageText: template.messageText,
          sequenceNumber: template.sequenceNumber,
          variationNumber: template.variationNumber,
          delayHours: template.delayHours
        }))
      });
    });

    return NextResponse.json({
      success: true,
      message: `Created ${templates.count} follow-up templates`
    });

  } catch (error) {
    console.error('Error creating follow-up templates:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid data format', details: error.errors },
        { status: 400 }
      );
    }

    // Handle Prisma unique constraint violations
    if (error && typeof error === 'object' && 'code' in error) {
      if (error.code === 'P2002') {
        return NextResponse.json(
          { success: false, error: 'Duplicate template detected. Please try again.' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Get follow-up templates for organization
 */
export async function GET(): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: userId
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const templates = await prisma.followUpTemplate.findMany({
      where: {
        organizationId: membership.organizationId
      },
      orderBy: [
        { sequenceNumber: 'asc' },
        { variationNumber: 'asc' }
      ]
    });

    return NextResponse.json({
      success: true,
      data: templates
    });

  } catch (error) {
    console.error('Error fetching follow-up templates:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
