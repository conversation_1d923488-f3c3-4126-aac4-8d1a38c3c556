import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';
import { generateInstagramResponse } from '@workspace/instagram-bot';
import { getAllConversations, getConversationMessages } from '~/lib/instagram-client';

const ProcessNewFollowersSchema = z.object({
  batchSize: z.number().int().min(1).max(100).default(50),
  organizationId: z.string().uuid().optional()
});

/**
 * Process new Instagram followers from InstagramFollower table
 * This is the trigger that converts InstagramFollower -> InstagramContact
 * with full conversation gathering workflow
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = ProcessNewFollowersSchema.parse(body);

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Get Instagram settings for API access
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: {
        organizationId,
        instagramToken: { not: null }
      }
    });

    if (!instagramSettings?.instagramToken) {
      return NextResponse.json(
        { success: false, error: 'No Instagram token found. Please configure Instagram settings first.' },
        { status: 400 }
      );
    }

    // Get pending followers that haven't been processed yet
    const pendingFollowers = await prisma.instagramFollower.findMany({
      where: {
        organizationId,
        status: 'pending',
        automationEnabled: true
      },
      take: validatedData.batchSize,
      orderBy: {
        createdAt: 'asc' // Process oldest first
      }
    });

    if (pendingFollowers.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No pending followers to process',
        data: {
          processed: 0,
          newContacts: 0,
          conversationsFound: 0
        }
      });
    }

    console.log(`Processing ${pendingFollowers.length} pending followers...`);

    // Get all conversations from Instagram API (cache this for efficiency)
    console.log('Getting all conversations from Instagram API...');
    const instagramApiResponse = await getAllConversations(instagramSettings.instagramToken);
    const instagramApiConversations = instagramApiResponse.data || [];

    // Create conversation map for quick lookup
    const conversationMap = new Map();
    instagramApiConversations.forEach((conversation: any) => {
      const participants = conversation.participants || [];
      if (participants.length >= 2) {
        // Skip the first participant (business account) and take the second one
        const participant = participants[1];
        if (participant.username) {
          conversationMap.set(participant.username, {
            conversationId: conversation.id,
            lastUpdated: conversation.updated_time,
            participantId: participant.id
          });
        }
      }
    });

    // Get available message batches for new followers
    const messageBatches = await prisma.messageBatch.findMany({
      where: {
        organizationId,
        isActive: true
      },
      include: {
        MessageBatchItem: {
          orderBy: { sequenceNumber: 'asc' }
        }
      }
    });

    const batchesWithMessages = messageBatches.filter(
      batch => batch.MessageBatchItem.length > 0
    );

    // Process each follower
    const results = {
      processed: 0,
      newContacts: 0,
      conversationsFound: 0,
      errors: [] as string[]
    };

    for (const follower of pendingFollowers) {
      try {
        results.processed++;

        // Check if InstagramContact already exists
        const existingContact = await prisma.instagramContact.findFirst({
          where: {
            organizationId,
            instagramNickname: follower.instagramNickname
          }
        });

        if (existingContact) {
          // Mark follower as contacted
          await prisma.instagramFollower.update({
            where: { id: follower.id },
            data: { 
              status: 'contacted',
              isContacted: true,
              updatedAt: new Date()
            }
          });
          continue;
        }

        // Check if this follower has a conversation
        const conversationData = conversationMap.get(follower.instagramNickname);
        const hasConversation = !!conversationData;

        if (!hasConversation) {
          // No conversation - create contact with priority 3 and batch messages
          const randomBatch = batchesWithMessages.length > 0 
            ? batchesWithMessages[Math.floor(Math.random() * batchesWithMessages.length)]
            : null;

          const newContact = await prisma.instagramContact.create({
            data: {
              organizationId,
              userId: session.user.id,
              instagramId: follower.instagramId,
              instagramNickname: follower.instagramNickname,
              avatar: follower.avatar,
              followerCount: follower.followerCount,
              isVerifiedUser: follower.isVerified,
              stage: 'new',
              priority: 3, // New followers get priority 3
              status: 'pending',
              messageCount: 0,
              isIgnored: false,
              isTakeControl: false,
              isConversionLinkSent: false,
              nextMessageAt: new Date(), // Ready to message immediately
              attackListStatus: 'pending',
              conversationSource: 'extension',
              batchId: randomBatch?.id
            }
          });

          // Create follow-ups from batch messages if available
          if (randomBatch && randomBatch.MessageBatchItem.length > 1) {
            for (const batchItem of randomBatch.MessageBatchItem) {
              if (batchItem.sequenceNumber > 1) { // Skip first message (it's the initial message)
                await prisma.instagramFollowUp.create({
                  data: {
                    contactId: newContact.id,
                    message: batchItem.messageText,
                    scheduledTime: new Date(Date.now() + ((batchItem.sequenceNumber - 1) * 24 * 60 * 60 * 1000)),
                    status: 'external', // External so it appears in attack list
                    sequenceNumber: batchItem.sequenceNumber - 1
                  }
                });
              }
            }
          }

          results.newContacts++;
        } else {
          // Has conversation - get full conversation and analyze with AI
          results.conversationsFound++;

          console.log(`Getting conversation messages for ${follower.instagramNickname}...`);
          const conversationResponse = await getConversationMessages(conversationData.conversationId, instagramSettings.instagramToken);

          // Format conversation history for AI
          let conversationHistory = '';
          if (conversationResponse && conversationResponse.data && conversationResponse.data.length > 0) {
            const conversation = conversationResponse.data[0];
            const messages = conversation.messages?.data || [];

            if (messages.length > 0) {
              const sortedMessages = messages.sort((a: any, b: any) =>
                new Date(a.created_time).getTime() - new Date(b.created_time).getTime()
              );

              conversationHistory = sortedMessages.map((msg: any) => {
                const sender = msg.from?.username || msg.from?.id || 'Unknown';
                const messageText = msg.message || '[Media/Attachment]';
                return `${sender}: ${messageText}`;
              }).join('\n');

              // Add last user interaction timestamp
              const lastUserMessage = sortedMessages
                .filter((msg: any) => msg.from?.username === follower.instagramNickname)
                .pop();

              if (lastUserMessage) {
                const lastInteractionTime = new Date(lastUserMessage.created_time);
                conversationHistory += `\n\nLAST USER INTERACTION: ${lastInteractionTime.toISOString()}`;
              }
            } else {
              conversationHistory = 'No messages found in conversation';
            }
          } else {
            conversationHistory = 'No messages found in conversation';
          }

          // Pass to AI for analysis with CONVERSATION GATHERING mode
          console.log(`Analyzing conversation with AI for ${follower.instagramNickname}...`);
          const aiResponse = await generateInstagramResponse({
            prompt: "CONVERSATION GATHERING",
            conversationHistory: conversationHistory,
            organizationId: organizationId
          });

          const aiPriority = aiResponse.priority || 3;
          const aiStage = aiResponse.stage || 'initial';

          const newContact = await prisma.instagramContact.create({
            data: {
              organizationId,
              userId: session.user.id,
              instagramId: follower.instagramId,
              instagramNickname: follower.instagramNickname,
              avatar: follower.avatar,
              followerCount: follower.followerCount,
              isVerifiedUser: follower.isVerified,
              stage: aiStage as any,
              priority: aiPriority,
              status: 'pending',
              messageCount: conversationResponse?.data?.[0]?.messages?.data?.length || 0,
              isIgnored: false,
              isTakeControl: false,
              isConversionLinkSent: false,
              nextMessageAt: new Date(), // Ready for follow-up
              attackListStatus: 'pending',
              conversationSource: 'extension'
            }
          });

          // Save conversation messages to database
          const messages = conversationResponse?.data?.[0]?.messages?.data || [];
          if (messages.length > 0) {
            for (const msg of messages) {
              try {
                await prisma.instagramMessage.create({
                  data: {
                    contactId: newContact.id,
                    messageId: msg.id,
                    content: msg.message || '[Media/Attachment]',
                    isFromUser: msg.from?.username === follower.instagramNickname,
                    isFromExtension: true,
                    timestamp: new Date(msg.created_time),
                    mediaType: msg.attachments?.[0]?.mime_type || null,
                    mediaUrl: msg.attachments?.[0]?.file_url || null
                  }
                });
              } catch (error) {
                console.error(`Error saving message ${msg.id}:`, error);
              }
            }
          }

          // Add AI-generated follow-ups if provided
          if (aiResponse.followUps && aiResponse.followUps.length > 0) {
            for (let i = 0; i < aiResponse.followUps.length; i++) {
              const followUp = aiResponse.followUps[i];
              await prisma.instagramFollowUp.create({
                data: {
                  contactId: newContact.id,
                  message: followUp.message,
                  scheduledTime: new Date(followUp.delayHours ? Date.now() + (followUp.delayHours * 60 * 60 * 1000) : Date.now() + (24 * 60 * 60 * 1000)),
                  status: 'external', // External so it appears in attack list
                  sequenceNumber: i + 1
                }
              });
            }
          }

          results.newContacts++;
        }

        // Mark follower as contacted
        await prisma.instagramFollower.update({
          where: { id: follower.id },
          data: { 
            status: 'contacted',
            isContacted: true,
            updatedAt: new Date()
          }
        });

        // Add small delay to prevent overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay

      } catch (error) {
        console.error(`Error processing follower ${follower.instagramNickname}:`, error);
        results.errors.push(`Failed to process ${follower.instagramNickname}: ${error}`);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully processed ${results.processed} followers`,
      data: results
    });

  } catch (error) {
    console.error('Error processing new followers:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Get processing status for new followers
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Get counts
    const pendingFollowers = await prisma.instagramFollower.count({
      where: {
        organizationId,
        status: 'pending',
        automationEnabled: true
      }
    });

    const contactedFollowers = await prisma.instagramFollower.count({
      where: {
        organizationId,
        status: 'contacted'
      }
    });

    const totalContacts = await prisma.instagramContact.count({
      where: { organizationId }
    });

    const attackListCount = await prisma.instagramContact.count({
      where: {
        organizationId,
        attackListStatus: 'pending'
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        pendingFollowers,
        contactedFollowers,
        totalContacts,
        attackListCount,
        needsProcessing: pendingFollowers > 0
      }
    });

  } catch (error) {
    console.error('Error getting processing status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
