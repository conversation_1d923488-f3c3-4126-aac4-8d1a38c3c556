import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@workspace/database/client';

/**
 * Auto-process new Instagram followers for all organizations
 * This endpoint can be called by a cron job or webhook
 * No authentication required - internal system endpoint
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    console.log('🚀 Starting auto-process for all organizations...');

    // Get all organizations that have pending followers and Instagram settings
    const organizationsWithPendingFollowers = await prisma.organization.findMany({
      where: {
        InstagramFollower: {
          some: {
            status: 'pending',
            automationEnabled: true
          }
        },
        InstagramSettings: {
          instagramToken: { not: null }
        }
      },
      include: {
        InstagramSettings: true,
        InstagramFollower: {
          where: {
            status: 'pending',
            automationEnabled: true
          },
          take: 1 // Just to check if any exist
        }
      }
    });

    console.log(`Found ${organizationsWithPendingFollowers.length} organizations with pending followers`);

    const results = {
      organizationsProcessed: 0,
      totalFollowersProcessed: 0,
      totalContactsCreated: 0,
      errors: [] as string[]
    };

    for (const organization of organizationsWithPendingFollowers) {
      try {
        console.log(`Processing organization: ${organization.name} (${organization.id})`);

        // Get a user from this organization to use for processing
        const membership = await prisma.membership.findFirst({
          where: {
            organizationId: organization.id,
            role: 'ADMIN'
          },
          include: {
            user: true
          }
        });

        if (!membership) {
          console.log(`No admin user found for organization ${organization.id}`);
          continue;
        }

        // Call the process-new-followers endpoint internally
        const processResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/instagram/process-new-followers`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.INTERNAL_API_KEY || 'internal'}` // Use internal API key
          },
          body: JSON.stringify({
            batchSize: 50,
            organizationId: organization.id
          })
        });

        if (processResponse.ok) {
          const processResult = await processResponse.json();
          if (processResult.success) {
            results.organizationsProcessed++;
            results.totalFollowersProcessed += processResult.data.processed || 0;
            results.totalContactsCreated += processResult.data.newContacts || 0;
            console.log(`✅ Processed ${processResult.data.processed} followers for ${organization.name}`);
          } else {
            console.error(`❌ Failed to process followers for ${organization.name}:`, processResult.error);
            results.errors.push(`${organization.name}: ${processResult.error}`);
          }
        } else {
          console.error(`❌ HTTP error processing ${organization.name}:`, processResponse.status);
          results.errors.push(`${organization.name}: HTTP ${processResponse.status}`);
        }

        // Add delay between organizations to prevent overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay

      } catch (error) {
        console.error(`Error processing organization ${organization.id}:`, error);
        results.errors.push(`${organization.name}: ${error}`);
      }
    }

    console.log('🏁 Auto-process completed:', results);

    return NextResponse.json({
      success: true,
      message: `Auto-processed ${results.organizationsProcessed} organizations`,
      data: results
    });

  } catch (error) {
    console.error('Error in auto-process:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Get auto-process status for all organizations
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Get summary of all organizations with pending followers
    const organizationStats = await prisma.organization.findMany({
      select: {
        id: true,
        name: true,
        _count: {
          select: {
            InstagramFollower: {
              where: {
                status: 'pending',
                automationEnabled: true
              }
            }
          }
        },
        InstagramSettings: {
          select: {
            instagramToken: true
          }
        }
      },
      where: {
        InstagramFollower: {
          some: {
            status: 'pending',
            automationEnabled: true
          }
        }
      }
    });

    const summary = organizationStats.map(org => ({
      organizationId: org.id,
      organizationName: org.name,
      pendingFollowers: org._count.InstagramFollower,
      hasInstagramToken: !!org.InstagramSettings?.instagramToken,
      canProcess: !!org.InstagramSettings?.instagramToken
    }));

    const totalPending = summary.reduce((sum, org) => sum + org.pendingFollowers, 0);
    const readyToProcess = summary.filter(org => org.canProcess).length;

    return NextResponse.json({
      success: true,
      data: {
        totalOrganizations: summary.length,
        totalPendingFollowers: totalPending,
        organizationsReadyToProcess: readyToProcess,
        organizations: summary
      }
    });

  } catch (error) {
    console.error('Error getting auto-process status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
