import { NextRequest, NextResponse } from 'next/server';
import { redirect } from 'next/navigation';
import { prisma } from '@workspace/database/client';
import { getSession } from '@workspace/auth/server-context';

// Instagram OAuth configuration
const INSTAGRAM_CLIENT_ID = '28269695839341181';
const INSTAGRAM_CLIENT_SECRET = process.env.INSTAGRAM_CLIENT_SECRET;
const INSTAGRAM_REDIRECT_URI = 'https://app.aisetter.pl/api/instagram/oauth/callback';

export async function GET(req: NextRequest) {
  try {
    // Get the authorization code from the URL
    const url = new URL(req.url);
    const code = url.searchParams.get('code');
    const error = url.searchParams.get('error');
    const state = url.searchParams.get('state'); // Organization slug should be in state

    // Check for OAuth errors
    if (error) {
      console.error('Instagram OAuth error:', error);
      return redirect(`/organizations/${state}/settings/instagram?error=oauth_error`);
    }

    if (!code) {
      console.error('No authorization code received');
      return redirect(`/organizations/${state}/settings/instagram?error=no_code`);
    }

    if (!INSTAGRAM_CLIENT_SECRET) {
      console.error('Instagram client secret not configured');
      return redirect(`/organizations/${state}/settings/instagram?error=server_config`);
    }

    // Get the current user session
    const session = await getSession();
    if (!session?.user?.id) {
      console.error('No user session found');
      return redirect(`/auth/sign-in?callbackUrl=/organizations/${state}/settings/instagram`);
    }

    // Exchange authorization code for access token
    const tokenResponse = await fetch('https://api.instagram.com/oauth/access_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: INSTAGRAM_CLIENT_ID,
        client_secret: INSTAGRAM_CLIENT_SECRET,
        grant_type: 'authorization_code',
        redirect_uri: INSTAGRAM_REDIRECT_URI,
        code,
      }),
    });

    const tokenData = await tokenResponse.json();

    if (!tokenResponse.ok) {
      console.error('Failed to exchange code for token:', tokenData);
      return redirect(`/organizations/${state}/settings/instagram?error=token_exchange_failed`);
    }

    // Get long-lived access token
    const longLivedTokenResponse = await fetch(
      `https://graph.instagram.com/access_token?grant_type=ig_exchange_token&client_secret=${INSTAGRAM_CLIENT_SECRET}&access_token=${tokenData.access_token}`,
      { method: 'GET' }
    );

    const longLivedTokenData = await longLivedTokenResponse.json();

    if (!longLivedTokenResponse.ok) {
      console.error('Failed to get long-lived token:', longLivedTokenData);
      // Use short-lived token if long-lived fails
      console.log('Using short-lived token as fallback');
    }

    const finalAccessToken = longLivedTokenData.access_token || tokenData.access_token;
    const expiresIn = longLivedTokenData.expires_in || tokenData.expires_in;

    // Get Instagram business account info
    const accountInfoResponse = await fetch(
      `https://graph.instagram.com/me?fields=id,username,name,profile_picture_url,followers_count,media_count,account_type&access_token=${finalAccessToken}`,
      { method: 'GET' }
    );

    const accountInfo = await accountInfoResponse.json();

    if (!accountInfoResponse.ok) {
      console.error('Failed to get account info:', accountInfo);
      return redirect(`/organizations/${state}/settings/instagram?error=account_info_failed`);
    }

    // Find or get the organization
    const organization = await prisma.organization.findFirst({
      where: {
        slug: state,
        memberships: {
          some: {
            userId: session.user.id,
            role: { in: ['OWNER', 'ADMIN'] }
          }
        }
      }
    });

    if (!organization) {
      console.error('Organization not found or user not authorized');
      return redirect(`/organizations/${state}/settings/instagram?error=unauthorized`);
    }

    // Calculate token expiration
    const tokenExpiresAt = expiresIn 
      ? new Date(Date.now() + (expiresIn * 1000))
      : new Date(Date.now() + (60 * 24 * 60 * 60 * 1000)); // 60 days default

    // Update or create Instagram settings
    await prisma.instagramSettings.upsert({
      where: {
        organizationId: organization.id
      },
      update: {
        instagramToken: finalAccessToken,
        instagramAccountId: accountInfo.id,
        instagramUsername: accountInfo.username,
        instagramName: accountInfo.name,
        instagramProfilePicture: accountInfo.profile_picture_url,
        tokenExpiresAt,
        isConnected: true,
        updatedAt: new Date()
      },
      create: {
        organizationId: organization.id,
        instagramToken: finalAccessToken,
        instagramAccountId: accountInfo.id,
        instagramUsername: accountInfo.username,
        instagramName: accountInfo.name,
        instagramProfilePicture: accountInfo.profile_picture_url,
        tokenExpiresAt,
        isConnected: true,
        isBotEnabled: false, // Default to disabled until user enables
        minResponseTime: 30,
        maxResponseTime: 50,
        messageDelayMin: 3,
        messageDelayMax: 5,
        autoCleanupEnabled: true,
        followUpCleanupDays: 30
      }
    });

    console.log(`Successfully connected Instagram account ${accountInfo.username} for organization ${organization.slug}`);

    // Redirect back to settings with success message
    return redirect(`/organizations/${state}/settings/instagram?success=connected`);

  } catch (error) {
    console.error('Instagram OAuth callback error:', error);
    return redirect(`/organizations/${state || 'unknown'}/settings/instagram?error=callback_failed`);
  }
}