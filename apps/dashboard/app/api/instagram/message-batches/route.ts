import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

const CreateMessageBatchSchema = z.object({
  name: z.string().min(1).max(255),
  messages: z.array(z.object({
    messageText: z.string().min(1),
    sequenceNumber: z.number().int().min(1).max(3),
    delayMinutes: z.number().int().min(0).max(60).default(5)
  })).min(1).max(3)
});

/**
 * Create a new message batch
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = CreateMessageBatchSchema.parse(body);

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      },
      include: {
        organization: true
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    // Create message batch with items
    const messageBatch = await prisma.messageBatch.create({
      data: {
        name: validatedData.name,
        userId: session.user.id,
        organizationId: membership.organizationId,
        MessageBatchItem: {
          create: validatedData.messages.map(msg => ({
            messageText: msg.messageText,
            sequenceNumber: msg.sequenceNumber,
            delayMinutes: msg.delayMinutes
          }))
        }
      },
      include: {
        MessageBatchItem: {
          orderBy: { sequenceNumber: 'asc' }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: messageBatch
    });

  } catch (error) {
    console.error('Error creating message batch:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: 'Invalid data format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Get message batches for organization
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const messageBatches = await prisma.messageBatch.findMany({
      where: {
        organizationId: membership.organizationId
      },
      include: {
        MessageBatchItem: {
          orderBy: { sequenceNumber: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({
      success: true,
      data: messageBatches
    });

  } catch (error) {
    console.error('Error fetching message batches:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Delete a message batch
 */
export async function DELETE(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const batchId = searchParams.get('id');

    if (!batchId) {
      return NextResponse.json(
        { success: false, error: 'Batch ID is required' },
        { status: 400 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    // Verify the batch belongs to the user's organization
    const batch = await prisma.messageBatch.findFirst({
      where: {
        id: batchId,
        organizationId: membership.organizationId
      }
    });

    if (!batch) {
      return NextResponse.json(
        { success: false, error: 'Message batch not found' },
        { status: 404 }
      );
    }

    // Delete the batch (cascade will delete items)
    await prisma.messageBatch.delete({
      where: {
        id: batchId
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Message batch deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting message batch:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
