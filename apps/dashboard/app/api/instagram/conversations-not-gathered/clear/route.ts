import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

/**
 * Clear all Instagram conversations not gathered for the organization
 */
export async function DELETE(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Delete all conversations not gathered for this organization
    const deleteResult = await prisma.instagramConversationsNotGathered.deleteMany({
      where: {
        organizationId
      }
    });

    console.log(`Cleared ${deleteResult.count} conversations not gathered for organization ${organizationId}`);

    return NextResponse.json({
      success: true,
      message: `Successfully cleared ${deleteResult.count} conversations`,
      deletedCount: deleteResult.count
    });

  } catch (error) {
    console.error('Error clearing conversations not gathered:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
