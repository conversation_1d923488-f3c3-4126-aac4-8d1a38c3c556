import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag } from 'next/cache';
import { prisma } from '@workspace/database/client';
import axios from 'axios';
import { sendMessage, getConversationHistory } from '~/lib/instagram-client';
import { Caching, OrganizationCacheKey } from '~/data/caching';
import { normalizeTimestamp } from '~/lib/formatters';
import { logger, createTimer } from '@workspace/common/logger';

/**
 * Get Instagram conversations for an organization
 */
export async function GET(req: NextRequest): Promise<Response> {
  const timer = createTimer('Instagram Conversations API - GET');

  try {
    // Get query parameters
    const organizationId = req.nextUrl.searchParams.get('organizationId');
    const contactId = req.nextUrl.searchParams.get('contactId');
    const fetchFromApi = req.nextUrl.searchParams.get('fetchFromApi') === 'true';
    const syncToDatabase = req.nextUrl.searchParams.get('syncToDatabase') === 'true';
    const testUsername = req.nextUrl.searchParams.get('testUsername'); // For testing with specific username

    logger.instagramConversation('Starting conversation retrieval', {
      operation: 'GET /api/instagram/conversations',
      organizationId: organizationId || 'missing',
      contactId: contactId || 'all',
      fetchFromApi,
      syncToDatabase
    });

    if (!organizationId) {
      logger.warn('Missing organizationId parameter in conversations API');
      return NextResponse.json({ error: 'Missing organizationId parameter' }, { status: 400 });
    }

    // Get Instagram settings for the organization
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: {
        organizationId,
        instagramToken: { not: null }
      }
    });

    if (!instagramSettings) {
      return NextResponse.json({ error: 'Instagram settings not found' }, { status: 404 });
    }

    // If testUsername is provided, find or create contact and get conversation
    if (testUsername) {
      logger.instagramConversation('Testing conversation retrieval with username', {
        operation: 'testUsername',
        organizationId,
        username: testUsername
      });

      // Find existing contact by username
      let contact = await prisma.instagramContact.findFirst({
        where: {
          organizationId,
          instagramNickname: testUsername
        }
      });

      if (!contact) {
        // Create a test contact if it doesn't exist
        contact = await prisma.instagramContact.create({
          data: {
            userId: 'test-user-id', // This should be replaced with actual user ID in production
            organizationId,
            instagramNickname: testUsername,
            stage: 'initial',
            priority: 3,
            conversationSource: 'api'
          }
        });

        logger.instagramConversation('Created test contact', {
          operation: 'testUsername',
          organizationId,
          username: testUsername,
          contactId: contact.id
        });
      }

      // Get messages for the contact from our database
      const messages = await prisma.instagramMessage.findMany({
        where: {
          contactId: contact.id
        },
        orderBy: {
          timestamp: 'asc'
        }
      });

      const response = NextResponse.json({
        contact,
        messages,
        testMode: true,
        message: `Test conversation retrieval for username: ${testUsername}`
      });

      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      response.headers.set('Pragma', 'no-cache');
      response.headers.set('Expires', '0');

      return response;
    }

    // If contactId is provided, get messages for that specific contact
    if (contactId) {
      const contact = await prisma.instagramContact.findUnique({
        where: {
          id: contactId,
          organizationId
        }
      });

      if (!contact) {
        return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
      }

      // Get messages for the contact from our database
      let messages = await prisma.instagramMessage.findMany({
        where: {
          contactId
        },
        orderBy: {
          timestamp: 'asc'
        }
      });

      if (fetchFromApi && instagramSettings.instagramToken) {
        const apiTimer = createTimer('Instagram API - Fetch Conversation History');

        try {
          logger.instagramConversation('Starting Instagram API fetch', {
            operation: 'fetchFromApi',
            organizationId,
            contactId,
            instagramId: contact.instagramId ?? undefined,
            username: contact.instagramNickname
          });

          // Fetch conversation history from Instagram API
          const accessToken = instagramSettings.instagramToken;

          // Check if contact has Instagram ID
          if (!contact.instagramId) {
            logger.warn('Contact missing Instagram ID, skipping API fetch', {
              operation: 'fetchFromApi',
              organizationId,
              contactId,
              username: contact.instagramNickname
            });
          } else {
            try {
              // Use the getConversationHistory function from instagram-client.ts
              const conversationHistory = await getConversationHistory(
                contact.instagramId,
                accessToken
              );

              const messageCount = conversationHistory.data[0]?.messages?.data?.length || 0;

              logger.instagramConversation('Successfully retrieved conversation from Instagram API', {
                operation: 'fetchFromApi',
                organizationId,
                contactId,
                instagramId: contact.instagramId ?? undefined,
                username: contact.instagramNickname,
                messageCount,
                apiCall: true
              });

              if (conversationHistory &&
                conversationHistory.data &&
                conversationHistory.data[0] &&
                conversationHistory.data[0].messages &&
                conversationHistory.data[0].messages.data) {

                const apiMessages = conversationHistory.data[0].messages.data;

                // Sort messages by created_time
                apiMessages.sort((a: any, b: any) => {
                  const timeA = a.created_time ? parseInt(a.created_time) : 0;
                  const timeB = b.created_time ? parseInt(b.created_time) : 0;
                  return timeA - timeB;
                });

                if (syncToDatabase) {
                  const syncTimer = createTimer('Database Sync - Instagram Messages');
                  let syncedCount = 0;
                  let skippedCount = 0;
                  let errorCount = 0;

                  logger.instagramConversation('Starting database sync', {
                    operation: 'syncToDatabase',
                    organizationId,
                    contactId,
                    instagramId: contact.instagramId ?? undefined,
                    totalMessages: apiMessages.length,
                    syncToDatabase: true
                  });

                  // Process each message and save to database if it doesn't exist
                  for (const apiMessage of apiMessages) {
                    try {
                      // Check if message already exists in our database
                      const existingMessage = await prisma.instagramMessage.findFirst({
                        where: {
                          messageId: apiMessage.id,
                          contactId: contact.id
                        }
                      });

                      if (!existingMessage) {
                        // Determine if message is from user or not
                        const isFromUser = apiMessage.from?.id === contact.instagramId;

                        // Save message to database
                        // Parse timestamp correctly - Instagram API returns Unix timestamp as string (seconds)
                        const timestamp = normalizeTimestamp(apiMessage.created_time);

                        logger.instagramConversation('Processing API message for database sync', {
                          operation: 'syncMessage',
                          organizationId,
                          contactId,
                          messageId: apiMessage.id,
                          created_time: apiMessage.created_time,
                          parsed_timestamp: timestamp.toISOString(),
                          content_preview: (apiMessage.message || '').substring(0, 50) + '...',
                          isFromUser,
                          syncToDatabase: true
                        });

                        await prisma.instagramMessage.create({
                          data: {
                            contactId: contact.id,
                            messageId: apiMessage.id,
                            content: apiMessage.message || '',
                            isFromUser,
                            isFromExtension: false, // lub true jeśli wiadomość z wtyczki
                            timestamp
                          }
                        });

                        syncedCount++;
                        logger.debug('Successfully saved message to database', {
                          operation: 'syncMessage',
                          messageId: apiMessage.id,
                          contactId
                        });
                      } else {
                        skippedCount++;
                      }
                    } catch (error) {
                      errorCount++;
                      logger.error('Error syncing message to database', {
                        operation: 'syncMessage',
                        organizationId,
                        contactId,
                        messageId: apiMessage.id
                      }, error as Error);
                    }
                  }

                  logger.instagramConversation('Database sync completed', {
                    operation: 'syncToDatabase',
                    organizationId,
                    contactId,
                    instagramId: contact.instagramId ?? undefined,
                    totalMessages: apiMessages.length,
                    syncedCount,
                    skippedCount,
                    errorCount,
                    syncToDatabase: true
                  });

                  syncTimer.end({
                    organizationId,
                    contactId,
                    syncedCount,
                    skippedCount,
                    errorCount
                  });

                  // Refresh messages from database
                  messages = await prisma.instagramMessage.findMany({
                    where: {
                      contactId
                    },
                    orderBy: {
                      timestamp: 'asc'
                    }
                  });
                }

                const response = NextResponse.json({
                  contact,
                  messages,
                  apiMessages
                });

                // Add cache control headers to prevent caching
                response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
                response.headers.set('Pragma', 'no-cache');
                response.headers.set('Expires', '0');

                return response;
              }
            } catch (error) {
              console.error('Error fetching conversation history:', error);
            }
          }
        } catch (error) {
          console.error('Error fetching conversation history from Instagram API:', error);
          // Continue with local messages only
        }
      }

      const response = NextResponse.json({
        contact,
        messages
      });

      // Add cache control headers to prevent caching
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
      response.headers.set('Pragma', 'no-cache');
      response.headers.set('Expires', '0');

      return response;
    }

    // Otherwise, get all contacts for the organization
    const contacts = await prisma.instagramContact.findMany({
      where: {
        organizationId
      },
      orderBy: {
        updatedAt: 'desc'
      },
      include: {
        InstagramConversation: {
          orderBy: {
            sentAt: 'desc'
          },
          take: 1
        }
      }
    });

    const response = NextResponse.json({ contacts });

    // Add cache control headers to prevent caching
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;
  } catch (error) {
    console.error('Error getting Instagram conversations:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Fetch conversations from Instagram API
 * This is a helper function to fetch conversations from the Instagram API
 */
async function fetchInstagramConversations(accessToken: string, instagramId: string) {
  try {
    // Get conversations
    const response = await axios.get(`https://graph.instagram.com/v22.0/${instagramId}/conversations`, {
      params: {
        access_token: accessToken,
        platform: 'instagram'
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching Instagram conversations:', error);
    throw error;
  }
}

/**
 * Fetch messages for a conversation from Instagram API
 */
async function fetchInstagramMessages(accessToken: string, conversationId: string) {
  try {
    // Get messages for the conversation
    const response = await axios.get(`https://graph.instagram.com/v22.0/${conversationId}`, {
      params: {
        access_token: accessToken,
        fields: 'messages'
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching Instagram messages:', error);
    throw error;
  }
}

/**
 * Fetch message details from Instagram API
 */
async function fetchMessageDetails(accessToken: string, messageId: string) {
  try {
    // Get message details
    const response = await axios.get(`https://graph.instagram.com/v22.0/${messageId}`, {
      params: {
        access_token: accessToken,
        fields: 'id,created_time,from,to,message'
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching message details:', error);
    throw error;
  }
}

/**
 * Fetch user profile from Instagram API
 */
async function fetchUserProfile(accessToken: string, instagramScopedId: string) {
  try {
    // Get user profile
    const response = await axios.get(`https://graph.instagram.com/v22.0/${instagramScopedId}`, {
      params: {
        access_token: accessToken,
        fields: 'name,username,profile_pic,follower_count,is_user_follow_business,is_business_follow_user'
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw error;
  }
}

/**
 * Send a message to an Instagram user
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const body = await req.json();
    const { organizationId, contactId, message } = body;

    if (!organizationId || !contactId || !message) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    // Get Instagram settings for the organization
    const instagramSettings = await prisma.instagramSettings.findFirst({
      where: {
        organizationId,
        instagramToken: { not: null }
      }
    });

    if (!instagramSettings) {
      return NextResponse.json({ error: 'Instagram settings not found' }, { status: 404 });
    }

    // Get the contact
    const contact = await prisma.instagramContact.findUnique({
      where: {
        id: contactId,
        organizationId
      }
    });

    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }

    // Check if Instagram token exists
    if (!instagramSettings?.instagramToken) {
      return NextResponse.json({ error: 'Instagram token not found' }, { status: 400 });
    }

    try {
      // Check if contact has Instagram ID
      if (!contact.instagramId) {
        return NextResponse.json({ error: 'Contact Instagram ID not available' }, { status: 400 });
      }

      // Send the message to Instagram API
      const response = await sendMessage({
        recipientId: contact.instagramId,
        message,
        accessToken: instagramSettings.instagramToken
      });

      console.log(`Message sent to Instagram user ${contact.instagramId}:`, response);
    } catch (error) {
      console.error('Error sending message to Instagram API:', error);
      return NextResponse.json({ error: 'Failed to send message to Instagram API' }, { status: 500 });
    }

    // Save the message to our database
    const messageId = `manual_${Date.now()}`;
    const newMessage = await prisma.instagramMessage.create({
      data: {
        contactId,
        messageId,
        content: message,
        isFromUser: false,
        timestamp: new Date()
      }
    });

    // Update message count only (do not update lastInteractionAt for manual bot messages)
    await prisma.instagramContact.update({
      where: { id: contactId },
      data: {
        messageCount: { increment: 1 }
      }
    });

    // Invalidate Instagram contacts cache to reflect updated message count
    try {
      const cacheTag = Caching.createOrganizationTag(
        OrganizationCacheKey.InstagramContacts,
        organizationId
      );
      revalidateTag(cacheTag);
      console.log(`Invalidated cache tag after sending message: ${cacheTag}`);
    } catch (cacheError) {
      console.error('Error invalidating cache after sending message:', cacheError);
    }

    return NextResponse.json({ success: true, message: newMessage });
  } catch (error) {
    console.error('Error sending Instagram message:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
