import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { triggerFollowerProcessing, triggerFollowerProcessingByNicknames } from '~/lib/instagram-follower-trigger';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const ImmediateTriggerSchema = z.object({
  action: z.enum(['single_follower', 'multiple_followers']),
  data: z.union([
    z.object({
      followerId: z.string().uuid(),
      instagramNickname: z.string().optional()
    }),
    z.object({
      nicknames: z.array(z.string().min(1)).min(1).max(10),
      organizationId: z.string().uuid()
    })
  ])
});

/**
 * Immediate trigger endpoint for real-time follower processing
 * NEW FOLLOWER = IMMEDIATE TRIGGER
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const body = await req.json();
    const validatedData = ImmediateTriggerSchema.parse(body);

    console.log('🚀 Immediate trigger received:', validatedData);

    if (validatedData.action === 'single_follower') {
      // Process single follower immediately
      const data = validatedData.data as { followerId: string; instagramNickname?: string };
      
      console.log(`⚡ Processing single follower immediately: ${data.followerId}`);
      
      const result = await triggerFollowerProcessing(data.followerId);
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          message: 'Follower processed immediately',
          data: result.data
        }, {
          headers: getCorsHeaders()
        });
      } else {
        return NextResponse.json({
          success: false,
          error: result.error
        }, {
          status: 400,
          headers: getCorsHeaders()
        });
      }

    } else if (validatedData.action === 'multiple_followers') {
      // Process multiple followers by nicknames
      const data = validatedData.data as { nicknames: string[]; organizationId: string };
      
      console.log(`⚡ Processing ${data.nicknames.length} followers immediately:`, data.nicknames);
      
      const result = await triggerFollowerProcessingByNicknames(data.nicknames, data.organizationId);
      
      return NextResponse.json({
        success: true,
        message: `Processed ${result.processed} followers immediately`,
        data: result
      }, {
        headers: getCorsHeaders()
      });
    }

    return NextResponse.json({
      success: false,
      error: 'Invalid action'
    }, {
      status: 400,
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error in immediate trigger:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, {
      status: 500,
      headers: getCorsHeaders()
    });
  }
}

/**
 * Get immediate trigger status
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action');

    if (action === 'health') {
      // Health check for immediate trigger system
      return NextResponse.json({
        success: true,
        message: 'Immediate trigger system is healthy',
        data: {
          status: 'active',
          endpoint: '/api/instagram/trigger-immediate',
          supportedActions: ['single_follower', 'multiple_followers'],
          timestamp: new Date().toISOString()
        }
      }, {
        headers: getCorsHeaders()
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Immediate trigger endpoint ready',
      data: {
        usage: {
          single_follower: {
            method: 'POST',
            body: {
              action: 'single_follower',
              data: {
                followerId: 'uuid',
                instagramNickname: 'optional'
              }
            }
          },
          multiple_followers: {
            method: 'POST',
            body: {
              action: 'multiple_followers',
              data: {
                nicknames: ['username1', 'username2'],
                organizationId: 'uuid'
              }
            }
          }
        }
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error getting immediate trigger status:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, {
      status: 500,
      headers: getCorsHeaders()
    });
  }
}
