import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

/**
 * Get a random message from message batches for Chrome Extension
 * Used for priority 3 (new) contacts with empty conversations
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    // Get all message batches for the organization
    const messageBatches = await prisma.messageBatch.findMany({
      where: {
        organizationId: membership.organizationId
      },
      include: {
        MessageBatchItem: {
          where: {
            sequenceNumber: 1 // Only get first messages from each batch
          }
        }
      }
    });

    if (messageBatches.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No message batches found' },
        { status: 404 }
      );
    }

    // Filter batches that have at least one message
    const batchesWithMessages = messageBatches.filter(
      batch => batch.MessageBatchItem.length > 0
    );

    if (batchesWithMessages.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No message batches with messages found' },
        { status: 404 }
      );
    }

    // Select a random batch
    const randomIndex = Math.floor(Math.random() * batchesWithMessages.length);
    const selectedBatch = batchesWithMessages[randomIndex];
    const firstMessage = selectedBatch.MessageBatchItem[0];

    return NextResponse.json({
      success: true,
      data: {
        batchId: selectedBatch.id,
        batchName: selectedBatch.name,
        message: {
          id: firstMessage.id,
          text: firstMessage.messageText,
          sequenceNumber: firstMessage.sequenceNumber,
          delayMinutes: firstMessage.delayMinutes
        }
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error fetching random message:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}