import { NextRequest, NextResponse } from 'next/server';
import { verifyApiKey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
  };
}

export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new Response(null, { status: 200, headers: getCorsHeaders() });
}

/**
 * Get contact status for Chrome Extension
 * Used to check if contact is disqualified before sending messages
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { contactId: string } }
): Promise<Response> {
  try {
    // Check for API key (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API key required' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Verify API key
    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;
    const { contactId } = params;

    // Find the contact
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: contactId,
        organizationId
      },
      select: {
        id: true,
        instagramNickname: true,
        stage: true,
        attackListStatus: true,
        priority: true,
        nextMessageAt: true
      }
    });

    if (!contact) {
      return NextResponse.json(
        { success: false, error: 'Contact not found' },
        { status: 404, headers: getCorsHeaders() }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: contact.id,
        instagramNickname: contact.instagramNickname,
        stage: contact.stage,
        attackListStatus: contact.attackListStatus,
        priority: contact.priority,
        nextMessageAt: contact.nextMessageAt
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error getting contact status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
} 