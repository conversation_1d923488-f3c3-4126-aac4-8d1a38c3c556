import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { verifyApi<PERSON>ey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const markSentSchema = z.object({
  contactId: z.string(),
  messageType: z.enum(['batch', 'batch_sequence', 'followup']),
  followUpId: z.string().optional(),
  sequenceNumber: z.number().int().min(1).optional(),
  batchCompleted: z.boolean().optional() // Indicates if entire batch sequence is completed
});

/**
 * Mark message as sent and schedule follow-ups
 * Used by Chrome extension when a message is successfully sent
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check for API key (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API key required' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    // Verify API key
    const result = await verifyApiKey(apiKey);
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.errorMessage },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const organizationId = result.organizationId;
    const body = await req.json();
    const validatedData = markSentSchema.parse(body);

    // Find the contact
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: validatedData.contactId,
        organizationId
      }
    });

    if (!contact) {
      return NextResponse.json(
        { success: false, error: 'Contact not found' },
        { status: 404, headers: getCorsHeaders() }
      );
    }

    // Check if contact is disqualified - prevent sending messages to disqualified contacts
    if (contact.stage === 'disqualified') {
      return NextResponse.json(
        { success: false, error: 'Cannot send messages to disqualified contact' },
        { status: 400, headers: getCorsHeaders() }
      );
    }

    const sentTime = new Date();
    const updateData: any = {
      lastInteractionAt: sentTime, // Set interaction time when message is actually sent
      lastMessageSentAt: sentTime,
      updatedAt: sentTime
    };

    if (validatedData.messageType === 'followup' && validatedData.followUpId) {
      // Mark specific follow-up as sent
      await prisma.instagramFollowUp.update({
        where: { id: validatedData.followUpId },
        data: {
          status: 'sent',
          sentAt: sentTime
        }
      });

      updateData.currentMessageSequence = validatedData.sequenceNumber || 1;

    } else if (validatedData.messageType === 'batch' || validatedData.messageType === 'batch_sequence') {
      // Handle batch message(s) sent
      updateData.batchMessageStatus = 'sent';
      updateData.currentMessageSequence = validatedData.sequenceNumber || 1;

      // Only create follow-ups and change priority when ENTIRE batch sequence is completed
      if (validatedData.batchCompleted) {
        updateData.priority = 1; // Change priority to 1 for follow-ups

      // Check if follow-ups already exist (from batch processing)
      const existingFollowUps = await prisma.instagramFollowUp.findMany({
        where: {
          contactId: contact.id,
          status: { in: ['pending', 'external'] }
        }
      });

      // Only create follow-ups if none exist (avoid duplicates)
      if (existingFollowUps.length === 0) {
        // Get follow-up templates for this organization
        const followUpTemplates = await prisma.followUpTemplate.findMany({
          where: {
            organizationId
            // Remove isActive filter - use all templates
          },
          orderBy: [
            { sequenceNumber: 'asc' },
            { variationNumber: 'asc' }
          ]
        });

        console.log(`Found ${followUpTemplates.length} follow-up templates for organization ${organizationId}`);

        // Group templates by sequence number
        const templatesBySequence = followUpTemplates.reduce((acc, template) => {
          if (!acc[template.sequenceNumber]) {
            acc[template.sequenceNumber] = [];
          }
          acc[template.sequenceNumber].push(template);
          return acc;
        }, {} as Record<number, typeof followUpTemplates>);

        // Create follow-ups for each sequence
        for (let sequenceNumber = 1; sequenceNumber <= 3; sequenceNumber++) {
          const sequenceTemplates = templatesBySequence[sequenceNumber];
          if (sequenceTemplates && sequenceTemplates.length > 0) {
            // Pick a random variation for this sequence
            const randomTemplate = sequenceTemplates[Math.floor(Math.random() * sequenceTemplates.length)];

            // Calculate scheduled time based on template delay
            const scheduledTime = new Date(sentTime.getTime() + (randomTemplate.delayHours * 60 * 60 * 1000));

            await prisma.instagramFollowUp.create({
              data: {
                contactId: contact.id,
                message: randomTemplate.messageText,
                scheduledTime: scheduledTime,
                status: 'external', // External so it appears in attack list
                sequenceNumber: sequenceNumber + 1 // Follow-up sequence starts from 2
              }
            });
          }
        }
        } else {
          console.log(`Follow-ups already exist for contact ${contact.id}, skipping creation`);
        }
      } else {
        console.log(`Batch sequence not completed yet for contact ${contact.id}, follow-ups will be created when batch is finished`);
      }
    }

    // Zapisz wiadomość wysłaną przez wtyczkę
    await prisma.instagramMessage.create({
      data: {
        contactId: contact.id,
        messageId: `${contact.id}-${Date.now()}`,
        content: validatedData.messageType === 'followup' ? 'AI follow-up sent' : 'Batch message sent',
        isFromUser: false,
        isFromExtension: true,
        timestamp: sentTime
      }
    });

    // Update next message time based on pending follow-ups
    const nextFollowUp = await prisma.instagramFollowUp.findFirst({
      where: {
        contactId: contact.id,
        status: { in: ['pending', 'external'] }
      },
      orderBy: { scheduledTime: 'asc' }
    });

    if (nextFollowUp) {
      updateData.nextMessageAt = nextFollowUp.scheduledTime;
      updateData.attackListStatus = 'pending';
    } else {
      updateData.attackListStatus = 'completed';
      updateData.nextMessageAt = null;
    }

    // Update the contact
    const updatedContact = await prisma.instagramContact.update({
      where: {
        id: validatedData.contactId
      },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      data: {
        contactId: updatedContact.id,
        attackListStatus: updatedContact.attackListStatus,
        nextMessageAt: updatedContact.nextMessageAt,
        priority: updatedContact.priority,
        lastInteractionAt: updatedContact.lastInteractionAt
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error marking message as sent:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
