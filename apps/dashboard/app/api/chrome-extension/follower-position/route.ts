import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const UpdatePositionSchema = z.object({
  position: z.number().int().min(0),
  totalFollowers: z.number().int().min(0).optional(),
  isCompleted: z.boolean().optional()
});

/**
 * Update follower processing position for Chrome Extension
 * Tracks where the extension stopped processing followers
 */
export async function PUT(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = UpdatePositionSchema.parse(body);

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Update or create processing state
    const processingState = await prisma.instagramFollowerProcessingState.upsert({
      where: {
        organizationId
      },
      update: {
        lastProcessedPosition: validatedData.position,
        totalFollowersCount: validatedData.totalFollowers,
        isCompleted: validatedData.isCompleted || false,
        lastProcessedAt: new Date(),
        updatedAt: new Date()
      },
      create: {
        organizationId,
        lastProcessedPosition: validatedData.position,
        totalFollowersCount: validatedData.totalFollowers,
        isCompleted: validatedData.isCompleted || false,
        lastProcessedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        position: processingState.lastProcessedPosition,
        totalFollowers: processingState.totalFollowersCount,
        isCompleted: processingState.isCompleted,
        lastProcessedAt: processingState.lastProcessedAt
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error updating follower position:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}

/**
 * Get current follower processing position
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Get processing state
    const processingState = await prisma.instagramFollowerProcessingState.findUnique({
      where: {
        organizationId
      }
    });

    if (!processingState) {
      // Return default state if none exists
      return NextResponse.json({
        success: true,
        data: {
          position: 0,
          totalFollowers: null,
          isCompleted: false,
          lastProcessedAt: null
        }
      }, {
        headers: getCorsHeaders()
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        position: processingState.lastProcessedPosition,
        totalFollowers: processingState.totalFollowersCount,
        isCompleted: processingState.isCompleted,
        lastProcessedAt: processingState.lastProcessedAt
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error getting follower position:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}

/**
 * Reset follower processing position (start from beginning)
 */
export async function DELETE(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404 }
      );
    }

    const organizationId = membership.organizationId;

    // Reset processing state
    await prisma.instagramFollowerProcessingState.upsert({
      where: {
        organizationId
      },
      update: {
        lastProcessedPosition: 0,
        isCompleted: false,
        lastProcessedAt: new Date(),
        updatedAt: new Date()
      },
      create: {
        organizationId,
        lastProcessedPosition: 0,
        isCompleted: false,
        lastProcessedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Follower processing position reset to beginning'
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error resetting follower position:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
