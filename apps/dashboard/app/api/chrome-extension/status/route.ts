import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

// Enhanced status validation - supports dynamic milestone statuses
const UpdateStatusSchema = z.object({
  extensionStatus: z.string().optional().superRefine((status, ctx) => {
    const baseStatuses = ['FRESH_START', 'ACTIVE', 'IDLE', 'STOPPED', 'ALL_SCRAPED', 'CONVERSATIONS_GATHERING', 'CONVERSATIONS_GATHERING_PAUSED', 'CONVERSATIONS_GATHERING_COMPLETED', 'CONVERSATIONS_GATHERED_READY'];
    const isValid =
      baseStatuses.includes(status!) ||
      /^SCRAPED_\d+$/.test(status!);
  
    if (!isValid) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: `Invalid extension status: "${status}"`,
      });
    }
  }),
  currentActivity: z.string().optional(),
  isConnected: z.boolean().optional(),
  // Enhanced tracking fields
  totalFollowersScraped: z.number().int().min(0).optional(),
  lastScrapedPosition: z.number().int().min(0).optional(),
  lastScrapedUsernames: z.array(z.string()).max(10).optional(),
  scrapingTargetReached: z.boolean().optional(),
  allFollowersScraped: z.boolean().optional(),
});

/**
 * Update Chrome Extension Status
 * Used by Chrome extension to update its current status and activity
 */
export async function PUT(req: NextRequest): Promise<Response> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401, headers: getCorsHeaders() }
      );
    }

    const body = await req.json();
    console.log('🔍 Received extension status update:', {
      body: JSON.stringify(body),
      extensionStatus: body.extensionStatus,
      extensionStatusType: typeof body.extensionStatus,
      hasExtensionStatus: 'extensionStatus' in body
    });
    
    const validatedData = UpdateStatusSchema.parse(body);

    // Get user's organization
    const membership = await prisma.membership.findFirst({
      where: {
        userId: session.user.id
      }
    });

    if (!membership) {
      return NextResponse.json(
        { success: false, error: 'No organization found' },
        { status: 404, headers: getCorsHeaders() }
      );
    }

    // Convert legacy status for backward compatibility
    let normalizedStatus = validatedData.extensionStatus;
    if (!normalizedStatus) {
      return NextResponse.json(
        { success: false, error: 'Extension status is required' },
        { status: 400, headers: getCorsHeaders() }
      );
    }
    
    if (normalizedStatus === 'SCRAPED_250_FOLLOWERS') {
      normalizedStatus = 'SCRAPED_250';
    }

    // Update Chrome Extension settings with new status and enhanced tracking
    const settings = await prisma.chromeExtensionSettings.upsert({
      where: {
        organizationId: membership.organizationId
      },
      update: {
        extensionStatus: normalizedStatus,
        currentActivity: validatedData.currentActivity,
        lastActivityAt: new Date(),
        ...(validatedData.isConnected !== undefined && {
          isConnected: validatedData.isConnected,
          lastConnectionAt: validatedData.isConnected ? new Date() : undefined,
        }),
        // Enhanced tracking fields
        ...(validatedData.totalFollowersScraped !== undefined && {
          totalFollowersScraped: validatedData.totalFollowersScraped,
        }),
        ...(validatedData.lastScrapedPosition !== undefined && {
          lastScrapedPosition: validatedData.lastScrapedPosition,
        }),
        ...(validatedData.lastScrapedUsernames !== undefined && {
          lastScrapedUsernames: validatedData.lastScrapedUsernames,
        }),
        ...(validatedData.scrapingTargetReached !== undefined && {
          scrapingTargetReached: validatedData.scrapingTargetReached,
        }),
        ...(validatedData.allFollowersScraped !== undefined && {
          allFollowersScraped: validatedData.allFollowersScraped,
        }),
        ...(normalizedStatus.startsWith('SCRAPED_') && {
          lastScrapingSession: new Date(),
        }),
        updatedAt: new Date(),
      },
      create: {
        organizationId: membership.organizationId,
        extensionStatus: normalizedStatus,
        currentActivity: validatedData.currentActivity,
        lastActivityAt: new Date(),
        isConnected: validatedData.isConnected ?? true,
        lastConnectionAt: validatedData.isConnected !== false ? new Date() : undefined,
        // Enhanced tracking fields with defaults
        totalFollowersScraped: validatedData.totalFollowersScraped ?? 0,
        lastScrapedPosition: validatedData.lastScrapedPosition ?? 0,
        lastScrapedUsernames: validatedData.lastScrapedUsernames ?? [],
        scrapingTargetReached: validatedData.scrapingTargetReached ?? false,
        allFollowersScraped: validatedData.allFollowersScraped ?? false,
        lastScrapingSession: normalizedStatus.startsWith('SCRAPED_') ? new Date() : undefined,
        // Default settings
        timeBetweenDMsMin: 3,
        timeBetweenDMsMax: 8,
        messagesBeforeBreakMin: 8,
        messagesBeforeBreakMax: 15,
        breakDurationMin: 10,
        breakDurationMax: 20,
        pauseStart: "00:30",
        pauseStop: "07:00",
        smartFocus: true,
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        extensionStatus: settings.extensionStatus,
        currentActivity: settings.currentActivity,
        lastActivityAt: settings.lastActivityAt,
        isConnected: settings.isConnected,
        lastConnectionAt: settings.lastConnectionAt,
        // Enhanced tracking data
        totalFollowersScraped: settings.totalFollowersScraped,
        lastScrapedPosition: settings.lastScrapedPosition,
        lastScrapedUsernames: settings.lastScrapedUsernames,
        scrapingTargetReached: settings.scrapingTargetReached,
        allFollowersScraped: settings.allFollowersScraped,
        lastScrapingSession: settings.lastScrapingSession,
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error updating extension status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}

/**
 * Get Chrome Extension Status
 * Used by Chrome extension to check current status
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const { verifyApiKey } = await import('@workspace/api-keys');
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }

      organizationId = membership.organizationId;
    }

    // Get Chrome Extension settings
    const settings = await prisma.chromeExtensionSettings.findUnique({
      where: {
        organizationId
      }
    });

    if (!settings) {
      return NextResponse.json({
        success: true,
        data: {
          extensionStatus: 'FRESH_START',
          currentActivity: null,
          lastActivityAt: null,
          isConnected: false,
          lastConnectionAt: null,
          // Enhanced tracking defaults
          totalFollowersScraped: 0,
          lastScrapedPosition: 0,
          lastScrapedUsernames: [],
          scrapingTargetReached: false,
          allFollowersScraped: false,
          lastScrapingSession: null,
          // 5-day interval scraping eligibility defaults
          isScrapingEligible: true, // Fresh start - always eligible
          nextScrapingAllowedAt: null,
          waitTimeMs: 0,
          waitTimeHuman: '',
          scrapingIntervalDays: 5,
        }
      }, {
        headers: getCorsHeaders()
      });
    }

    // Calculate scraping eligibility
    const now = new Date();
    let isScrapingEligible = true;
    let nextScrapingAllowedAt: Date | null = settings.nextScrapingAllowedAt;
    let waitTimeMs = 0;
    let waitTimeHuman = '';

    if (settings.nextScrapingAllowedAt && settings.nextScrapingAllowedAt > now) {
      isScrapingEligible = false;
      waitTimeMs = settings.nextScrapingAllowedAt.getTime() - now.getTime();

      // Convert to human readable format
      const days = Math.floor(waitTimeMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor((waitTimeMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((waitTimeMs % (1000 * 60 * 60)) / (1000 * 60));

      if (days > 0) {
        waitTimeHuman = `${days} day${days > 1 ? 's' : ''}, ${hours} hour${hours > 1 ? 's' : ''}`;
      } else if (hours > 0) {
        waitTimeHuman = `${hours} hour${hours > 1 ? 's' : ''}, ${minutes} minute${minutes > 1 ? 's' : ''}`;
      } else {
        waitTimeHuman = `${minutes} minute${minutes > 1 ? 's' : ''}`;
      }
    }

    // Special case: if all followers are scraped, always allow (for testing/manual override)
    if (settings.allFollowersScraped) {
      isScrapingEligible = true;
      nextScrapingAllowedAt = null;
      waitTimeMs = 0;
      waitTimeHuman = '';
    }

    return NextResponse.json({
      success: true,
      data: {
        extensionStatus: settings.extensionStatus,
        currentActivity: settings.currentActivity,
        lastActivityAt: settings.lastActivityAt,
        isConnected: settings.isConnected,
        lastConnectionAt: settings.lastConnectionAt,
        // Enhanced tracking data
        totalFollowersScraped: settings.totalFollowersScraped,
        lastScrapedPosition: settings.lastScrapedPosition,
        lastScrapedUsernames: settings.lastScrapedUsernames,
        scrapingTargetReached: settings.scrapingTargetReached,
        allFollowersScraped: settings.allFollowersScraped,
        lastScrapingSession: settings.lastScrapingSession,
        // 5-day interval scraping eligibility
        isScrapingEligible,
        nextScrapingAllowedAt,
        waitTimeMs,
        waitTimeHuman,
        scrapingIntervalDays: settings.scrapingIntervalDays,
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error getting extension status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
