import { NextRequest, NextResponse } from 'next/server';

import { auth } from '@workspace/auth';
import { verifyApiKey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(_req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

/**
 * Get scraping status for Chrome Extension
 * This is the endpoint the Chrome extension expects
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }

      organizationId = membership.organizationId;
    }

    // Get Chrome Extension settings
    const settings = await prisma.chromeExtensionSettings.findUnique({
      where: {
        organizationId
      }
    });

    const now = new Date();
    let isScrapingEligible = true;
    let nextScrapingAllowedAt: Date | null = null;
    let waitTimeMs = 0;
    let waitTimeHuman = '';

    if (settings) {
      // Check if we need to wait based on interval
      if (settings.nextScrapingAllowedAt && settings.nextScrapingAllowedAt > now) {
        isScrapingEligible = false;
        nextScrapingAllowedAt = settings.nextScrapingAllowedAt;
        waitTimeMs = settings.nextScrapingAllowedAt.getTime() - now.getTime();
        
        // Convert to human readable format
        const days = Math.floor(waitTimeMs / (1000 * 60 * 60 * 24));
        const hours = Math.floor((waitTimeMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((waitTimeMs % (1000 * 60 * 60)) / (1000 * 60));
        
        if (days > 0) {
          waitTimeHuman = `${days} day${days > 1 ? 's' : ''}, ${hours} hour${hours > 1 ? 's' : ''}`;
        } else if (hours > 0) {
          waitTimeHuman = `${hours} hour${hours > 1 ? 's' : ''}, ${minutes} minute${minutes > 1 ? 's' : ''}`;
        } else {
          waitTimeHuman = `${minutes} minute${minutes > 1 ? 's' : ''}`;
        }
      }

      // Special case: if all followers are scraped, always allow (for testing/manual override)
      if (settings.allFollowersScraped) {
        isScrapingEligible = true;
        nextScrapingAllowedAt = null;
        waitTimeMs = 0;
        waitTimeHuman = '';
      }
    }

    // Return comprehensive scraping status for Chrome extension
    return NextResponse.json({
      success: true,
      data: {
        // Scraping eligibility
        isScrapingEligible,
        nextScrapingAllowedAt,
        waitTimeMs,
        waitTimeHuman,
        
        // Extension status
        extensionStatus: settings?.extensionStatus || 'FRESH_START',
        currentActivity: settings?.currentActivity,
        lastActivityAt: settings?.lastActivityAt,
        isConnected: settings?.isConnected || false,
        lastConnectionAt: settings?.lastConnectionAt,
        
        // Scraping statistics
        totalFollowersScraped: settings?.totalFollowersScraped || 0,
        lastScrapedPosition: settings?.lastScrapedPosition || 0,
        lastScrapedUsernames: settings?.lastScrapedUsernames || [],
        scrapingTargetReached: settings?.scrapingTargetReached || false,
        allFollowersScraped: settings?.allFollowersScraped || false,
        lastScrapingSession: settings?.lastScrapingSession,
        
        // Configuration
        scrapingIntervalDays: settings?.scrapingIntervalDays || 5,
        
        // Chrome extension settings
        timeBetweenDMsMin: settings?.timeBetweenDMsMin || 3,
        timeBetweenDMsMax: settings?.timeBetweenDMsMax || 8,
        messagesBeforeBreakMin: settings?.messagesBeforeBreakMin || 8,
        messagesBeforeBreakMax: settings?.messagesBeforeBreakMax || 15,
        breakDurationMin: settings?.breakDurationMin || 10,
        breakDurationMax: settings?.breakDurationMax || 20,
        pauseStart: settings?.pauseStart || "00:30",
        pauseStop: settings?.pauseStop || "07:00",
        smartFocus: settings?.smartFocus ?? true,
        
        // Status message for UI
        statusMessage: isScrapingEligible 
          ? "✅ Ready to scrape followers" 
          : `⏳ Must wait ${waitTimeHuman} before next scraping session`,
        
        // Action recommendations
        canScrape: isScrapingEligible,
        shouldWait: !isScrapingEligible,
        recommendedAction: isScrapingEligible 
          ? "You can start scraping up to 250 followers" 
          : `Wait ${waitTimeHuman} before starting next scraping session`
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error getting scraping status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
