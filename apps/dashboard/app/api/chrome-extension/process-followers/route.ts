import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { auth } from '@workspace/auth';
import { verifyApiKey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';
import { followerProcessingQueue } from '~/lib/follower-processing-queue';

/**
 * Enhanced status determination logic
 * Generates dynamic milestone statuses based on total scraped count
 */
function determineExtensionStatus(
  totalScraped: number,
  newFollowersCount: number,
  isComplete: boolean,
  reachedBottom: boolean,
): string {
  if (reachedBottom || isComplete) {
    return "ALL_SCRAPED";
  }

  const newTotal = totalScraped + newFollowersCount;

  // Generate milestone status (every 250 followers)
  const milestone = Math.floor(newTotal / 250) * 250;
  if (milestone > 0 && newTotal >= milestone) {
    return `SCRAPED_${milestone}`;
  }

  return "ACTIVE";
}

// CORS headers for Chrome Extension
function getCorsHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key, ngrok-skip-browser-warning',
    'Access-Control-Max-Age': '86400',
  };
}

// Handle preflight OPTIONS requests
export async function OPTIONS(_req: NextRequest): Promise<Response> {
  return new NextResponse(null, {
    status: 200,
    headers: getCorsHeaders(),
  });
}

const ProcessFollowersSchema = z.object({
  followers: z.array(z.object({
    instagramNickname: z.string().min(1),
    instagramId: z.string().optional(),
    avatar: z.string().url().optional(),
    followerCount: z.number().int().min(0).optional(),
    isVerified: z.boolean().default(false)
  })).min(1).max(500),
  startPosition: z.number().int().min(0),
  totalFollowers: z.number().int().min(0).optional(),
  isComplete: z.boolean().optional(),
  // Enhanced tracking fields
  lastScrapedUsernames: z.array(z.string()).max(10).optional(),
});

/**
 * Process followers batch from Chrome Extension
 * Simply adds followers to InstagramFollower table
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;
    let userId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;

      // For API key auth, we need to get a user ID from the organization
      const membership = await prisma.membership.findFirst({
        where: {
          organizationId: result.organizationId,
          isOwner: true // Use the owner as the default user for API operations
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization owner found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }

      userId = membership.userId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }

      organizationId = membership.organizationId;
      userId = session.user.id;
    }



    const body = await req.json();
    console.log('🚨 DEBUG: Received request body:', JSON.stringify(body, null, 2));
    
    try {
      ProcessFollowersSchema.parse(body);
      console.log('🚨 DEBUG: Schema validation successful');
    } catch (validationError: unknown) {
      console.error('🚨 DEBUG: Schema validation failed:', validationError);
      const errorMessage = validationError instanceof Error ? validationError.message : 'Unknown validation error';
      return NextResponse.json(
        { success: false, error: `Validation error: ${errorMessage}` },
        { status: 400, headers: getCorsHeaders() }
      );
    }
    
    const validatedData = ProcessFollowersSchema.parse(body);

    // Process followers - simply add them to InstagramFollower table
    const results = {
      processed: 0,
      newFollowers: 0,
      existingFollowers: 0,
      errors: [] as string[]
    };

    for (const follower of validatedData.followers) {
      try {
        results.processed++;

        // Check if follower already exists in InstagramFollower table
        const existingFollower = await prisma.instagramFollower.findFirst({
          where: {
            organizationId,
            instagramNickname: follower.instagramNickname
          }
        });

        if (existingFollower) {
          results.existingFollowers++;
          continue;
        }

        // Add follower to InstagramFollower table
        const newFollower = await prisma.instagramFollower.create({
          data: {
            organizationId,
            userId: userId,
            instagramId: follower.instagramId,
            instagramNickname: follower.instagramNickname,
            avatar: follower.avatar,
            followerCount: follower.followerCount,
            isVerified: follower.isVerified,
            batchNumber: Math.floor(validatedData.startPosition / 500) + 1,
            priority: 'normal',
            status: 'pending',
            isTargeted: false,
            automationEnabled: true
          }
        });

        results.newFollowers++;

        // 🚀 ADD TO QUEUE: Initially add without conversation check (will be updated after gathering)
        try {
          console.log(`🚀 Adding ${follower.instagramNickname} to processing queue...`);

          // Add to FollowerProcessingQueue - hasConversation will be updated after gathering is complete
          await prisma.followerProcessingQueue.create({
            data: {
              organizationId,
              followerId: newFollower.id,
              priority: 2, // Default priority - will be updated after conversation gathering
              scheduledAt: new Date(), // Process when queue runs
              status: 'pending',
              hasConversation: false, // Will be updated after conversation gathering
              processingType: 'batch' // Will be updated after conversation gathering
            }
          });

          console.log(`✅ Added ${follower.instagramNickname} to processing queue (conversation check will happen after gathering)`);
        } catch (queueError) {
          console.error(`💥 Error adding ${follower.instagramNickname} to queue:`, queueError);
          // Don't fail the main upload if queue fails
        }

      } catch (error) {
        console.error(`Error processing follower ${follower.instagramNickname}:`, error);
        results.errors.push(`Failed to process ${follower.instagramNickname}: ${error}`);
      }
    }

    // Step 2: Update processing position
    await prisma.instagramFollowerProcessingState.upsert({
      where: {
        organizationId
      },
      update: {
        lastProcessedPosition: validatedData.startPosition + validatedData.followers.length,
        totalFollowersCount: validatedData.totalFollowers,
        lastProcessedAt: new Date(),
        updatedAt: new Date()
      },
      create: {
        organizationId,
        lastProcessedPosition: validatedData.startPosition + validatedData.followers.length,
        totalFollowersCount: validatedData.totalFollowers,
        lastProcessedAt: new Date()
      }
    });

    // Step 3: Enhanced status tracking - update ChromeExtensionSettings with new status
    const currentSettings = await prisma.chromeExtensionSettings.findUnique({
      where: { organizationId }
    });

    const currentTotalScraped = currentSettings?.totalFollowersScraped || 0;
    const newTotalScraped = currentTotalScraped + results.newFollowers;
    const isComplete = validatedData.isComplete || false;

    // Determine new status based on enhanced logic
    const newStatus = determineExtensionStatus(
      currentTotalScraped,
      results.newFollowers,
      isComplete,
      isComplete
    );

    // Prepare last scraped usernames (keep last 10)
    const newUsernames = validatedData.followers.map(f => f.instagramNickname);
    const existingUsernames = validatedData.lastScrapedUsernames || currentSettings?.lastScrapedUsernames || [];
    const combinedUsernames = [...existingUsernames, ...newUsernames];
    const lastScrapedUsernames = combinedUsernames.slice(-10); // Keep only last 10

    // 🚨 SET NEXT SCRAPING ALLOWED TIME: When 250 followers are reached, set 5-day wait
    const scrapingIntervalDays = currentSettings?.scrapingIntervalDays || 5;
    let nextScrapingAllowedAt: Date | null = null;

    // Check if we just completed a batch of 250 followers
    // Logic: if we uploaded 250+ followers in this batch, trigger the wait
    const reachedMilestone = results.newFollowers >= 250;

    if (reachedMilestone || isComplete) {
      // Set next allowed scraping time to 5 days from now
      nextScrapingAllowedAt = new Date();
      nextScrapingAllowedAt.setDate(nextScrapingAllowedAt.getDate() + scrapingIntervalDays);
      console.log(`🚨 Batch of 250 completed! Next scraping allowed at: ${nextScrapingAllowedAt.toISOString()}`);
    }

    // Update ChromeExtensionSettings with enhanced tracking
    await prisma.chromeExtensionSettings.upsert({
      where: { organizationId },
      update: {
        extensionStatus: newStatus,
        totalFollowersScraped: newTotalScraped,
        lastScrapedPosition: validatedData.startPosition + validatedData.followers.length,
        lastScrapedUsernames: lastScrapedUsernames,
        scrapingTargetReached: newTotalScraped >= 250 && (newTotalScraped % 250 === 0),
        allFollowersScraped: isComplete,
        lastScrapingSession: new Date(),
        nextScrapingAllowedAt: nextScrapingAllowedAt, // Set 5-day wait when batch completed
        currentActivity: isComplete ?
          `All ${newTotalScraped} followers processed` :
          `Processed ${results.newFollowers} followers (total: ${newTotalScraped})`,
        lastActivityAt: new Date(),
        updatedAt: new Date(),
      },
      create: {
        organizationId,
        extensionStatus: newStatus,
        totalFollowersScraped: newTotalScraped,
        lastScrapedPosition: validatedData.startPosition + validatedData.followers.length,
        lastScrapedUsernames: lastScrapedUsernames,
        scrapingTargetReached: newTotalScraped >= 250 && (newTotalScraped % 250 === 0),
        allFollowersScraped: isComplete,
        lastScrapingSession: new Date(),
        nextScrapingAllowedAt: nextScrapingAllowedAt, // Set 5-day wait when batch completed
        currentActivity: isComplete ?
          `All ${newTotalScraped} followers processed` :
          `Processed ${results.newFollowers} followers (total: ${newTotalScraped})`,
        lastActivityAt: new Date(),
        // Default settings
        timeBetweenDMsMin: 3,
        timeBetweenDMsMax: 8,
        messagesBeforeBreakMin: 8,
        messagesBeforeBreakMax: 15,
        breakDurationMin: 10,
        breakDurationMax: 20,
        pauseStart: "00:30",
        pauseStop: "07:00",
        smartFocus: true,
        isConnected: true,
        lastConnectionAt: new Date(),
      }
    });

    console.log(`✅ Enhanced status tracking: ${newStatus} (${newTotalScraped} total followers)`);
    console.log(`✅ Queued ${results.newFollowers} new followers for processing`);

    // 🚀 CONVERSATION GATHERING & QUEUE PROCESSING: Handle conversation gathering first, then queue
    if (results.newFollowers > 0) {
      try {
        console.log(`🚀 Processing ${results.newFollowers} new followers...`);

        // Check if conversation gathering has been done for this organization
        const existingConversations = await prisma.instagramConversationsNotGathered.count({
          where: { organizationId }
        });

        // If no conversations exist, this is the FIRST TIME - run conversation gathering
        let conversationGatheringInProgress = false;
        if (existingConversations === 0) {
          conversationGatheringInProgress = true;
          console.log(`🚀 FIRST FOLLOWER BATCH - Running conversation gathering for organization: ${organizationId}`);

          // Get Instagram settings for conversation gathering
          const instagramSettings = await prisma.instagramSettings.findFirst({
            where: {
              organizationId,
              instagramToken: { not: null }
            }
          });

          if (instagramSettings?.instagramToken) {
            try {
              console.log(`📞 Fetching conversations from Instagram API...`);
              
              // Get the Instagram business account ID
              const businessAccountResponse = await fetch(
                `https://graph.instagram.com/v22.0/me?fields=id&access_token=${instagramSettings.instagramToken}`
              );
              
              if (!businessAccountResponse.ok) {
                throw new Error('Failed to get business account info');
              }
              
              const businessAccount = await businessAccountResponse.json();
              
              // Fetch conversations from Instagram API
              const conversationsResponse = await fetch(
                `https://graph.instagram.com/v22.0/${businessAccount.id}/conversations?platform=instagram&access_token=${instagramSettings.instagramToken}`
              );
              
              if (!conversationsResponse.ok) {
                throw new Error('Failed to fetch conversations');
              }
              
              const conversationsData = await conversationsResponse.json();
              const instagramApiConversations = conversationsData.data || [];

              console.log(`📝 Found ${instagramApiConversations.length} conversations from Instagram API`);

              // Populate InstagramConversationsNotGathered table
              let conversationsAdded = 0;
              for (const conversation of instagramApiConversations) {
                const participants = conversation.participants || [];
                if (participants.length >= 2) {
                  // Skip the first participant (business account) and take the second one
                  const participant = participants[1];
                  if (participant.username) {
                    try {
                      await prisma.instagramConversationsNotGathered.create({
                        data: {
                          organizationId,
                          instagramConversationId: conversation.id,
                          participantUsername: participant.username,
                          participantId: participant.id,
                          updatedTime: new Date(conversation.updated_time),
                          isGathered: false
                        }
                      });
                      conversationsAdded++;
                    } catch (error) {
                      // Ignore duplicates
                      const errorMessage = error instanceof Error ? error.message : String(error);
                      if (!errorMessage.includes('unique constraint')) {
                        console.error(`Error adding conversation for ${participant.username}:`, error);
                      }
                    }
                  }
                }
              }

              console.log(`✅ Conversation gathering completed. Added ${conversationsAdded} conversations.`);
              conversationGatheringInProgress = false; // Mark as complete
              
              // 🔄 UPDATE QUEUE ITEMS: Now that conversations are gathered, update hasConversation flags
              console.log(`🔄 Updating queue items with conversation status...`);
              
              try {
                const allQueueItems = await prisma.followerProcessingQueue.findMany({
                  where: {
                    organizationId,
                    status: 'pending'
                  },
                  include: {
                    InstagramFollower: true
                  }
                });

                let updatedWithConversations = 0;
                for (const queueItem of allQueueItems) {
                  if (queueItem.InstagramFollower?.instagramNickname) {
                    const hasConversation = await prisma.instagramConversationsNotGathered.findFirst({
                      where: {
                        organizationId,
                        participantUsername: queueItem.InstagramFollower.instagramNickname
                      }
                    });

                    if (hasConversation) {
                      await prisma.followerProcessingQueue.update({
                        where: { id: queueItem.id },
                        data: {
                          hasConversation: true,
                          priority: 1, // Higher priority for conversation processing
                          processingType: 'conversation'
                        }
                      });
                      updatedWithConversations++;
                      console.log(`✅ Updated ${queueItem.InstagramFollower.instagramNickname} - has conversation (priority 1)`);
                    }
                  }
                }
                
                console.log(`✅ Updated ${updatedWithConversations} queue items with conversation status`);
              } catch (error) {
                console.error('❌ Error updating queue items:', error);
              }
              
              // 🚀 NOW TRIGGER QUEUE PROCESSING: Conversation gathering is complete
              console.log(`🚀 Conversation gathering complete - triggering queue processing for followers...`);
              
              try {
                followerProcessingQueue.processQueue().catch(error => {
                  console.error('❌ Error triggering queue processing:', error);
                });
                console.log(`✅ Queue processing triggered after conversation gathering completion`);
              } catch (error) {
                console.error('❌ Error triggering post-gathering queue processing:', error);
              }
            } catch (error) {
              console.error(`❌ Error during conversation gathering:`, error);
              conversationGatheringInProgress = false; // Mark as complete even if failed
              // Continue with processing even if gathering fails
            }
          } else {
            console.log(`⚠️ No Instagram token found for organization ${organizationId} - skipping conversation gathering`);
            conversationGatheringInProgress = false; // Mark as complete
          }
        } else {
          console.log(`✅ Conversation gathering already done. Found ${existingConversations} existing conversations.`);
          
          // 🔄 UPDATE QUEUE ITEMS: Conversations already exist, update hasConversation flags for new followers
          console.log(`🔄 Updating new queue items with existing conversation status...`);
          
          try {
            const newQueueItems = await prisma.followerProcessingQueue.findMany({
              where: {
                organizationId,
                status: 'pending',
                hasConversation: false // Only check items that haven't been checked yet
              },
              include: {
                InstagramFollower: true
              }
            });

            let updatedWithConversations = 0;
            for (const queueItem of newQueueItems) {
              if (queueItem.InstagramFollower?.instagramNickname) {
                const hasConversation = await prisma.instagramConversationsNotGathered.findFirst({
                  where: {
                    organizationId,
                    participantUsername: queueItem.InstagramFollower.instagramNickname
                  }
                });

                if (hasConversation) {
                  await prisma.followerProcessingQueue.update({
                    where: { id: queueItem.id },
                    data: {
                      hasConversation: true,
                      priority: 1, // Higher priority for conversation processing
                      processingType: 'conversation'
                    }
                  });
                  updatedWithConversations++;
                  console.log(`✅ Updated ${queueItem.InstagramFollower.instagramNickname} - has conversation (priority 1)`);
                }
              }
            }
            
            console.log(`✅ Updated ${updatedWithConversations} new queue items with existing conversation status`);
          } catch (error) {
            console.error('❌ Error updating new queue items:', error);
          }
        }

        // 🚨 WAIT FOR CONVERSATION GATHERING: Only trigger queue if conversation gathering is complete
        if (!conversationGatheringInProgress) {
          console.log(`✅ Conversation gathering complete - triggering queue processing for ${results.newFollowers} new followers...`);

          // Trigger queue processing in the background (don't wait for it)
          followerProcessingQueue.processQueue().catch(error => {
            console.error('❌ Error triggering queue processing:', error);
          });

          console.log(`✅ Queue processing triggered successfully`);
        } else {
          console.log(`⏳ Conversation gathering still in progress - queue processing will be triggered later`);
        }
      } catch (error) {
        console.error('❌ Error in conversation gathering and queue processing:', error);
        // Don't fail the upload if processing fails
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully uploaded ${results.processed} followers. Added ${results.newFollowers} new followers to processing queue, ${results.existingFollowers} already existed.`,
      data: results
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error processing followers:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}

/**
 * Get processing status
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for API key first (Chrome extension)
    const apiKey = req.headers.get('X-API-Key');
    let organizationId: string;

    if (apiKey) {
      // API key authentication (Chrome extension)
      const result = await verifyApiKey(apiKey);
      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.errorMessage },
          { status: 401, headers: getCorsHeaders() }
        );
      }
      organizationId = result.organizationId;
    } else {
      // Session authentication (web dashboard)
      const session = await auth();
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401, headers: getCorsHeaders() }
        );
      }

      // Get user's organization
      const membership = await prisma.membership.findFirst({
        where: {
          userId: session.user.id
        }
      });

      if (!membership) {
        return NextResponse.json(
          { success: false, error: 'No organization found' },
          { status: 404, headers: getCorsHeaders() }
        );
      }

      organizationId = membership.organizationId;
    }

    // Get processing state
    const processingState = await prisma.instagramFollowerProcessingState.findUnique({
      where: {
        organizationId
      }
    });

    // Get counts
    const totalFollowers = await prisma.instagramFollower.count({
      where: { organizationId }
    });

    const pendingFollowers = await prisma.instagramFollower.count({
      where: {
        organizationId,
        status: 'pending'
      }
    });

    const contactedFollowers = await prisma.instagramFollower.count({
      where: {
        organizationId,
        status: 'contacted'
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        processingState: processingState || {
          position: 0,
          totalFollowers: null,
          isCompleted: false,
          lastProcessedAt: null
        },
        stats: {
          totalFollowers,
          pendingFollowers,
          contactedFollowers
        }
      }
    }, {
      headers: getCorsHeaders()
    });

  } catch (error) {
    console.error('Error getting processing status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500, headers: getCorsHeaders() }
    );
  }
}
