import { NextRequest, NextResponse } from 'next/server';
import { followerProcessingQueue } from '~/lib/follower-processing-queue';

/**
 * Cron job to process follower queue
 * This endpoint should be called by a cron job every 2-5 minutes
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check for authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader || authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🚀 Cron job: Processing follower queue...');

    // Process the queue
    await followerProcessingQueue.processQueue();

    // Get stats for logging
    const stats = await followerProcessingQueue.getStats();
    
    console.log('✅ Cron job completed. Queue stats:', stats);

    return NextResponse.json({ 
      success: true, 
      stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error in follower queue cron job:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
