import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { prisma } from '@workspace/database/client';
import { autoTriggerPendingFollowers } from '~/lib/instagram-follower-trigger';

const WebhookSchema = z.object({
  organizationId: z.string().uuid(),
  action: z.enum(['followers_added', 'process_followers']),
  data: z.object({
    count: z.number().int().min(0).optional(),
    batchNumber: z.number().int().min(1).optional()
  }).optional()
});

/**
 * Webhook endpoint for Instagram follower events
 * Can be called by Chrome extension or external systems
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Verify webhook signature if needed
    const webhookSecret = process.env.INSTAGRAM_WEBHOOK_SECRET;
    if (webhookSecret) {
      const signature = req.headers.get('x-webhook-signature');
      if (!signature) {
        return NextResponse.json(
          { success: false, error: 'Missing webhook signature' },
          { status: 401 }
        );
      }
      // TODO: Verify signature
    }

    const body = await req.json();
    const validatedData = WebhookSchema.parse(body);

    console.log('📨 Instagram followers webhook received:', validatedData);

    switch (validatedData.action) {
      case 'followers_added':
        return await handleFollowersAdded(validatedData.organizationId, validatedData.data);

      case 'process_followers':
        return await handleProcessFollowers(validatedData.organizationId);

      default:
        return NextResponse.json(
          { success: false, error: 'Unknown action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error processing Instagram followers webhook:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function handleFollowersAdded(organizationId: string, data?: any): Promise<Response> {
  try {
    // Check if organization exists and has Instagram settings
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: {
        InstagramSettings: true
      }
    });

    if (!organization) {
      return NextResponse.json(
        { success: false, error: 'Organization not found' },
        { status: 404 }
      );
    }

    if (!organization.InstagramSettings?.instagramToken) {
      return NextResponse.json(
        { success: false, error: 'Instagram not configured for this organization' },
        { status: 400 }
      );
    }

    // Get count of pending followers
    const pendingCount = await prisma.instagramFollower.count({
      where: {
        organizationId,
        status: 'pending',
        automationEnabled: true
      }
    });

    console.log(`📊 Organization ${organizationId} has ${pendingCount} pending followers`);

    // Trigger processing immediately for any new followers
    const BATCH_THRESHOLD = 1; // Process immediately when we have 1+ pending followers

    if (pendingCount >= BATCH_THRESHOLD) {
      console.log(`🚀 Triggering auto-processing for organization ${organizationId}`);

      // Call the auto-trigger function directly (more reliable than fetch)
      const processResult = await autoTriggerPendingFollowers(organizationId, Math.min(pendingCount, 50));

      if (processResult.success) {
        console.log(`✅ Auto-processing completed for ${organizationId}:`, processResult);

        return NextResponse.json({
          success: true,
          message: 'Followers added and auto-processing triggered',
          data: {
            pendingFollowers: pendingCount,
            processed: 'processed' in processResult ? processResult.processed : 0,
            successes: 'successes' in processResult ? processResult.successes : [],
            errors: 'errors' in processResult ? processResult.errors : []
          }
        });
      } else {
        console.error(`❌ Auto-processing failed for ${organizationId}:`, processResult.error);
        return NextResponse.json({
          success: true,
          message: 'Followers added but auto-processing failed',
          data: {
            pendingFollowers: pendingCount,
            error: processResult.error
          }
        });
      }
    } else {
      return NextResponse.json({
        success: true,
        message: 'Followers added, waiting for more before processing',
        data: {
          pendingFollowers: pendingCount,
          threshold: BATCH_THRESHOLD
        }
      });
    }

  } catch (error) {
    console.error('Error handling followers_added webhook:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to handle followers_added event' },
      { status: 500 }
    );
  }
}

async function handleProcessFollowers(organizationId: string): Promise<Response> {
  try {
    console.log(`🔄 Manual processing triggered for organization ${organizationId}`);

    // Call the auto-trigger function directly (more reliable than fetch)
    const processResult = await autoTriggerPendingFollowers(organizationId, 50);

    if (processResult.success) {
      console.log(`✅ Manual processing completed for ${organizationId}:`, processResult);

      return NextResponse.json({
        success: true,
        message: 'Manual processing completed',
        data: processResult
      });
    } else {
      console.error(`❌ Manual processing failed for ${organizationId}:`, processResult.error);
      return NextResponse.json(
        { success: false, error: processResult.error || 'Processing failed' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error handling process_followers webhook:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to handle process_followers event' },
      { status: 500 }
    );
  }
}

/**
 * Get webhook status and configuration
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json(
        { success: false, error: 'organizationId parameter required' },
        { status: 400 }
      );
    }

    // Get organization stats
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: {
        InstagramSettings: {
          select: {
            instagramToken: true
          }
        },
        _count: {
          select: {
            InstagramFollower: {
              where: {
                status: 'pending',
                automationEnabled: true
              }
            }
          }
        }
      }
    });

    if (!organization) {
      return NextResponse.json(
        { success: false, error: 'Organization not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        organizationId,
        organizationName: organization.name,
        hasInstagramToken: !!organization.InstagramSettings?.instagramToken,
        pendingFollowers: organization._count.InstagramFollower,
        webhookUrl: `${process.env.NEXTAUTH_URL}/api/webhooks/instagram-followers`,
        canProcess: !!organization.InstagramSettings?.instagramToken && organization._count.InstagramFollower > 0
      }
    });

  } catch (error) {
    console.error('Error getting webhook status:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
