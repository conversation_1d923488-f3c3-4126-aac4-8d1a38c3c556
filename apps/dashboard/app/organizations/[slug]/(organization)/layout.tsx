import * as React from 'react';
import type { Metadata } from 'next';
import { cookies } from 'next/headers';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { getAuthOrganizationContextForClient } from '@workspace/auth/server-context';
import { SidebarInset } from '@workspace/ui/components/sidebar';

import { SidebarRenderer } from '~/components/organizations/slug/sidebar-renderer';
import { getProfile } from '~/data/account/get-profile';

import { getOrganizations } from '~/data/organization/get-organizations';
import { createTitle } from '~/lib/formatters';
import { Providers as OrgProviders } from './providers';
import { Providers } from '~/app/providers';

export const generateMetadata = async (): Promise<Metadata> => {
  const ctx = await getAuthOrganizationContext();
  return {
    title: createTitle('Organization'),
    other: {
      'user-email': ctx.session.user.email || ''
    }
  };
};

export default async function OrganizationLayout(
  props: NextPageProps & React.PropsWithChildren
): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();
  const authContext = await getAuthOrganizationContextForClient();

  const [cookieStore, organizations, profile] = await Promise.all([
    cookies(),
    getOrganizations(),
    getProfile()
  ]);

  return (
    <Providers authContext={authContext}>
      <div className="flex flex-col size-full overflow-hidden">
        <OrgProviders
          organization={ctx.organization}
          membership={ctx.membership}
          userEmail={ctx.session.user.email || ''}
          defaultOpen={
            (cookieStore.get('sidebar:state')?.value ?? 'true') === 'true'
          }
          defaultWidth={cookieStore.get('sidebar:width')?.value}
        >
          <SidebarRenderer
            organizations={organizations}
            profile={profile}
          />
          {/* Set max-width so full-width tables can overflow horizontally correctly */}
          <SidebarInset
            id="skip"
            className="size-full lg:[transition:max-width_0.2s_linear] lg:peer-data-[state=collapsed]:max-w-[calc(100svw-var(--sidebar-width-icon))] lg:peer-data-[state=expanded]:max-w-[calc(100svw-var(--sidebar-width))]"
          >
            {props.children}
          </SidebarInset>
        </OrgProviders>
      </div>
    </Providers>
  );
}
