import { type Metadata } from 'next';

import { getAuthOrganizationContext } from '@workspace/auth/context';

import { Page, PageBody, PageHeader, PagePrimaryBar } from '@workspace/ui/components/page';
import { TransitionProvider } from '~/hooks/use-transition-context';

import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { ApiKeySection } from '~/components/organizations/slug/instagram/settings/api-key-section';

export const metadata: Metadata = {
  title: 'API Key'
};

export default async function ApiKeyPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="API Key"
              info="Generate and manage API keys for the Chrome Extension"
            />
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6">
          <div className="space-y-6">
            <ApiKeySection />
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
