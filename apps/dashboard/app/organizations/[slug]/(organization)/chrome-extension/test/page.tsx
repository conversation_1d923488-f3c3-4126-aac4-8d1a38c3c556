import * as React from 'react';
import { type Metadata } from 'next';

import {
  <PERSON>,
  <PERSON>Body,
  PageHeader,
  PagePrimaryBar
} from '@workspace/ui/components/page';

import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { ChromeExtensionTestPanel } from '~/components/organizations/slug/chrome-extension/test/chrome-extension-test-panel';
import { createTitle } from '~/lib/formatters';

export const metadata: Metadata = {
  title: createTitle('Test Chrome Extension Flow')
};

export default function ChromeExtensionTestPage(): React.JSX.Element {
  return (
    <Page>
      <PageHeader>
        <PagePrimaryBar>
          <OrganizationPageTitle
            title="Test Chrome Extension Flow"
            info="Test the complete Instagram follower flow"
          />
        </PagePrimaryBar>
      </PageHeader>
      <PageBody className="p-6">
        <ChromeExtensionTestPanel />
      </PageBody>
    </Page>
  );
}