import { type Metadata } from 'next';

import { getAuthOrganizationContext } from '@workspace/auth/context';

import { Page, PageBody, PageHeader, PagePrimaryBar } from '@workspace/ui/components/page';
import { TransitionProvider } from '~/hooks/use-transition-context';

import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { ChromeExtensionSettingsForm } from '~/components/organizations/slug/chrome-extension/chrome-extension-settings-form';

export const metadata: Metadata = {
  title: 'Chrome Extension Settings'
};

export default async function ChromeExtensionSettingsPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="General Settings"
              info="Configure general settings for the Instagram Chrome Extension"
            />
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6">
          <div className="space-y-6">
            <ChromeExtensionSettingsForm />
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
