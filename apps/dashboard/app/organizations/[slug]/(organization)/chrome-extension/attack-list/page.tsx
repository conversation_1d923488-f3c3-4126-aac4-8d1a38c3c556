import { type Metadata } from 'next';

import { getAuthOrganizationContext } from '@workspace/auth/context';

import { Page, PageBody, PageHeader, PagePrimaryBar } from '@workspace/ui/components/page';
import { TransitionProvider } from '~/hooks/use-transition-context';

import { OrganizationPageTitle } from '~/components/organizations/slug/organization-page-title';
import { AttackListManager } from '~/components/organizations/slug/chrome-extension/attack-list-manager';

export const metadata: Metadata = {
  title: 'Attack List'
};

export default async function AttackListPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthOrganizationContext();

  return (
    <TransitionProvider>
      <Page>
        <PageHeader>
          <PagePrimaryBar>
            <OrganizationPageTitle
              title="Attack List"
              info="Manage the list of followers to target with the Chrome Extension"
            />
          </PagePrimaryBar>
        </PageHeader>
        <PageBody className="p-6">
          <div className="space-y-6">
            <AttackListManager />
          </div>
        </PageBody>
      </Page>
    </TransitionProvider>
  );
}
