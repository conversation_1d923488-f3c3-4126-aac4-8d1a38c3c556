'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@workspace/ui/components/card';
import { Button } from '@workspace/ui/components/button';
import { Badge } from '@workspace/ui/components/badge';
import { RefreshCw, Play, Square, Trash2 } from 'lucide-react';

interface QueueStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
}

interface TypeStats {
  batch: number;
  conversation: number;
}

interface QueueItem {
  id: string;
  status: string;
  processingType: string;
  hasConversation: boolean;
  priority: number;
  attempts: number;
  createdAt: string;
  updatedAt: string;
  errorMessage?: string;
  follower: {
    nickname: string;
    avatar?: string;
  };
}

export default function QueueMonitorPage() {
  const [stats, setStats] = useState<QueueStats>({ pending: 0, processing: 0, completed: 0, failed: 0 });
  const [typeStats, setTypeStats] = useState<TypeStats>({ batch: 0, conversation: 0 });
  const [recentItems, setRecentItems] = useState<QueueItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/follower-queue-stats');
      const data = await response.json();
      
      if (data.success) {
        setStats(data.data.stats);
        setTypeStats(data.data.typeStats);
        setRecentItems(data.data.recentItems);
      }
    } catch (error) {
      console.error('Error fetching queue stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const processQueue = async () => {
    setProcessing(true);
    try {
      const response = await fetch('/api/admin/process-follower-queue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'process' })
      });
      
      const data = await response.json();
      if (data.success) {
        // Refresh stats after processing
        setTimeout(fetchStats, 1000);
      }
    } catch (error) {
      console.error('Error processing queue:', error);
    } finally {
      setProcessing(false);
    }
  };

  const retryFailed = async () => {
    try {
      const response = await fetch('/api/admin/process-follower-queue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'retry' })
      });
      
      const data = await response.json();
      if (data.success) {
        fetchStats();
      }
    } catch (error) {
      console.error('Error retrying failed items:', error);
    }
  };

  const cleanup = async () => {
    try {
      const response = await fetch('/api/admin/process-follower-queue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'cleanup', days: 7 })
      });

      const data = await response.json();
      if (data.success) {
        fetchStats();
      }
    } catch (error) {
      console.error('Error cleaning up queue:', error);
    }
  };

  const resetCircuitBreaker = async () => {
    try {
      const response = await fetch('/api/admin/reset-circuit-breaker', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const data = await response.json();
      if (data.success) {
        console.log('Circuit breaker reset successfully');
        // Refresh stats after reset
        fetchStats();
      }
    } catch (error) {
      console.error('Error resetting circuit breaker:', error);
    }
  };

  useEffect(() => {
    fetchStats();
    const interval = setInterval(fetchStats, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'batch': return 'bg-purple-100 text-purple-800';
      case 'conversation': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return <div className="p-6">Loading queue stats...</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Follower Processing Queue</h1>
        <div className="flex gap-2">
          <Button onClick={fetchStats} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button 
            onClick={processQueue} 
            disabled={processing}
            size="sm"
          >
            <Play className="w-4 h-4 mr-2" />
            {processing ? 'Processing...' : 'Process Queue'}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Processing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.processing}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Failed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
          </CardContent>
        </Card>
      </div>

      {/* Type Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Pending Batch Processing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{typeStats.batch}</div>
            <p className="text-sm text-gray-500">No conversation, fast processing</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Pending AI Conversation</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{typeStats.conversation}</div>
            <p className="text-sm text-gray-500">Has conversation, 15s AI delays</p>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Queue Actions</CardTitle>
        </CardHeader>
        <CardContent className="flex gap-2">
          <Button onClick={retryFailed} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry Failed
          </Button>
          <Button onClick={cleanup} variant="outline" size="sm">
            <Trash2 className="w-4 h-4 mr-2" />
            Cleanup Old Items
          </Button>
          <Button onClick={resetCircuitBreaker} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Reset Circuit Breaker
          </Button>
        </CardContent>
      </Card>

      {/* Recent Items */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Queue Items</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {recentItems.map((item) => (
              <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                    {item.follower.avatar ? (
                      <img src={item.follower.avatar} alt="" className="w-8 h-8 rounded-full" />
                    ) : (
                      <span className="text-xs">{item.follower.nickname.charAt(0).toUpperCase()}</span>
                    )}
                  </div>
                  <div>
                    <div className="font-medium">{item.follower.nickname}</div>
                    <div className="text-sm text-gray-500">
                      Priority: {item.priority} | Attempts: {item.attempts}
                    </div>
                    {item.errorMessage && (
                      <div className="text-xs text-red-500 mt-1">{item.errorMessage}</div>
                    )}
                  </div>
                </div>
                <div className="flex gap-2">
                  <Badge className={getStatusColor(item.status)}>
                    {item.status}
                  </Badge>
                  <Badge className={getTypeColor(item.processingType)}>
                    {item.processingType}
                  </Badge>
                  {item.hasConversation && (
                    <Badge variant="outline">💬</Badge>
                  )}
                </div>
              </div>
            ))}
            {recentItems.length === 0 && (
              <div className="text-center text-gray-500 py-4">No queue items found</div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
