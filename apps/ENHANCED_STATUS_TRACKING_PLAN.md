# Enhanced Status Tracking System - Architecture Plan

## 🎯 **Problem Statement**

The current follower scraping system has several limitations:

1. **Invalid enum value**: `ALL_SCRAPED` is not in the allowed status list
2. **Limited status granularity**: Only `SCRAPED_250_FOLLOWERS` but need 500, 750, 1000, etc.
3. **Position tracking**: Need to track last ~10 followers for reliable resume points
4. **Status persistence**: Ensure status updates are properly saved

## 🏗️ **Solution Architecture**

### **1. Enhanced Status System**

#### Current Status Enum (Limited):

```typescript
"FRESH_START" | "SCRAPED_250_FOLLOWERS" | "ACTIVE" | "IDLE" | "STOPPED";
```

#### New Dynamic Status System:

```typescript
// Base statuses
'FRESH_START' | 'ACTIVE' | 'IDLE' | 'STOPPED' | 'ALL_SCRAPED'

// Dynamic scraping milestone statuses
'SCRAPED_250' | 'SCRAPED_500' | 'SCRAPED_750' | 'SCRAPED_1000' | 'SCRAPED_1250' | 'SCRAPED_1500' | ...
```

#### Status Generation Logic:

```typescript
function generateScrapingStatus(
  totalScraped: number,
  isComplete: boolean,
): string {
  if (isComplete) return "ALL_SCRAPED";
  if (totalScraped === 0) return "FRESH_START";

  // Generate milestone status (every 250 followers)
  const milestone = Math.floor(totalScraped / 250) * 250;
  if (milestone > 0 && totalScraped >= milestone) {
    return `SCRAPED_${milestone}`;
  }

  return "ACTIVE";
}
```

### **2. Enhanced Position Tracking**

#### New Database Fields (ChromeExtensionSettings):

```sql
-- Add to ChromeExtensionSettings table
totalFollowersScraped INT DEFAULT 0,
lastScrapedPosition INT DEFAULT 0,
lastScrapedUsernames TEXT[], -- Array of last 10 usernames
scrapingTargetReached BOOLEAN DEFAULT false,
allFollowersScraped BOOLEAN DEFAULT false,
lastScrapingSession TIMESTAMP,
```

#### Position Tracking Logic:

```typescript
interface ScrapingProgress {
  totalScraped: number; // Total followers scraped across all sessions
  lastPosition: number; // Position in follower list where we stopped
  lastUsernames: string[]; // Last 10 scraped usernames for resume detection
  targetReached: boolean; // Whether current 250-batch target was reached
  isComplete: boolean; // Whether we've reached the end of all followers
  sessionId: string; // Current scraping session identifier
}
```

### **3. Resume Logic Enhancement**

#### Smart Resume Detection:

```typescript
function findResumePosition(
  currentFollowers: Follower[],
  lastScrapedUsernames: string[],
): number {
  // Find the position where last scraped usernames appear
  for (let i = 0; i < currentFollowers.length - 10; i++) {
    const slice = currentFollowers.slice(i, i + 10).map((f) => f.username);
    const matches = slice.filter((username) =>
      lastScrapedUsernames.includes(username),
    );

    // If we find 7+ matches in a 10-username window, this is our resume point
    if (matches.length >= 7) {
      return i + 10; // Start after the matched usernames
    }
  }

  return 0; // Start from beginning if no match found
}
```

## 🔧 **Implementation Plan**

### **Phase 1: Database Schema Updates**

#### File: `packages/database/prisma/schema.prisma`

```prisma
model ChromeExtensionSettings {
  // ... existing fields ...

  // Enhanced scraping tracking
  totalFollowersScraped Int @default(0)
  lastScrapedPosition   Int @default(0)
  lastScrapedUsernames  String[] @default([])
  scrapingTargetReached Boolean @default(false)
  allFollowersScraped   Boolean @default(false)
  lastScrapingSession   DateTime?
}
```

### **Phase 2: API Updates**

#### File: `apps/dashboard/app/api/chrome-extension/status/route.ts`

**Changes Needed:**

1. **Expand Status Enum**: Add dynamic status generation
2. **Enhanced Status Logic**: Support milestone statuses
3. **Position Tracking**: Save/retrieve scraping progress

```typescript
// New status validation schema
const UpdateStatusSchema = z.object({
  extensionStatus: z.string(), // Allow dynamic statuses
  currentActivity: z.string().optional(),
  isConnected: z.boolean().optional(),
  // New fields for enhanced tracking
  totalFollowersScraped: z.number().optional(),
  lastScrapedPosition: z.number().optional(),
  lastScrapedUsernames: z.array(z.string()).optional(),
  scrapingTargetReached: z.boolean().optional(),
  allFollowersScraped: z.boolean().optional(),
});
```

#### File: `apps/dashboard/app/api/chrome-extension/process-followers/route.ts`

**Changes Needed:**

1. **Enhanced Status Determination**: Calculate milestone statuses
2. **Position Tracking**: Save last scraped usernames
3. **Resume Support**: Better position detection

```typescript
// Enhanced status determination logic
function determineExtensionStatus(
  totalScraped: number,
  newFollowersCount: number,
  isComplete: boolean,
  reachedBottom: boolean,
): string {
  if (reachedBottom || isComplete) {
    return "ALL_SCRAPED";
  }

  const newTotal = totalScraped + newFollowersCount;

  // Generate milestone status (every 250 followers)
  const milestone = Math.floor(newTotal / 250) * 250;
  if (milestone > 0 && newTotal >= milestone) {
    return `SCRAPED_${milestone}`;
  }

  return "ACTIVE";
}
```

### **Phase 3: Extension Updates**

#### File: `apps/insta-dm-pro/src/shared/api-service.ts`

**Changes Needed:**

1. **Enhanced Status Types**: Support new status values
2. **Position Tracking**: Send last scraped usernames
3. **Resume Logic**: Handle position detection

```typescript
// Updated status types
export type ExtensionStatus =
  | 'FRESH_START'
  | 'ACTIVE'
  | 'IDLE'
  | 'STOPPED'
  | 'ALL_SCRAPED'
  | `SCRAPED_${number}`; // Dynamic milestone statuses

// Enhanced follower sending
static async sendScrapedFollowers(
  apiKey: string,
  followers: ScrapedFollower[],
  startPosition: number,
  totalFollowers?: number,
  isComplete?: boolean,
  lastScrapedUsernames?: string[] // New parameter
): Promise<SendFollowersResult>
```

#### File: `apps/insta-dm-pro/src/background/index.ts`

**Changes Needed:**

1. **Status Handling**: Support new milestone statuses
2. **Resume Logic**: Use enhanced position detection
3. **Progress Tracking**: Save last scraped usernames

```typescript
// Enhanced status checking
const shouldScrape =
  currentStatus === "FRESH_START" ||
  currentStatus === "ACTIVE" ||
  (currentStatus.startsWith("SCRAPED_") &&
    !currentStatus.includes("ALL_SCRAPED")) ||
  (localProgress && !localProgress.isComplete);
```

### **Phase 4: Enhanced Resume Logic**

#### File: `apps/insta-dm-pro/src/content/Component.tsx`

**Changes Needed:**

1. **Position Detection**: Implement smart resume logic
2. **Username Tracking**: Save last 10 scraped usernames
3. **Progress Reporting**: Enhanced progress tracking

```typescript
// Enhanced resume position detection
const findResumePosition = (
  followers: FollowerElement[],
  lastScrapedUsernames: string[],
): number => {
  if (!lastScrapedUsernames || lastScrapedUsernames.length === 0) {
    return 0;
  }

  // Look for a window where 7+ of the last 10 usernames appear
  for (let i = 0; i < followers.length - 10; i++) {
    const window = followers.slice(i, i + 10);
    const windowUsernames = window.map((f) => f.username);
    const matches = windowUsernames.filter((u) =>
      lastScrapedUsernames.includes(u),
    );

    if (matches.length >= 7) {
      console.log(
        `🎯 Found resume position at index ${i + 10} with ${matches.length} matches`,
      );
      return i + 10; // Start scraping after this window
    }
  }

  console.log("⚠️ No resume position found, starting from beginning");
  return 0;
};
```

## 📊 **Status Flow Diagram**

```
FRESH_START (0 followers)
    ↓ (scrape 250)
SCRAPED_250 (250 followers)
    ↓ (scrape 250 more)
SCRAPED_500 (500 followers)
    ↓ (scrape 250 more)
SCRAPED_750 (750 followers)
    ↓ (scrape 250 more)
SCRAPED_1000 (1000 followers)
    ↓ (continue...)
SCRAPED_1250, SCRAPED_1500, etc.
    ↓ (reach end of followers)
ALL_SCRAPED (final state)
```

## 🎯 **Benefits of This Architecture**

1. **Flexible Milestones**: Support any number of followers (250, 500, 750, 1000+)
2. **Reliable Resume**: Track last 10 usernames for robust position detection
3. **Progress Visibility**: Clear status progression for users
4. **Fault Tolerance**: Handle unfollows and list changes gracefully
5. **Scalable**: Works for accounts with any number of followers

## 🔄 **Migration Strategy**

1. **Database Migration**: Add new fields to ChromeExtensionSettings
2. **Backward Compatibility**: Map old `SCRAPED_250_FOLLOWERS` to `SCRAPED_250`
3. **Gradual Rollout**: Update APIs first, then extension
4. **Data Migration**: Convert existing progress data to new format

## 🧪 **Testing Strategy**

1. **Unit Tests**: Status generation logic
2. **Integration Tests**: API endpoint behavior
3. **E2E Tests**: Full scraping workflow
4. **Resume Tests**: Position detection accuracy
5. **Edge Cases**: Handle unfollows, account changes, etc.

## 📝 **Implementation Checklist**

- [ ] Update database schema with new fields
- [ ] Expand status enum in API endpoints
- [ ] Implement dynamic status generation logic
- [ ] Add position tracking to process-followers API
- [ ] Update extension status handling
- [ ] Implement enhanced resume logic
- [ ] Add last-usernames tracking
- [ ] Update error handling for new statuses
- [ ] Add comprehensive logging
- [ ] Test migration from old to new system

This architecture provides a robust, scalable solution for tracking follower scraping progress with reliable resume capabilities and clear milestone progression.
