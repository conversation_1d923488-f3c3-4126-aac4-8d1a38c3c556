import Browser from 'webextension-polyfill'

export const keys = {
  config: '_current_config',
  main_tab_id: '_main_tab_id',
}

class DataLayer {
  static async getConfig(): Promise<{
    power: boolean
    status: string
    messages: Array<string>
    onlyZeroMessages: boolean
    waitTime: Array<number>
    timeBetweenDM: Array<number>
    messageLineDelay: Array<number>
    followerAge: Array<number>
    dmpool: Array<string>
    dmPastFollowers: boolean
    dmCount: Array<number>
    dmBreakTime: Array<number>
    zero_message_cache: Array<string>
    pastFollowerTimeBetweenDM: Array<number>
    pastFollowerDMCount: Array<number>
    pastFollowerBreakTime: Array<number>
  }> {
    let result = await Browser.storage.local.get([keys.config])
    return (
      result[keys.config] || {
        power: false,
        status: '',
        messages: ['Hello! 👋'],
        onlyZeroMessages: false,
        waitTime: [1, 2],
        timeBetweenDM: [1, 1],
        messageLineDelay: [10, 30],
        followerAge: [0, 1],
        dmpool: [],
        dmPastFollowers: false,
        dmCount: [10, 20],
        dmBreakTime: [5, 10],
        zero_message_cache: [],
        pastFollowerTimeBetweenDM: [1, 2],
        pastFollowerDMCount: [87, 157],
        pastFollowerBreakTime: [10, 15]
      }
    )
  }

  static async setConfig(data: any) {
    await Browser.storage.local.set({
      [keys.config]: data,
    })
  }

  // Method to get main tab id
  static async getMainTabId(): Promise<string | null> {
    let result = await Browser.storage.local.get([keys.main_tab_id])
    return result[keys.main_tab_id] || null
  }

  // Method to set main tab id
  static async setMainTabId(tabId: string) {
    await Browser.storage.local.set({
      [keys.main_tab_id]: tabId,
    })
  }

  // Method to remove main tab id
  static async removeMainTabId() {
    await Browser.storage.local.remove([keys.main_tab_id])
  }


  // Method to get sec tab id
  static async getSecTabId(): Promise<string | null> {
    let result = await Browser.storage.local.get(['_sec_tab_id'])
    return result['_sec_tab_id'] || null
  }

  // Method to set sec tab id
  static async setSecTabId(tabId: string) {
    await Browser.storage.local.set({
      '_sec_tab_id': tabId,
    })
  }

  // Method to remove sec tab id
  static async removeSecTabId() {
    await Browser.storage.local.remove(['_sec_tab_id'])
  }
}

export default DataLayer
