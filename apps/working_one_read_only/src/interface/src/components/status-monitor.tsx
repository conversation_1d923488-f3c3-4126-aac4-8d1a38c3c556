/* eslint-disable */

import { useEffect, useState } from "react"
import { Badge } from "./ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/card"
import Browser from "webextension-polyfill"
import MessageTypes from "../lib/messagetypes"

interface StatusInfo {
  apiConnected: boolean;
  organizationName?: string;
  pendingFollowUps: number;
  lastFollowUpCheck?: string;
  extensionVersion: string;
  botRunning: boolean;
}

export function StatusMonitor() {
  const [status, setStatus] = useState<StatusInfo>({
    apiConnected: false,
    pendingFollowUps: 0,
    extensionVersion: '1.0.0',
    botRunning: false
  })

  useEffect(() => {
    const updateStatus = async () => {
      try {
        // Get API status
        const apiStatus = await Browser.runtime.sendMessage({
          type: MessageTypes.GET_API_STATUS
        })

        // Get pending follow-ups
        const followUpsResult = await Browser.runtime.sendMessage({
          type: MessageTypes.FETCH_PENDING_FOLLOWUPS
        })

        // Get bot status
        const botStatus = await Browser.runtime.sendMessage({
          type: MessageTypes.GET_STATUS
        })

        // Get extension version
        const manifest = Browser.runtime.getManifest()

        setStatus({
          apiConnected: apiStatus?.isAuthenticated || false,
          organizationName: apiStatus?.organizationName,
          pendingFollowUps: followUpsResult?.success ? (followUpsResult.data?.length || 0) : 0,
          lastFollowUpCheck: apiStatus?.lastVerified,
          extensionVersion: manifest.version,
          botRunning: botStatus?.power || false
        })
      } catch (error) {
        console.error('Error updating status:', error)
      }
    }

    updateStatus()
    const interval = setInterval(updateStatus, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-sm font-medium">Extension Status</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* API Connection */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">API Connection</span>
          {status.apiConnected ? (
            <Badge variant="default" className="bg-green-500 text-xs">
              Connected
            </Badge>
          ) : (
            <Badge variant="destructive" className="text-xs">
              Disconnected
            </Badge>
          )}
        </div>

        {/* Organization */}
        {status.organizationName && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Organization</span>
            <span className="text-xs font-medium">{status.organizationName}</span>
          </div>
        )}

        {/* Bot Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Bot Status</span>
          {status.botRunning ? (
            <Badge variant="default" className="bg-blue-500 text-xs">
              Running
            </Badge>
          ) : (
            <Badge variant="outline" className="text-xs">
              Stopped
            </Badge>
          )}
        </div>

        {/* Pending Follow-ups */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Pending Follow-ups</span>
          {status.pendingFollowUps > 0 ? (
            <Badge variant="default" className="bg-orange-500 text-xs">
              {status.pendingFollowUps}
            </Badge>
          ) : (
            <Badge variant="outline" className="text-xs">
              0
            </Badge>
          )}
        </div>

        {/* Extension Version */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Version</span>
          <span className="text-xs font-mono">{status.extensionVersion}</span>
        </div>

        {/* Last Check */}
        {status.lastFollowUpCheck && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Last Check</span>
            <span className="text-xs">
              {new Date(status.lastFollowUpCheck).toLocaleTimeString()}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/* eslint-enable */
