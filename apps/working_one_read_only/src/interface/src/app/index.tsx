import { Sidebar } from '../components/sidebar'
import { useState, useEffect } from 'react';
import Browser from 'webextension-polyfill';
import Home from './Home';
import AutoDM from './AutoDM';
import AutoDMMessages from './AutoDMMessages';
import Settings from './Settings';
import ApiSettings from './ApiSettings';
import FollowUps from './FollowUps';

// Component to show when API key is required
function ApiKeyRequired() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center space-y-4 p-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
        <div className="text-6xl">🔐</div>
        <h2 className="text-2xl font-bold text-gray-900">API Key Required</h2>
        <p className="text-gray-600 max-w-md">
          Please configure your API key in the API Settings to access this feature.
        </p>
        <p className="text-sm text-gray-500">
          Get your API key from the dashboard at{' '}
          <a href="https://app.aisetter.pl" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
            app.aisetter.pl
          </a>
        </p>
      </div>
    </div>
  )
}

export default function MainApp() {
  const [activePathName, setActivePathName] = useState("/api-settings"); // Start with API settings
  const [isApiAuthenticated, setIsApiAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Check API authentication status
  useEffect(() => {
    const checkApiStatus = async () => {
      try {
        const result = await Browser.storage.local.get(['apiStatus']);
        const apiStatus = result.apiStatus;
        const isAuthenticated = apiStatus?.isAuthenticated || false;
        setIsApiAuthenticated(isAuthenticated);

        // If not authenticated, force to API settings
        if (!isAuthenticated && activePathName !== "/api-settings") {
          setActivePathName("/api-settings");
        }
      } catch (error) {
        console.error('Error checking API status:', error);
        setIsApiAuthenticated(false);
        setActivePathName("/api-settings");
      } finally {
        setIsLoading(false);
      }
    };

    checkApiStatus();

    // Listen for API status changes
    const handleStorageChange = (changes: any) => {
      if (changes.apiStatus) {
        const newStatus = changes.apiStatus.newValue?.isAuthenticated || false;
        setIsApiAuthenticated(newStatus);

        // If API key becomes invalid, redirect to API settings
        if (!newStatus && activePathName !== "/api-settings") {
          setActivePathName("/api-settings");
        }
      }
    };

    Browser.storage.onChanged.addListener(handleStorageChange);
    return () => Browser.storage.onChanged.removeListener(handleStorageChange);
  }, [activePathName]);

  // Protected routes that require API key
  const protectedRoutes = ["/", "/auto-dm", "/follow-ups", "/auto-dm-messages", "/settings"];
  const isProtectedRoute = protectedRoutes.includes(activePathName);
  const shouldShowApiRequired = isProtectedRoute && !isApiAuthenticated && !isLoading;

  return (
    <div className="flex h-screen">
      <Sidebar activePathName={activePathName} changeActivePathName={setActivePathName} />
      <main className="flex-1 overflow-y-auto p-6">
        {shouldShowApiRequired ? (
          <ApiKeyRequired />
        ) : (
          <>
            {activePathName === "/" && <Home />}
            {activePathName === "/auto-dm" && <AutoDM />}
            {activePathName === "/follow-ups" && <FollowUps />}
            {activePathName === "/auto-dm-messages" && <AutoDMMessages />}
            {activePathName === "/api-settings" && <ApiSettings />}
            {activePathName === "/settings" && <Settings />}
          </>
        )}
      </main>
    </div>
  )
}
