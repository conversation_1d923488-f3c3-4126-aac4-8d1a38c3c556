
/* eslint-disable */

import { useEffect, useState } from "react"
import { Label } from "../components/ui/label"
import { Button } from "../components/ui/button"
import Browser from "webextension-polyfill"
import MessageTypes from "../lib/messagetypes"
import { RangeSlider } from "../components/ui/range_slider"
import { guard } from "../lib/common-utils"

export default function Settings() {
  const [timeBetweenDM, setTimeBetweenDM] = useState([1, 1])
  const [messageLineDelay, setMessageLineDelay] = useState([10, 30])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [reset, setReset] = useState(false);
  const [resetCache, setResetCache] = useState(false);

  useEffect(() => {
    Browser.runtime.sendMessage({
      type: MessageTypes.GET_SETTINGS
    }).then((settings) => {
      setTimeBetweenDM(guard(settings.timeBetweenDM, [1,1]));
      setMessageLineDelay(guard(settings.messageLineDelay, [10, 30]));
      setIsLoading(false)
    })
  }, [])

  const validateSettings = () => {
    const errors = []

    if (timeBetweenDM[0] >= timeBetweenDM[1]) {
      errors.push("Time between DMs: minimum must be less than maximum")
    }
    if (messageLineDelay[0] >= messageLineDelay[1]) {
      errors.push("Message line delay: minimum must be less than maximum")
    }

    return errors
  }

  const handleSave = () => {
    const validationErrors = validateSettings()

    if (validationErrors.length > 0) {
      alert("Please fix the following errors:\n" + validationErrors.join("\n"))
      return
    }

    setIsSaving(true)
    console.log("Saving Auto DM settings:", { timeBetweenDM, messageLineDelay })
    Browser.runtime.sendMessage({
      type: MessageTypes.SAVE_SETTINGS,
      data: { timeBetweenDM, messageLineDelay }
    }).then(() => {
      setTimeout(() => {
        setIsSaving(false)
      }, 1000);
    })
  }

  const resetDMPool = () => {
    Browser.runtime.sendMessage({
        type: MessageTypes.RESET_DM_POOL
    });
    setReset(true);
    setTimeout(() => {
        setReset(false);
    }, 1000);
  }

  const resetNonZeroMessage = () => {
    Browser.runtime.sendMessage({
      type: MessageTypes.RESET_NONZERO_CACHE
  });
  setResetCache(true);
  setTimeout(() => {
      setResetCache(false);
  }, 1000);
  }

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold">Auto DM Settings</h1>

    {isLoading ? <div>Loading...</div> : <>
    <div className="space-y-4">
    <Label>How long to wait between DM's? ({timeBetweenDM.join('-')} minutes)</Label>
    <RangeSlider value={timeBetweenDM} onValueChange={(value: number[]) => setTimeBetweenDM(value)} min={1} max={99} step={1} />
    </div>

    <div className="space-y-4">
    <Label>Delay between message lines? ({messageLineDelay.join('-')} seconds)</Label>
    <RangeSlider value={messageLineDelay} onValueChange={(value: number[]) => setMessageLineDelay(value)} min={1} max={120} step={1} />
    </div>
    <div className="space-y-4">
    <Label>Clear DM Pool? (if you do this it may resend the messages for the notifications you've not yet viewed)</Label>
    <br/>
    <Button onClick={resetDMPool}>{reset ? "Cleared!" : "Clear DM Pool"}</Button>
    </div>
    <div className="space-y-4">
    <Label>Clear Non-Zero message cache? (if you do this it may remake this if you use only zero message setting.)</Label>
    <br/>
    <Button onClick={resetNonZeroMessage}>{resetCache ? "Cleared!" : "Clear Non-Zero Message Cache"}</Button>
    </div>
    </>}

      <Button onClick={handleSave} disabled={isSaving || isLoading}>{isSaving ? "Saving..." : "Save Settings"}</Button>
    </div>
  )
}


/* eslint-disable */