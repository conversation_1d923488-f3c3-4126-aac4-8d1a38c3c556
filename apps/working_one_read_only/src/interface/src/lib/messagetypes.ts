const MessageTypes = {
    GET_COOKIE: "getCookie",
    TEST_TAB_BUTTON: 'testTabButton',
    SAVE_SETTINGS: 'saveSettings',
    GET_SETTINGS: 'getSettings',
    GET_STATUS: 'getStatus',
    START_BOT: 'startBot',
    STOP_BOT: 'stopBot',
    START_PROCESS: 'startProcess',
    STOP_PROCESS: 'stopProcess',
    RESET_DM_POOL: 'resetDMPool',
    GET_INSTA_INBOX: 'getInstaInbox',
    HAVE_ZERO_MESSAGES: 'haveZeroMessages',
    VISIT_USER_MESSAGES: 'visitUserMessages',
    MESSAGE_PROFILE: 'messageProfile',
    RESET_NONZERO_CACHE: 'resetNonZeroCache',
    GET_PROFILE_URL: 'getProfileUrl',
    OPEN_FOLLOWERS_PANEL: 'openFollowersPanel',
    GET_FOLLOWERS_LIST_BY_COUNT: 'getFollowersList',
    // API Key functionality
    SAVE_API_KEY: 'saveApiKey',
    GET_API_KEY: 'getApiKey',
    VERIFY_API_KEY: 'verifyApiKey',
    GET_API_STATUS: 'getApiStatus',
    // Follow-up functionality
    FETCH_PENDING_FOLLOWUPS: 'fetchPendingFollowUps',
    SEND_FOLLOWUP_MESSAGE: 'sendFollowUpMessage',
    MARK_FOLLOWUP_SENT: 'markFollowUpSent',
    MARK_FOLLOWUP_FAILED: 'markFollowUpFailed',
    // Auto-DM functionality
    SET_AUTO_DM_ENABLED: 'setAutoDMEnabled',
    GET_AUTO_DM_STATUS: 'getAutoDMStatus'
}

export default MessageTypes;