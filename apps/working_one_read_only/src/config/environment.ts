/**
 * Environment configuration for the AISetter Chrome Extension
 */

export interface Environment {
  apiBaseUrl: string;
  isDevelopment: boolean;
  version: string;
}

// Detect environment based on extension context
const isDevelopment = process.env.NODE_ENV === 'development' || 
  (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getManifest().version.includes('dev'));

export const environment: Environment = {
  apiBaseUrl: isDevelopment ? 'http://localhost:3000' : 'https://app.aisetter.pl',
  isDevelopment,
  version: '1.0.0'
};

export default environment;
