import { render } from 'preact'
import '../base.css'
import Component from './Component'
import lg from '../logger'

async function mount() {
  const container = document.createElement('div')
  container.className = 'insta-container'
  container.id = 'instaContainer'
  const bdy = document.querySelector('body')
  if (bdy) {
    bdy.prepend(container)
  }

  lg.info("[index.tsx] mounting component root.")

  render(<Component />, container)
}

async function run() {
  mount()
}

run()
