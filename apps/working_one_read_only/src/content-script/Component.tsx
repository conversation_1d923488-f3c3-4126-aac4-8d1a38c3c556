import { useEffect, useRef } from 'react'
import Browser from 'webextension-polyfill'
import { GeneralConfig } from '../config'
import { MessageTypes } from '../messaging'
import delay from '../background/Utils/delay'

const logNow = (...all: any) => {
  try {
    const timestamp = new Date().toISOString()
    const existingLogs = localStorage.getItem('logs') || '[]'
    const logs = JSON.parse(existingLogs)
    logs.push({
      timestamp,
      data: all,
    })
    // console.log(`-> `, logs[logs.length - 1])
    console.log(`${timestamp}: `, ...all)
    localStorage.setItem('logs', JSON.stringify(logs))
  } catch (error) {
    console.warn('error :: ', error)
  }
}

let isLoopOn = false

const Component = () => {
  const botRunning = useRef(false)

  // Initialize localStorage-based Instagram DM handler
  useEffect(() => {
    logNow('Initializing content script with localStorage approach')

    const script = document.createElement('script')
    script.src = Browser.runtime.getURL('ijsource-localstorage.js')
    script.onload = () => {
      logNow('ijsource-localstorage.js loaded successfully')
      script.remove()
    }
    script.onerror = () => {
      logNow('Failed to load ijsource-localstorage.js')
      script.remove()
    }
    document.head.appendChild(script)
  }, [])

  /** Bot start */

  const simulateKeyboardActivity = async () => {
    // b opens report dialog!!
    const keys = 'acdefghijklmnopqrstuvwxyz'.split('')
    const randomKey = keys[Math.floor(Math.random() * keys.length)]

    document.dispatchEvent(new KeyboardEvent('keydown', { key: randomKey, bubbles: true }))
    await delay(Math.random() * 100 + 30) // Natural key press time
    document.dispatchEvent(new KeyboardEvent('keyup', { key: randomKey, bubbles: true }))
  }

  // Simulate mouse movements with realistic curves and speeds
  const simulateMouseMovement = async () => {
    const startX = Math.random() * window.innerWidth
    const startY = Math.random() * window.innerHeight
    const endX = Math.random() * window.innerWidth
    const endY = Math.random() * window.innerHeight

    // Generate curve points
    for (let i = 0; i < 10; i++) {
      const progress = i / 10
      const curveX = startX + (endX - startX) * progress + (Math.random() - 0.5) * 50
      const curveY = startY + (endY - startY) * progress + (Math.random() - 0.5) * 50

      document.dispatchEvent(
        new MouseEvent('mousemove', {
          bubbles: true,
          clientX: curveX,
          clientY: curveY,
        }),
      )
      await delay(Math.random() * 50 + 20) // Natural mouse speed
    }
  }

  // Simulate scrolling behavior
  const simulateScrolling = async () => {
    const scrollAmount = Math.random() * 500 + 100
    window.scrollBy({
      top: scrollAmount,
      behavior: 'smooth',
    })
    await delay(Math.random() * 1000 + 500)
    window.scrollBy({
      top: -scrollAmount / 2,
      behavior: 'smooth',
    })
  }

  // Simulate tab/window focus changes
  const simulateFocusActivity = async () => {
    window.dispatchEvent(new Event('focus'))
    await delay(Math.random() * 500 + 200)
  }

  // Simulate touch events for mobile
  const simulateTouchActivity = async () => { }

  const getChatArea = () => {
    const chat_area = document.querySelectorAll('[aria-label][role="grid"]')
    if (chat_area.length > 0) {
      return chat_area[0]
    }
    return null
  }

  const navigateAround = async () => {
    let scrollPanel = null
    let all_chats: any[] = []
    let chatboxlist = Array.from(document.querySelectorAll('[role="list"]'))
    for (let i = 0; i < chatboxlist.length; i++) {
      const element = chatboxlist[i]
      try {
        scrollPanel = element.children[0].children[0].children[0]
        if (scrollPanel) {
          break
        }
      } catch (error) { }
    }

    if (scrollPanel) {
      all_chats = Array.from(scrollPanel.querySelectorAll('[role="button"][tabindex="0"]'))
    }

    return {
      panel: scrollPanel,
      // chats: all_chats,
      chats: [],
    }
  }

  const mimickUserActivity = async () => {
    while (true) {
      if (!botRunning.current) {
        isLoopOn = false
        break
      }
      isLoopOn = true
      const activities = [
        simulateKeyboardActivity,
        simulateMouseMovement,
        simulateScrolling,
        simulateFocusActivity,
        simulateTouchActivity,
      ]

      // Randomly select and execute 1-3 activities
      const numActivities = Math.floor(Math.random() * 3) + 1
      for (let i = 0; i < numActivities; i++) {
        const randomActivity = activities[Math.floor(Math.random() * activities.length)]
        await randomActivity()
      }
      let for_chats = await navigateAround()
      if (for_chats.panel && for_chats.chats && for_chats.chats.length > 0) {
        // Randomly scroll up or down
        const scrollDirection = Math.random() > 0.5 ? 1 : -1
        const scrollAmount = Math.floor(Math.random() * 300) + 100 // Random scroll between 100-400px
        for_chats.panel.scrollTop += scrollAmount * scrollDirection
        let current_chat = for_chats.chats[Math.floor(Math.random() * for_chats.chats.length)]
        await delay(Math.random() * 3000 + 1000)
        if (current_chat) {
          ; (current_chat as HTMLElement).click()
          await delay(Math.random() * 6000 + 1000)
          let chat_panel = getChatArea()
          if (chat_panel) {
            const scrollDirection = Math.random() > 0.5 ? 1 : -1
            const scrollAmount = Math.floor(Math.random() * 300) + 100 // Random scroll between 100-400px
            chat_panel.scrollTop += scrollAmount * scrollDirection
            let scroll_again = Math.random() > 0.5 ? 1 : 0
            await delay(Math.random() * 2000 + 1000)
            if (scroll_again) {
              const scrollDirection = Math.random() > 0.5 ? 1 : -1
              const scrollAmount = Math.floor(Math.random() * 300) + 100 // Random scroll between 100-400px
              chat_panel.scrollTop += scrollAmount * scrollDirection
            }
            await delay(Math.random() * 3000 + 1000)
          }
        }
      }
      // Random pause between activity bursts
      await delay(Math.random() * 9000 + 1000)
    }
  }

  /** Bot end */

  const readInstagramInbox = async () => {
    let csrf_token = await Browser.runtime.sendMessage({
      type: MessageTypes.GET_COOKIE,
      data: { url: 'https://www.instagram.com', name: 'csrftoken' },
    })

    const inboxResponse = await fetch('https://i.instagram.com/api/v1/news/inbox/', {
      credentials: 'include',
      headers: {
        accept: 'application/json, text/plain, */*',
        Referer: 'https://www.instagram.com/',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'x-asbd-id': '129477',
        'X-IG-App-ID': '936619743392459',
        'x-instagram-ajax': '1',
        'X-CSRFToken': csrf_token,
        'x-requested-with': 'XMLHttpRequest',
      },
      body: null,
      method: 'POST',
    })
    const inboxData = await inboxResponse.json()
    return inboxData
  }

  const getInstagramStories = async (conf: GeneralConfig) => {
    const inbox = await readInstagramInbox()
    let stories = []
    if (inbox.status && inbox.status === 'ok') {
      let total_stories = [...inbox.new_stories, ...inbox.old_stories]
      for (let i = 0; i < total_stories.length; i++) {
        const story = total_stories[i]
        if (story.story_type === 101 && story.type === 3) {

          // let minWait = conf.waitTime[0] * 60 * 1000;
          // let maxWait = conf.waitTime[1] * 60 * 1000;
          // let randomWait = Math.floor(Math.random() * (maxWait - minWait + 1)) + minWait;



          stories.push({
            id: story.args.profile_id,
            username: story.args.profile_name,
            profile_image: story.args.profile_image,
            type: 0,
            timestamp: story.args.timestamp,
          })
        } else {
          console.log('not the ig_follow one!')
        }
      }

      return {
        status: 'success',
        data: stories,
      }
    } else {
      return {
        status: 'failed',
        message: 'Failed to fetch inbox',
      }
    }
  }

  const createGroupThread = async (recipent_id: string) => {
    let u = 'https://i.instagram.com/api/v1/direct_v2/create_group_thread/'
    let csrf_token = await Browser.runtime.sendMessage({
      type: MessageTypes.GET_COOKIE,
      data: { url: 'https://www.instagram.com', name: 'csrftoken' },
    })
    const s = { recipient_users: '["'.concat(recipent_id, '"]') },
      l = new URLSearchParams([...Object.entries(s)]).toString()

    let response = await fetch(u, {
      credentials: 'include',
      headers: {
        accept: 'application/json, text/plain, */*',
        'content-type': 'application/x-www-form-urlencoded',
        Referer: 'https://www.instagram.com/',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'x-asbd-id': '129477',
        'X-IG-App-ID': '936619743392459',
        'x-instagram-ajax': '1',
        'X-CSRFToken': csrf_token,
        'x-requested-with': 'XMLHttpRequest',
      },
      body: l,
      method: 'POST',
    })
    const data = await response.json()
    return data
  }

  const visitUserMessages = async (username: string) => {
    logNow('visitUserMessages called', { username, currentUrl: window.location.href, isDMPage: window.location.href.includes('/direct/') })
    console.log(`Starting visitUserMessages for user: ${username}`)

    let directClassName = `x1i10hfl xjqpnuy xa49m3k xqeqjp1 x2hbi6w x972fbf xcfux6l x1qhh985 xm0m39n xdl72j9 x2lah0s xe8uvvx xdj266r x11i5rnm xat24cr x1mh8g0r x2lwn1j xeuugli xexx8yu x18d9i69 x1hl2dhg xggy1nq x1ja2u2z x1t137rt x1q0g3np x1lku1pv x1a2a7pz x6s0dn4 xjyslct x1lq5wgf xgqcy7u x30kzoy x9jhf4c x1ejq31n xd10rxx x1sy0etr x17r0tee x9f619 x1ypdohk x78zum5 x1f6kntn xwhw2v2 x10w6t97 xl56j7k x17ydfre x1swvt13 x1pi30zi x1n2onr6 x2b8uid xlyipyv x87ps6o x14atkfc xcdnw81 x1i0vuye x1gjpkn9 x5n08af xsz8vos`
    let directOptionsButton = `x1i10hfl x972fbf xcfux6l x1qhh985 xm0m39n x9f619 xe8uvvx xdj266r x11i5rnm xat24cr x1mh8g0r x16tdsg8 x1hl2dhg xggy1nq x1a2a7pz x6s0dn4 xjbqb8w x1ejq31n xd10rxx x1sy0etr x17r0tee x1ypdohk x78zum5 xl56j7k x1y1aw1k x1sxyh0 xwib8y2 xurb0ha xcdnw81`

    let message_button = null

    message_button = document.querySelector(`.${directClassName.split(' ').join('.')}`)

    let all_hdr_buttons = document.querySelector('header')?.querySelectorAll('[role="button"]')

    console.log(`Header buttons found: ${all_hdr_buttons?.length || 0}`)

    if (all_hdr_buttons && all_hdr_buttons.length > 0) {
      console.log('Likely message button exists, checking...')
      all_hdr_buttons.forEach((btn) => {
        if ((btn as HTMLElement).innerText.toLowerCase().includes('message')) {
          message_button = btn
        }
      })
    }

    if (!message_button) {
      if ((all_hdr_buttons?.length || 0) >= 3) {
        if (!message_button) {
          console.log('Message button not found, assuming index 0.')
          message_button = (all_hdr_buttons as any)[0]
          let is_profile_pic_btn = message_button.querySelector('canvas')
          console.log('Is it profile pic button :: ', is_profile_pic_btn)
          if (is_profile_pic_btn) {
            message_button = null
          } else {
            if (((message_button as HTMLElement).innerText || '').length < 1) {
              console.log("It doesn't seems message button!")
              message_button = null
            }
          }
        } else {
        }
      }
    }

    if (message_button) {
      await delay(2000)
      logNow('visitUserMessages: Attempting to click message_button:', message_button)
      console.log('Message button found, clicking...')
        ; (message_button as HTMLElement).click()
    } else {
      logNow('visitUserMessages: message_button not found directly. Header HTML:', document.querySelector('header')?.innerHTML)
      console.log('Message button still not found, checking options...')
      let options_button = document
        .querySelector('[aria-label="Options"]')
        ?.closest('[role="button"]')

      if (!options_button) {
        options_button = document.querySelector(`.${directOptionsButton.split(' ').join('.')}`)
      }

      if (options_button) {
        logNow('visitUserMessages: Attempting to click options_button:', options_button)
        console.log('Options button found, clicking... :: ', options_button)
          ; (options_button as any).click()
      } else {
        logNow('visitUserMessages: Options button not found by aria-label="Options" or class selector.')
        console.log('Options button not found.')
      }

      await delay(2000)

      let dialog = document.querySelector('[role="dialog"]')
      let dialog_buttons = dialog?.querySelectorAll('button')

      console.log(`Dialog buttons found: ${dialog_buttons?.length || 0}`)

      let found_index = -1
      dialog_buttons?.forEach((dialog_btn, i) => {
        if (dialog_btn.innerText.toLowerCase().includes('send message')) {
          found_index = i
        }
      })

      if (found_index > -1) {
        logNow('visitUserMessages: Attempting to click "Send Message" in dialog. Found index:', found_index, 'Button:', dialog_buttons?.[found_index])
        console.log(`Send message button found at index ${found_index}, clicking...`)
          ; (dialog_buttons as any)[found_index].click()
      } else {
        if (dialog_buttons?.length === 7) {
          found_index = 5
          logNow('visitUserMessages: Fallback: Attempting to click "Send Message" in dialog at index 5. Button:', dialog_buttons?.[found_index])
          console.log(`Fallback: Clicking index ${found_index} in dialog buttons.`)
            ; (dialog_buttons as any)[found_index].click()
        } else {
          logNow('visitUserMessages: "Send Message" button in dialog NOT found. Dialog HTML:', document.querySelector('[role="dialog"]')?.innerHTML)
          console.error('Unable to locate send message button!')
        }
      }
    }

    await delay(2000)
    logNow('visitUserMessages completed', { username, finalUrl: window.location.href, isDMPage: window.location.href.includes('/direct/') })
  }

  function getLinksWithImages(classString: string): HTMLAnchorElement[] {
    let selector: string = '.' + classString.split(' ').join('.')
    let element: Element | null = document.querySelector(selector)
    console.log('getLinksWithImages Selector :: ', selector, element)

    if (!element) return []

    let links: NodeListOf<HTMLAnchorElement> = element.querySelectorAll('a[role="link"][href^="/"]')
    let linksWithImages: HTMLAnchorElement[] = []

    console.log('Links :: ', links)

    const links_to_avoid = [
      '/',
      '/',
      '#',
      '/explore/',
      '/reels/',
      '/direct/inbox/',
      '#',
      '#',
      '#',
      // "/alexgodlewsky/",
      // "https://www.threads.net/?xmt=AQGzYnPVLAFYx5D9B3x0hJ6UW5LpfIYBgdtysvnxNCesr9w",
      '#',
    ]

    links.forEach((link) => {
      if (link.querySelector('img')) {
        linksWithImages.push(link)
      }
    })

    console.log('LinkswithImages :: ', linksWithImages)

    if (linksWithImages.length === 0) {
      links.forEach((link) => {
        if (
          !links_to_avoid.includes(link.getAttribute('href') || '') &&
          !link.getAttribute('href')?.startsWith('https://')
        ) {
          linksWithImages.push(link)
        }
      })
    }

    console.log('LinkswithImages part2 :: ', linksWithImages)

    return linksWithImages
  }

  const getProfileLinKUrl = async () => {
    await delay(1000)
    let s =
      'x1uvtmcs x4k7w5x x1h91t0o x1beo9mf xaigb6o x12ejxvf x3igimt xarpa2k xedcshv x1lytzrv x1t2pt76 x7ja8zs x1n2onr6 x1qrby5j x1jfb8zj'
    let result = getLinksWithImages(s)
    console.log('Result :: getProfileLinkyrl :: ', result)
    if (result.length > 0) {
      let l = result[0]
      let ll = l.getAttribute('href')
      return ll
    }
    return null
  }

  function findScrollableElements(root: HTMLElement = document.body): HTMLElement[] {
    const scrollableElements: HTMLElement[] = []

    function checkElement(el: HTMLElement) {
      const style = window.getComputedStyle(el)
      if (
        (style.overflowY === 'scroll' || style.overflowY === 'auto') &&
        el.scrollHeight > el.clientHeight
      ) {
        scrollableElements.push(el)
      }
    }

    function traverse(el: HTMLElement) {
      if (el.children.length) {
        Array.from(el.children).forEach((child) => {
          checkElement(child as HTMLElement)
          traverse(child as HTMLElement)
        })
      }
    }

    traverse(root)
    return scrollableElements
  }

  // Enhanced function to find the actual followers container
  function findFollowersScrollContainer(dialog: HTMLElement): HTMLElement | null {
    logNow('🔍 Looking for followers scroll container...')

    // Method 1: Look for elements with overflow-y: scroll in computed styles
    const allDivs = dialog.querySelectorAll('div')
    logNow('📋 Total divs in dialog:', allDivs.length)

    for (let div of allDivs) {
      const htmlDiv = div as HTMLElement
      const computedStyle = window.getComputedStyle(htmlDiv)

      // Look for the element with overflow-y: scroll
      if (computedStyle.overflowY === 'scroll') {
        logNow('🎯 Found element with overflow-y: scroll:', {
          element: htmlDiv,
          className: htmlDiv.className,
          scrollHeight: htmlDiv.scrollHeight,
          clientHeight: htmlDiv.clientHeight,
          overflowY: computedStyle.overflowY,
          canScroll: htmlDiv.scrollHeight > htmlDiv.clientHeight,
          maxHeight: computedStyle.maxHeight
        })

        // Check if this element actually has scrollable content
        if (htmlDiv.scrollHeight > htmlDiv.clientHeight) {
          logNow('✅ Found scrollable element with overflow-y: scroll:', htmlDiv)
          return htmlDiv
        }
      }
    }

    // Method 2: Look for elements with max-height and overflow-y: scroll
    for (let div of allDivs) {
      const htmlDiv = div as HTMLElement
      const computedStyle = window.getComputedStyle(htmlDiv)

      if (computedStyle.maxHeight && computedStyle.maxHeight !== 'none' &&
        (computedStyle.overflowY === 'scroll' || computedStyle.overflowY === 'auto')) {
        logNow('🎯 Found element with max-height and overflow:', {
          element: htmlDiv,
          className: htmlDiv.className,
          scrollHeight: htmlDiv.scrollHeight,
          clientHeight: htmlDiv.clientHeight,
          overflowY: computedStyle.overflowY,
          maxHeight: computedStyle.maxHeight,
          canScroll: htmlDiv.scrollHeight > htmlDiv.clientHeight
        })

        if (htmlDiv.scrollHeight > htmlDiv.clientHeight) {
          logNow('✅ Found scrollable element with max-height:', htmlDiv)
          return htmlDiv
        }
      }
    }

    // Method 3: Look for the third container in containers with max-height
    const containersWithMaxHeight: HTMLElement[] = []
    for (let div of allDivs) {
      const htmlDiv = div as HTMLElement
      const computedStyle = window.getComputedStyle(htmlDiv)
      if (computedStyle.maxHeight && computedStyle.maxHeight !== 'none') {
        containersWithMaxHeight.push(htmlDiv)
      }
    }

    logNow('📋 Found containers with max-height:', containersWithMaxHeight.length)

    if (containersWithMaxHeight.length >= 3) {
      const thirdContainer = containersWithMaxHeight[2] // Third container (0-indexed)
      logNow('🎯 Checking third container with max-height:', {
        element: thirdContainer,
        className: thirdContainer.className,
        scrollHeight: thirdContainer.scrollHeight,
        clientHeight: thirdContainer.clientHeight,
        maxHeight: window.getComputedStyle(thirdContainer).maxHeight
      })

      if (thirdContainer.scrollHeight > thirdContainer.clientHeight) {
        logNow('✅ Using third container with max-height:', thirdContainer)
        return thirdContainer
      }
    }

    // Method 4: Look for elements with specific class patterns that indicate scrollable content
    const scrollableClassPatterns = [
      'xyi19xy', // From the DOM inspector
      'x1ccrb07',
      'xtf3nb5'
    ]

    for (let pattern of scrollableClassPatterns) {
      const elements = dialog.querySelectorAll(`div[class*="${pattern}"]`)
      logNow(`📋 Found ${elements.length} elements with class containing "${pattern}"`)

      for (let element of elements) {
        const htmlElement = element as HTMLElement
        const computedStyle = window.getComputedStyle(htmlElement)
        if ((computedStyle.overflowY === 'scroll' || computedStyle.overflowY === 'auto') &&
          htmlElement.scrollHeight > htmlElement.clientHeight) {
          logNow('✅ Found scrollable element by class pattern:', htmlElement)
          return htmlElement
        }
      }
    }

    // Method 5: Fallback to standard scrollable element detection
    logNow('🔄 Falling back to standard scrollable element detection...')
    return findScrollableElements(dialog)[0] || null
  }

  const openFollowersPanel = async (username: string) => {
    console.log("username :: ", username)
    let selector = `a[href^="/${username}/followers"][role="link"]`
    let result = {
      status: 'failed',
      message: 'Not started yet',
    }
    let followers_list_btn = document.querySelector(selector)
    if (!followers_list_btn) {
      await delay(2000)
      followers_list_btn = document.querySelector(selector)
    }
    if (!followers_list_btn) {
      console.log('ERROR couldnt find follower button')
      result.status = 'error'
      result.message = 'Failed to find followers button.'
      return result
    }
    ; (followers_list_btn as HTMLElement).click()
    await delay(4000)
    let dialog = document.querySelector('[role="dialog"]')
    if (!dialog) {
      ; (followers_list_btn as HTMLElement).click()
      await delay(4000)
      dialog = document.querySelector('[role="dialog"]')
    }
    if (!dialog) {
      console.log('ERROR')
      result.status = 'error'
      result.message = 'Failed to find dialog.'
      return result
    }
    result.status = 'success'
    result.message = 'Followers dialog opened.'
    return result
  }

  async function humanLikeScroll(element: any, direction = 'down', targetScroll = 300, speed = 5) {
    if (!element) {
      logNow('🐛 DEBUG: humanLikeScroll - element is null/undefined')
      return
    }

    logNow('🐛 DEBUG: humanLikeScroll starting', {
      direction,
      targetScroll,
      speed,
      elementType: element.tagName,
      elementClasses: element.className,
      scrollHeight: element.scrollHeight,
      clientHeight: element.clientHeight,
      currentScrollTop: element.scrollTop
    })

    let scrollAmount = 0
    let maxScroll = Math.abs(targetScroll)
    let mainDirection = direction === 'down' ? 1 : -1
    let scrollAttempts = 0
    const maxAttempts = Math.ceil(maxScroll / (speed + 2)) // Estimate max attempts needed

    while (scrollAmount < maxScroll && scrollAttempts < maxAttempts) {
      let scrollStep = Math.random() * speed + 2 // Random step (2 to speed+2)
      let deviation = Math.random() * 2 - 1 // Small deviation
      let delay = Math.random() * 200 + 50 // Reduced delay (50-250ms instead of 100-400ms)

      // Occasionally scroll up for natural behavior
      let randomUpChance = Math.random()
      let scrollDirection = mainDirection

      if (randomUpChance < 0.15) { // Reduced from 20% to 15%
        // 15% chance to scroll in the opposite direction
        scrollDirection *= -1
      }

      const beforeScroll = element.scrollTop

      element.scrollBy({
        top: (scrollStep + deviation) * scrollDirection,
        behavior: 'smooth',
      })

      const afterScroll = element.scrollTop
      const actualScrolled = Math.abs(afterScroll - beforeScroll)

      logNow('🐛 DEBUG: Scroll step', {
        attempt: scrollAttempts + 1,
        scrollStep: scrollStep + deviation,
        scrollDirection,
        beforeScroll,
        afterScroll,
        actualScrolled,
        scrollAmount,
        maxScroll
      })

      scrollAmount += scrollStep * (scrollDirection === mainDirection ? 1 : 0) // Only count forward progress
      scrollAttempts++

      // If we're not actually scrolling anymore (reached bottom), break
      if (actualScrolled === 0 && scrollDirection === mainDirection) {
        logNow('🐛 DEBUG: Reached scroll limit, breaking')
        break
      }

      await new Promise((resolve) => setTimeout(resolve, delay)) // Wait before next step
    }

    logNow('🐛 DEBUG: humanLikeScroll completed', {
      totalScrollAmount: scrollAmount,
      totalAttempts: scrollAttempts,
      finalScrollTop: element.scrollTop,
      reachedTarget: scrollAmount >= maxScroll
    })
  }

  async function getFollowersByCount(followerCount: Array<number>) {
    logNow('🐛 DEBUG: getFollowersByCount started', { followerCount })

    let dialog = document.querySelector('[role="dialog"]')
    logNow('🐛 DEBUG: Dialog found', {
      dialogExists: !!dialog,
      dialogHTML: dialog ? dialog.outerHTML.substring(0, 200) + '...' : 'null'
    })

    // let followerCountToGet = followerCount[Math.floor(Math.random() * followerCount.length)]
    let followerCountToGet = followerCount[0] + followerCount[1]
    let profileLinks = new Set()

    logNow('🐛 DEBUG: Target follower count', { followerCountToGet })

    if (dialog) {
      // Use the enhanced followers container detection
      let scrollingEl = findFollowersScrollContainer(dialog as HTMLElement)

      if (!scrollingEl) {
        logNow('🐛 DEBUG: Enhanced detection failed, falling back to standard detection')
        let scrollableElements = findScrollableElements(dialog as HTMLElement)
        logNow('🐛 DEBUG: Found scrollable elements', {
          count: scrollableElements.length,
          elements: scrollableElements.map(el => ({
            tagName: el.tagName,
            className: el.className,
            scrollHeight: el.scrollHeight,
            clientHeight: el.clientHeight,
            scrollTop: el.scrollTop
          }))
        })
        scrollingEl = scrollableElements[0]
      }
      if (scrollingEl) {
        logNow('🐛 DEBUG: Using scrolling element', {
          tagName: scrollingEl.tagName,
          className: scrollingEl.className,
          scrollHeight: scrollingEl.scrollHeight,
          clientHeight: scrollingEl.clientHeight,
          childrenCount: scrollingEl.children.length
        })

        let prevCount = 0
        let scrollAttempts = 0
        const maxScrollAttempts = 50 // Prevent infinite loops

        while (profileLinks.size < followerCountToGet && scrollAttempts < maxScrollAttempts) {
          logNow('🐛 DEBUG: Scroll attempt', {
            attempt: scrollAttempts + 1,
            currentProfileCount: profileLinks.size,
            targetCount: followerCountToGet
          })

          // Enhanced scrolling with multiple methods
          if (!scrollingEl) {
            logNow('🐛 DEBUG: scrollingEl is null, breaking')
            break
          }

          const beforeScroll = scrollingEl.scrollTop

          // Method 1: scrollBy with smooth behavior
          scrollingEl.scrollBy({
            top: 300,
            behavior: 'smooth'
          })

          // Wait for scroll to complete
          await new Promise(resolve => setTimeout(resolve, 500))

          let afterScroll = scrollingEl.scrollTop
          let actualScrolled = afterScroll - beforeScroll

          // Method 2: Direct scrollTop if scrollBy didn't work
          if (actualScrolled === 0) {
            logNow('🔄 Trying direct scrollTop...')
            scrollingEl.scrollTop += 300
            await new Promise(resolve => setTimeout(resolve, 300))
            afterScroll = scrollingEl.scrollTop
            actualScrolled = afterScroll - beforeScroll
          }

          // Method 3: Try scrolling parent element
          if (actualScrolled === 0 && scrollingEl.parentElement) {
            logNow('🔄 Trying parent element scroll...')
            const parent = scrollingEl.parentElement as HTMLElement
            const parentBefore = parent.scrollTop
            parent.scrollBy({ top: 300, behavior: 'smooth' })
            await new Promise(resolve => setTimeout(resolve, 500))
            const parentAfter = parent.scrollTop
            const parentMoved = parentAfter - parentBefore

            if (parentMoved > 0) {
              logNow(`✅ Parent scrolled: ${parentBefore} → ${parentAfter} (moved ${parentMoved}px)`)
              // Update scrollingEl to parent for future scrolls
              scrollingEl = parent
              afterScroll = parentAfter
              actualScrolled = parentMoved
            }
          }

          // Method 4: Try wheel event simulation
          if (actualScrolled === 0) {
            logNow('🔄 Trying wheel event simulation...')
            const wheelEvent = new WheelEvent('wheel', {
              deltaY: 300,
              bubbles: true,
              cancelable: true
            })
            scrollingEl.dispatchEvent(wheelEvent)
            await new Promise(resolve => setTimeout(resolve, 500))
            afterScroll = scrollingEl.scrollTop
            actualScrolled = afterScroll - beforeScroll
          }

          logNow(`📊 Scroll result: ${beforeScroll} → ${afterScroll} (moved ${actualScrolled}px)`)

          let mainEl = scrollingEl.children[0];
          logNow('🐛 DEBUG: Main element analysis', {
            mainElExists: !!mainEl,
            mainElTagName: mainEl?.tagName,
            mainElChildrenCount: mainEl?.children?.length,
            hasImg: !!mainEl?.querySelector('img')
          })

          // Fix the logic error - if mainEl has no img, try children[1]
          if (!mainEl?.querySelector('img') && scrollingEl.children[1]) {
            logNow('🐛 DEBUG: Main element has no img, trying children[1]')
            mainEl = scrollingEl.children[1];
          }

          // Try multiple possible DOM structures for finding followers
          let followers = null
          const possiblePaths = [
            scrollingEl.children[0]?.children[0]?.children,
            scrollingEl.children[0]?.children,
            scrollingEl.children[1]?.children[0]?.children,
            scrollingEl.children[1]?.children,
            scrollingEl.querySelectorAll('a[href^="/"][href*="/"]') // Direct link search
          ]

          for (let i = 0; i < possiblePaths.length; i++) {
            const path = possiblePaths[i]
            if (path && path.length > 0) {
              followers = path
              logNow('🐛 DEBUG: Found followers using path', { pathIndex: i, followerCount: path.length })
              break
            }
          }

          if (!followers || followers.length === 0) {
            logNow('🐛 DEBUG: No followers found in any path, breaking')
            break
          }

          let newLinksFound = 0
          for (let follower of followers) {
            let link = follower.querySelector ? follower.querySelector('a')?.href : (follower as HTMLAnchorElement).href
            if (link && link.includes('instagram.com/') && !link.includes('/followers') && !link.includes('/following') && !profileLinks.has(link)) {
              profileLinks.add(link)
              newLinksFound++
            }
            if (profileLinks.size >= followerCountToGet) break
          }

          logNow('🐛 DEBUG: Links extraction result', {
            newLinksFound,
            totalLinks: profileLinks.size,
            targetCount: followerCountToGet
          })

          // Check if we're making progress
          if (profileLinks.size === prevCount) {
            logNow('🐛 DEBUG: No new profiles found, checking if we can scroll more')

            // Check if we've reached the bottom
            const isAtBottom = scrollingEl.scrollTop + scrollingEl.clientHeight >= scrollingEl.scrollHeight - 10
            if (isAtBottom || actualScrolled === 0) {
              logNow('🐛 DEBUG: Reached bottom of scroll area or no scroll movement')
              break
            }

            // Wait longer for content to load
            await new Promise((res) => setTimeout(res, 2000))
          } else {
            // Normal wait time when making progress
            await new Promise((res) => setTimeout(res, 1000))
          }

          prevCount = profileLinks.size
          scrollAttempts++
        }

        logNow('🐛 DEBUG: Scrolling completed', {
          totalAttempts: scrollAttempts,
          finalProfileCount: profileLinks.size,
          reachedTarget: profileLinks.size >= followerCountToGet,
          maxAttemptsReached: scrollAttempts >= maxScrollAttempts
        })
      } else {
        logNow('🐛 DEBUG: No scrollable element found in dialog')
      }
    } else {
      logNow('🐛 DEBUG: No dialog found')
    }

    let finalLinks = Array.from(profileLinks)
    finalLinks.splice(0, followerCount[0])

    logNow('🐛 DEBUG: getFollowersByCount completed', {
      totalLinksFound: profileLinks.size,
      finalLinksCount: finalLinks.length,
      skippedCount: followerCount[0]
    })

    console.log('final links :: ', finalLinks, ', profile links :: ', profileLinks)
    return finalLinks
  }

  const checkIfConvoIncludeUsername = async (username: string) => {
    let verify_if_username_is_in_page = document.querySelector('[role="navigation"]')
    if (verify_if_username_is_in_page) {
      if (verify_if_username_is_in_page.nextElementSibling) {
        if (verify_if_username_is_in_page.nextElementSibling.outerHTML.includes(username)) {
          return true
        }
      }
    }
    return false
  }

  const getHaveZeroMessages = async (profile_id: string, username: string) => {
    let result = 'unknown';
    await delay(1000); // Reduced delay

    logNow(`getHaveZeroMessages: Checking for user ${username}, profile_id ${profile_id}`);

    // Try to find the main grid that holds all message rows - language agnostic approach
    // Look for grids that contain message-like content patterns
    let messageListGrid = null;
    const allGrids = document.querySelectorAll('div[role="grid"]');

    // DIAGNOSTIC: Check all grids on page
    logNow(`DIAGNOSTIC: Found ${allGrids.length} total grids on page`);
    allGrids.forEach((grid, index) => {
      logNow(`DIAGNOSTIC: Grid ${index} aria-label:`, grid.getAttribute('aria-label'));
      logNow(`DIAGNOSTIC: Grid ${index} classes:`, grid.className);
    });

    for (const grid of allGrids) {
      const ariaLabel = grid.getAttribute('aria-label') || '';
      // Check for common conversation patterns in multiple languages
      const isConversationGrid =
        ariaLabel.toLowerCase().includes('conversation') ||
        ariaLabel.toLowerCase().includes('konwersacja') ||
        ariaLabel.toLowerCase().includes('messages') ||
        ariaLabel.toLowerCase().includes('wiadomości') ||
        ariaLabel.toLowerCase().includes('chat') ||
        grid.querySelector('div[role="row"]') !== null; // Has message rows

      if (isConversationGrid) {
        messageListGrid = grid;
        logNow(`DIAGNOSTIC: Selected grid with aria-label: "${ariaLabel}"`);
        break;
      }
    }
    logNow('getHaveZeroMessages: messageListGrid found:', messageListGrid ? messageListGrid.outerHTML.substring(0, 200) + '...' : 'null');

    // DIAGNOSTIC: If no grid found, log current page state
    if (!messageListGrid) {
      logNow('DIAGNOSTIC: No messageListGrid found. Current URL:', window.location.href);
      logNow('DIAGNOSTIC: Page title:', document.title);
      logNow('DIAGNOSTIC: Looking for alternative selectors...');

      // Try alternative selectors
      const alternativeGrid = document.querySelector('[aria-label*="Wiadomości w"][role="grid"]');
      logNow('DIAGNOSTIC: Alternative grid found:', alternativeGrid ? alternativeGrid.getAttribute('aria-label') : 'null');
    }

    if (messageListGrid) {
      // Now check for actual message rows within this grid
      const messageRows = messageListGrid.querySelectorAll('div[role="row"]');
      logNow('getHaveZeroMessages: messageRows found inside grid:', messageRows.length);

      // DIAGNOSTIC: Log all rows found
      messageRows.forEach((row, index) => {
        logNow(`DIAGNOSTIC: Row ${index} content preview:`, row.outerHTML.substring(0, 150) + '...');
        logNow(`DIAGNOSTIC: Row ${index} text content:`, row.textContent?.substring(0, 100) || 'no text');
      });

      if (messageRows.length === 0) {
        // No "row" elements found within the grid.
        // This could mean an empty chat, or a chat with only a date separator.
        // A very basic check: are there any direct children at all in the first significant child of the grid?
        // The structure from your example was: grid > div > div > [message rows]
        // Or grid > div > [message rows]
        const firstLevelChildren = Array.from(messageListGrid.children);
        logNow(`DIAGNOSTIC: messageListGrid has ${firstLevelChildren.length} direct children`);

        let potentialMessageContainer = null;
        for (let child of firstLevelChildren) {
          logNow(`DIAGNOSTIC: Checking child with ${child.children.length} children, tagName: ${child.tagName}`);
          if (child.querySelector('div[role="row"]')) { // if a child contains rows, it might be the one
            potentialMessageContainer = child;
            logNow('DIAGNOSTIC: Found child with rows');
            break;
          }
          // Fallback: check if this child itself has children that could be rows or row containers
          if (child.children.length > 0 && child.children[0].children.length > 0) {
            potentialMessageContainer = child.children[0]; // A common pattern is grid > div > div(this one) > rows
            logNow('DIAGNOSTIC: Using child.children[0] as potentialMessageContainer');
          } else if (child.children.length > 0) {
            potentialMessageContainer = child; // Or grid > div(this one) > rows
            logNow('DIAGNOSTIC: Using child as potentialMessageContainer');
          }
        }

        logNow('getHaveZeroMessages: potentialMessageContainer for empty check:', potentialMessageContainer ? potentialMessageContainer.outerHTML.substring(0, 100) + '...' : 'null');

        if (potentialMessageContainer && potentialMessageContainer.childElementCount === 0) {
          logNow('getHaveZeroMessages: potentialMessageContainer has 0 childElementCount.');
          result = 'yes';
        } else if (!potentialMessageContainer && messageRows.length === 0) {
          // If no rows and we couldn't identify a clear container, assume empty.
          logNow('getHaveZeroMessages: No messageRows and no clear potentialMessageContainer.');
          result = 'yes';
        } else if (messageRows.length > 0) {
          // This case should ideally not be hit if messageRows.length === 0 above, but as a safeguard:
          let actualMessageContentFound = false;
          messageRows.forEach((row, index) => {
            // Look for a div with dir="auto" that typically holds message text.
            // This is based on the provided HTML snippet for a message.
            const messageContent = row.querySelector('div[dir="auto"].html-div, span.x193iq5w.xeuugli.x13faqbe');
            logNow(`DIAGNOSTIC: Row ${index} message content selector found:`, messageContent ? 'YES' : 'NO');
            if (messageContent) {
              actualMessageContentFound = true;
              logNow('getHaveZeroMessages: Actual message content signature found in a row:', row.outerHTML.substring(0, 100) + '...');
            }
          });
          result = actualMessageContentFound ? 'no' : 'yes';
          logNow(`getHaveZeroMessages: Rows found, actualMessageContentFound: ${actualMessageContentFound}`);
        } else {
          // No rows, and potentialMessageContainer either doesn't exist or has children.
          // This state is ambiguous. Defaulting to 'yes' if no rows.
          logNow('getHaveZeroMessages: Ambiguous state with no rows but potential container has children or doesnt exist. Defaulting to yes.');
          result = 'yes';
        }
      } else { // messageRows.length > 0
        // Message rows were found. Now, are they actual messages or just date separators/system messages?
        let actualMessageContentFound = false;
        logNow(`DIAGNOSTIC: Processing ${messageRows.length} message rows for content detection`);

        messageRows.forEach((row, index) => {
          // A date separator might be role="row" but not contain the typical message text structure.
          // The selector 'div[dir="auto"].html-div' was from your example of a message bubble's text.
          // Also checking for typical span classes that hold text.
          const messageContent = row.querySelector('div[dir="auto"].html-div, span.x193iq5w.xeuugli.x13faqbe');
          logNow(`DIAGNOSTIC: Row ${index} message content check:`, messageContent ? 'FOUND' : 'NOT FOUND');

          // DIAGNOSTIC: Additional checks for content patterns
          const hasTextContent = row.textContent && row.textContent.trim().length > 0;
          const hasViewProfileText = row.textContent?.includes('Wyświetl profil') || row.textContent?.includes('View profile');
          const hasDateText = row.textContent?.includes('Dzisiaj') || row.textContent?.includes('Today') || /\d{1,2}:\d{2}/.test(row.textContent || '');

          logNow(`DIAGNOSTIC: Row ${index} analysis - hasTextContent: ${hasTextContent}, hasViewProfileText: ${hasViewProfileText}, hasDateText: ${hasDateText}`);

          if (messageContent) {
            actualMessageContentFound = true;
            logNow('getHaveZeroMessages: Actual message content signature found in a row:', row.outerHTML.substring(0, 100) + '...');
          }
        });
        result = actualMessageContentFound ? 'no' : 'yes';
        logNow(`getHaveZeroMessages: ${messageRows.length} rows found. actualMessageContentFound: ${actualMessageContentFound}`);
      }
    } else {
      // If we can't even find the main message grid, it's an unknown state,
      // or possibly an empty chat screen that doesn't even render the grid.
      if (window.location.href.includes('/direct/')) {
        logNow('getHaveZeroMessages: Message list grid not found, assuming empty on a DM page.');
        result = 'yes'; // Default to 'yes' (empty) if on a DM page and grid is missing
      } else {
        logNow('getHaveZeroMessages: Message list grid not found, and not on a DM page.');
        result = 'unknown';
      }
    }
    logNow(`getHaveZeroMessages: final result for ${username}: ${result}`);
    return result;
  };

  /** createGroupThread API already sends the message - no additional sending needed */

  const getClassNameString = (classnamestr: string) => {
    return `.${classnamestr.replaceAll(' ', '.')}`
  }

  async function focusAndTypeText(text: string) {
    const textbox = document.querySelector('[role="textbox"][contenteditable="true"]')

    if (!textbox) {
      console.log('Textbox not found.')
      return
    }

    // Simulate user interaction to focus the element
    textbox.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }))
    textbox.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }))
    textbox.dispatchEvent(new MouseEvent('click', { bubbles: true }))

    await new Promise((resolve) => setTimeout(resolve, 80)) // Small delay

      ; (textbox as HTMLElement).focus()

    // Simulate typing each character with random delays
    for (let char of text) {
      const eventOptions = {
        bubbles: true,
        cancelable: true,
        key: char,
        keyCode: char.charCodeAt(0),
      }

      textbox.dispatchEvent(new KeyboardEvent('keydown', eventOptions))
      textbox.dispatchEvent(new KeyboardEvent('keypress', eventOptions))

      document.execCommand('insertText', false, char) // Works well for contenteditable elements

      textbox.dispatchEvent(new InputEvent('input', { bubbles: true }))
      textbox.dispatchEvent(new KeyboardEvent('keyup', eventOptions))

      // Random delay between keystrokes (50ms - 200ms)
      await new Promise((resolve) => setTimeout(resolve, Math.random() * (200 - 50) + 50))
    }

    console.log('Typed:', text)
  }

  function findParentWithButtonsAndCheckSVGs(element: HTMLElement) {
    let current: any = element
    let foundBtns: Array<any> = []

    while (current) {
      let all_buttons = current.querySelectorAll('[role="button"]')

      if (all_buttons.length > 0) {
        // Found a parent with buttons, now check SVGs inside those buttons
        all_buttons.forEach((button: HTMLElement) => {
          let svgs = button.querySelectorAll('svg')
          if (svgs.length > 0) {
            console.log('SVGs found inside button:', svgs)
          } else {
            console.log('No SVGs inside this button:', button)
            foundBtns.push(button)
          }
        })

        break
      }

      // Move up the DOM tree
      current = current.parentElement
    }

    console.log('No parent with buttons found.')
    console.log('Found buttons with no svgs :: ', foundBtns)
    if (foundBtns.length > 0) {
      return foundBtns[0]
    }
    return null
  }

  const sendMessage = async (recipent_id: string, message_text: string) => {
    logNow('sendMessage called', { recipent_id, message_text, currentUrl: window.location.href })

    try {
      // Get viewer ID for authentication
      let viewerId = 'unknown';

      // Quick viewer ID detection from cookies
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'ds_user_id' && value) {
          viewerId = value;
          logNow('Found viewerId via cookie:', viewerId);
          break;
        }
      }

      // Extract thread ID from current URL or create new thread
      let threadId = 'unknown';
      const urlMatch = window.location.pathname.match(/\/direct\/t\/(\d+)/);
      if (urlMatch) {
        threadId = urlMatch[1];
        logNow('Found existing thread ID from URL:', threadId);
      }

      if (threadId === 'unknown') {
        logNow('No existing thread, creating new one for recipient:', recipent_id)
        const group_thread_resp = await createGroupThread(recipent_id)

        if (group_thread_resp && group_thread_resp.status === 'ok') {
          threadId = group_thread_resp.thread_id;
          logNow('New thread created:', threadId);
        } else {
          throw new Error('Failed to create thread');
        }
      } else {
        logNow('Using existing thread:', threadId);
      }

      // Send message via localStorage approach
      const messagePromise = new Promise((resolve, reject) => {
        const messageHandler = (event: MessageEvent) => {
          if (event.data && event.data.type === 'INJECT_DISPATCH_DM_RESPONSE') {
            window.removeEventListener('message', messageHandler)
            if (event.data.ret === 1) {
              logNow('Message sent successfully!')
              resolve(true)
            } else {
              logNow('Message failed:', event.data)
              reject(new Error(`Message failed: ${event.data.error || event.data.status_code}`))
            }
          }
        }

        window.addEventListener('message', messageHandler)

        // Send message to localStorage handler
        window.postMessage({
          type: 'INJECT_DISPATCH_DM_REQUEST',
          thread_id: threadId,
          viewer_id: viewerId,
          user: { id: recipent_id, username: 'target_user' },
          text: message_text,
          debug: false
        }, '*')

        // Timeout after 15 seconds
        setTimeout(() => {
          window.removeEventListener('message', messageHandler)
          reject(new Error('Message timeout'))
        }, 15000)
      })

      await messagePromise
      return true
    } catch (error) {
      logNow('Error in sendMessage:', error)
      console.error('Error in sendMessage:', error)
      return false
    }
  }

  useEffect(() => {
    Browser.runtime.onMessage.addListener(async (message: any) => {
      console.log('Message from bg :: ', message)
      if (message.type === MessageTypes.GET_INSTA_INBOX) {
        return await getInstagramStories(message.data.config)
      } else if (message.type === MessageTypes.HAVE_ZERO_MESSAGES) {
        return await getHaveZeroMessages(message.data.recipent_id, message.data.recipent_username)
      } else if (message.type === MessageTypes.MESSAGE_PROFILE) {
        return await sendMessage(message.data.recipent_id, message.data.text)
      } else if (message.type === MessageTypes.VISIT_USER_MESSAGES) {
        return await visitUserMessages(message.data.recipent_username)
      } else if (message.type === MessageTypes.START_PROCESS) {
        botRunning.current = true
        isLoopOn = true
        mimickUserActivity()
      } else if (message.type === MessageTypes.STOP_PROCESS) {
        botRunning.current = false
        isLoopOn = false
      } else if (message.type === MessageTypes.GET_PROFILE_URL) {
        return await getProfileLinKUrl()
      } else if (message.type === MessageTypes.OPEN_FOLLOWERS_PANEL) {
        return await openFollowersPanel(message.data.username)
      } else if (message.type === MessageTypes.GET_FOLLOWERS_LIST_BY_COUNT) {
        return await getFollowersByCount(message.data.followerCount)
      }
    })
  }, [])

  return <></>
}

export default Component
