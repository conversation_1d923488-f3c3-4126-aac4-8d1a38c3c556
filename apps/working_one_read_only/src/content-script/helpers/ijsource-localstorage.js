// Instagram localStorage-based DM Handler
// Based on reverse engineering findings: Instagram uses localStorage messaging queue

console.log('Loading localStorage-based Instagram DM handler');

// Prevent multiple instances
if (window.instagramLocalStorageHandler) {
    console.log('localStorage handler already loaded, skipping');
} else {
    window.instagramLocalStorageHandler = true;

    class InstagramLocalStorageMessenger {
        constructor() {
            this.initialized = false;
            this.messageQueue = new Map();
            this.originalSetItem = null;
            this.setupStorageHook();
        }

        // Hook into localStorage to monitor Instagram's messaging system
        setupStorageHook() {
            if (!this.originalSetItem) {
                this.originalSetItem = Storage.prototype.setItem;
                const self = this;

                Storage.prototype.setItem = function(key, value) {
                    // Monitor Instagram's message tracking
                    if (key === 'mw_sent_message') {
                        console.log('Instagram message sent detected:', value);
                        self.onInstagramMessageSent(value);
                    }

                    return self.originalSetItem.apply(this, [key, value]);
                };

                console.log('Storage hook installed successfully');
            }
        }

        // Called when Instagram sends a message (for monitoring)
        onInstagramMessageSent(timestamp) {
            console.log('Instagram internal message sent at:', timestamp);
        }

        // Initialize Instagram's messaging context
        async initialize() {
            try {
                console.log('Initializing Instagram localStorage messenger');

                // Get current user info from Instagram's internal state
                const instagramUser = this.getInstagramUserInfo();
                console.log('Instagram user info:', instagramUser);

                // Get current thread info
                const threadInfo = this.getCurrentThreadInfo();
                console.log('Thread info:', threadInfo);

                this.initialized = true;
                console.log('localStorage messenger initialized');

                return true;
            } catch (error) {
                console.error('Failed to initialize localStorage messenger:', error);
                return false;
            }
        }

        // Extract Instagram user info from page
        getInstagramUserInfo() {
            try {
                // Try multiple methods to get user info

                // Method 1: From cookies
                const cookies = document.cookie.split(';');
                let userId = null;
                for (const cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 'ds_user_id') {
                        userId = value;
                        break;
                    }
                }

                // Method 2: From window._sharedData
                const sharedData = window._sharedData || {};
                const configUserId = sharedData.config?.viewer?.id;

                // Method 3: From Instagram's internal store
                let storeUserId = null;
                if (window.__initialDataLoaded) {
                    storeUserId = window.__initialDataLoaded[0]?.viewer?.id;
                }

                return {
                    userId: userId || configUserId || storeUserId || 'unknown',
                    sessionId: this.generateSessionId(),
                    deviceId: this.generateDeviceId()
                };
            } catch (error) {
                console.log('Could not extract user info:', error);
                return {
                    userId: 'unknown',
                    sessionId: this.generateSessionId(),
                    deviceId: this.generateDeviceId()
                };
            }
        }

        // Get current thread information
        getCurrentThreadInfo() {
            try {
                // Extract thread ID from URL
                const urlMatch = window.location.pathname.match(/\/direct\/t\/(\d+)/);
                const threadId = urlMatch ? urlMatch[1] : null;

                // Try to get thread info from Instagram's internal state
                let threadData = null;
                if (window.__initialData) {
                    threadData = window.__initialData.data?.viewer?.direct_threads;
                }

                return {
                    threadId: threadId,
                    threadData: threadData,
                    currentUrl: window.location.href
                };
            } catch (error) {
                console.log('Could not extract thread info:', error);
                return {
                    threadId: null,
                    threadData: null,
                    currentUrl: window.location.href
                };
            }
        }

        // Generate session ID
        generateSessionId() {
            return Date.now().toString() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // Generate device ID
        generateDeviceId() {
            return 'ls_' + Math.random().toString(36).substr(2, 16);
        }

        // Main message sending function - mimics Instagram's localStorage approach
        async sendMessage(threadId, viewerId, user, text) {
            try {
                console.log(`Attempting localStorage-based message send to thread ${threadId}`);
                console.log(`Message: "${text}" to user: ${user.username} (${user.id})`);

                if (!this.initialized) {
                    await this.initialize();
                }

                // Method 1: Try to trigger Instagram's internal message sending
                const messageSuccess = await this.triggerInstagramMessageSend(threadId, text);
                if (messageSuccess) {
                    console.log('Message sent via Instagram internal system');
                    return this.sendSuccessResponse();
                }

                // Method 2: Try DOM manipulation approach
                const domSuccess = await this.sendViaDOMManipulation(text);
                if (domSuccess) {
                    console.log('Message sent via DOM manipulation');
                    return this.sendSuccessResponse();
                }

                // Method 3: Try localStorage queue injection
                const queueSuccess = await this.injectIntoMessageQueue(threadId, text);
                if (queueSuccess) {
                    console.log('Message sent via queue injection');
                    return this.sendSuccessResponse();
                }

                throw new Error('All sending methods failed');

            } catch (error) {
                console.error('Error sending message:', error);
                return this.sendErrorResponse(error);
            }
        }

        // Try to trigger Instagram's internal message sending mechanism
        async triggerInstagramMessageSend(threadId, text) {
            try {
                console.log('Attempting to trigger Instagram internal message send');

                // Find the message input element
                const messageInput = this.findMessageInput();
                if (!messageInput) {
                    console.log('Message input not found');
                    return false;
                }

                // Clear any existing text first
                this.clearInput(messageInput);
                await this.delay(50);

                // Insert text into input
                this.insertTextIntoInput(messageInput, text);

                // Wait a moment for Instagram to process
                await this.delay(200);

                // Trigger sending
                const sendTriggered = this.triggerSendAction(messageInput);
                if (sendTriggered) {
                    // Wait for Instagram's localStorage update
                    const messageProcessed = await this.waitForMessageProcessing();
                    return messageProcessed;
                }

                return false;
            } catch (error) {
                console.log('Internal trigger failed:', error);
                return false;
            }
        }

        // Find Instagram's message input element
        findMessageInput() {
            // Try multiple selectors for Instagram's message input
            const selectors = [
                'div[contenteditable="true"][role="textbox"]',
                'div[aria-label*="Message"]',
                'div[aria-label*="Wiadomość"]', // Polish
                'div[data-testid="message-input"]',
                'div.notranslate._3cqvc', // Instagram CSS class pattern
                '[placeholder*="message" i]'
            ];

            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element && element.isContentEditable) {
                    console.log('Found message input with selector:', selector);
                    return element;
                }
            }

            console.log('No message input found');
            return null;
        }

        // Clear input element
        clearInput(element) {
            try {
                element.textContent = '';
                element.innerHTML = '';

                // Trigger clear events
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));

                console.log('Input cleared');
            } catch (error) {
                console.log('Failed to clear input:', error);
            }
        }

        // Insert text into Instagram's input element
        insertTextIntoInput(element, text) {
            try {
                // Method 1: Use textContent
                element.textContent = text;

                // Method 2: If textContent doesn't work, try execCommand
                if (element.textContent !== text) {
                    element.focus();
                    document.execCommand('selectAll', false, null);
                    document.execCommand('insertText', false, text);
                }

                // Method 3: If still doesn't work, try innerHTML
                if (element.textContent !== text && !element.innerHTML.includes(text)) {
                    element.innerHTML = text;
                }

                // Trigger comprehensive input events
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
                element.dispatchEvent(new InputEvent('input', { bubbles: true, data: text }));

                console.log('Text inserted into message input:', text);
                console.log('Final input content:', element.textContent || element.innerHTML);
                return true;
            } catch (error) {
                console.log('Failed to insert text:', error);
                return false;
            }
        }

        // Trigger the send action (preventing duplication)
        triggerSendAction(messageInput) {
            try {
                let sendTriggered = false;

                // Method 1: Try Enter key first
                const enterEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true,
                    cancelable: true
                });

                const enterResult = messageInput.dispatchEvent(enterEvent);
                console.log('Enter key triggered, result:', enterResult);

                // Wait to see if Enter worked before trying button
                setTimeout(() => {
                    if (!sendTriggered) {
                        const sendButton = this.findSendButton();
                        if (sendButton) {
                            console.log('Enter fallback - clicking send button');
                            sendButton.click();
                            sendTriggered = true;
                        }
                    }
                }, 300);

                return true;
            } catch (error) {
                console.log('Failed to trigger send:', error);
                return false;
            }
        }

        // Find Instagram's send button
        findSendButton() {
            const selectors = [
                'button[type="submit"]',
                'div[role="button"][tabindex="0"]',
                'svg[aria-label*="Send" i]',
                'svg[aria-label*="Wyślij" i]' // Polish
            ];

            for (const selector of selectors) {
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {
                    const text = element.textContent?.toLowerCase() || '';
                    const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || '';

                    if (text.includes('send') || ariaLabel.includes('send') ||
                        text.includes('wyślij') || ariaLabel.includes('wyślij')) {
                        return element;
                    }
                }
            }

            return null;
        }

        // Wait for Instagram to process the message (watch localStorage)
        async waitForMessageProcessing(timeout = 5000) {
            return new Promise((resolve) => {
                const startTime = Date.now();
                const checkInterval = setInterval(() => {
                    const currentTime = Date.now();
                    const lastMessageTime = localStorage.getItem('mw_sent_message');

                    // Check if new message was sent
                    if (lastMessageTime && parseInt(lastMessageTime) > startTime) {
                        clearInterval(checkInterval);
                        console.log('Message processing detected in localStorage');
                        resolve(true);
                        return;
                    }

                    // Timeout
                    if (currentTime - startTime > timeout) {
                        clearInterval(checkInterval);
                        console.log('Message processing timeout');
                        resolve(false);
                    }
                }, 100);
            });
        }

        // Try DOM manipulation approach
        async sendViaDOMManipulation(text) {
            try {
                console.log('Attempting DOM manipulation approach');
                // This is a fallback method - would need more specific implementation
                return false;
            } catch (error) {
                console.log('DOM manipulation failed:', error);
                return false;
            }
        }

        // Try injecting into Instagram's message queue
        async injectIntoMessageQueue(threadId, text) {
            try {
                console.log('Attempting message queue injection');

                // Create fake message entry in localStorage
                const timestamp = Date.now().toString();
                localStorage.setItem('mw_sent_message', timestamp);

                // Try to trigger Instagram's message processing
                this.triggerMessageProcessing();

                return true;
            } catch (error) {
                console.log('Queue injection failed:', error);
                return false;
            }
        }

        // Trigger Instagram's message processing systems
        triggerMessageProcessing() {
            try {
                // Dispatch custom events that might trigger Instagram's systems
                window.dispatchEvent(new Event('storage'));
                window.dispatchEvent(new CustomEvent('instagramMessageSent'));

                console.log('Message processing triggers dispatched');
            } catch (error) {
                console.log('Failed to trigger processing:', error);
            }
        }

        // Utility delay function
        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Send success response
        sendSuccessResponse() {
            window.postMessage({
                type: 'INJECT_DISPATCH_DM_RESPONSE',
                ret: 1,
                status_code: 200,
                timestamp: Date.now()
            }, '*');
        }

        // Send error response
        sendErrorResponse(error) {
            window.postMessage({
                type: 'INJECT_DISPATCH_DM_RESPONSE',
                ret: 0,
                status_code: 500,
                error: error.message
            }, '*');
        }
    }

    // Create global instance
    const dmMessenger = new InstagramLocalStorageMessenger();

    // Listen for message requests
    window.addEventListener('message', async (event) => {
        if (event.data && event.data.type === 'INJECT_DISPATCH_DM_REQUEST') {
            console.log('Received DM request:', event.data);

            try {
                const { thread_id, viewer_id, user, text, debug } = event.data;

                if (debug) {
                    console.log(`[DEBUG] DM [${thread_id}] sent ${text} to ${user.username} (${user.id})`);
                    window.postMessage({
                        ...event.data,
                        type: 'INJECT_DISPATCH_DM_RESPONSE',
                        ret: 1,
                        ts: new Date().toISOString()
                    }, '*');
                    return;
                }

                // Send message via localStorage approach
                console.log('[LIVE] Processing localStorage-based message send');
                await dmMessenger.sendMessage(thread_id, viewer_id, user, text);

            } catch (error) {
                console.error('Error processing DM request:', error);
                window.postMessage({
                    type: 'INJECT_DISPATCH_DM_RESPONSE',
                    ret: 0,
                    status_code: 500,
                    error: error.message
                }, '*');
            }
        }
    });

    console.log('Instagram localStorage DM handler initialized');
}

console.log("ijsource-localstorage.js loaded successfully");