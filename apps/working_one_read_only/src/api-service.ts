/**
 * API Service for Chrome Extension
 * Handles communication with the AISetter backend API
 */

import { environment } from './config/environment';

export interface ApiKeyVerificationResult {
  success: boolean;
  data?: {
    organizationId: string;
    organizationName: string;
    organizationSlug: string;
  };
  message?: string;
}

export interface PendingFollowUp {
  id: string;
  contactId: string;
  recipientId: string;
  username: string;
  message: string;
  scheduledTime: string;
  followUpNumber: number;
}

export interface PendingFollowUpsResult {
  success: boolean;
  data?: PendingFollowUp[];
  message?: string;
}

export interface MarkFollowUpResult {
  success: boolean;
  message?: string;
}

export class ApiService {
  private static readonly BASE_URL = environment.apiBaseUrl;

  /**
   * Verify API key with the backend
   */
  static async verifyApiKey(apiKey: string): Promise<ApiKeyVerificationResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/verify-api-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey
        }
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error verifying API key:', error);
      return {
        success: false,
        message: 'Failed to connect to API server'
      };
    }
  }

  /**
   * Fetch pending follow-ups from the backend
   */
  static async fetchPendingFollowUps(apiKey: string): Promise<PendingFollowUpsResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/pending-follow-ups`, {
        method: 'GET',
        headers: {
          'X-API-Key': apiKey
        }
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error fetching pending follow-ups:', error);
      return {
        success: false,
        message: 'Failed to fetch pending follow-ups'
      };
    }
  }

  /**
   * Mark a follow-up as sent
   */
  static async markFollowUpSent(apiKey: string, followUpId: string): Promise<MarkFollowUpResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/follow-ups/${followUpId}/mark-sent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey
        }
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error marking follow-up as sent:', error);
      return {
        success: false,
        message: 'Failed to mark follow-up as sent'
      };
    }
  }

  /**
   * Mark a follow-up as failed
   */
  static async markFollowUpFailed(apiKey: string, followUpId: string, errorMessage?: string): Promise<MarkFollowUpResult> {
    try {
      // For now, we'll use the same endpoint but could create a separate one for failed status
      const response = await fetch(`${this.BASE_URL}/api/follow-ups/${followUpId}/mark-failed`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey
        },
        body: JSON.stringify({
          error: errorMessage
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error marking follow-up as failed:', error);
      return {
        success: false,
        message: 'Failed to mark follow-up as failed'
      };
    }
  }
}
