import Browser from 'webextension-polyfill'
import { GeneralConfig } from '../config'
import DataLayer from '../datalayer'
import { MessageTypes } from '../messaging'
import delay from './Utils/delay'
import { ApiService } from '../api-service'
import { FollowUpManager } from './follow-up-manager'

const heartbeatAlarmName = 'heartbeat'
const followUpAlarmName = 'followUpCheck'
let heartbeatAlreadyRunning = false
let heartbeatScheduledAlreadyRunning = false
let scheduledScanIsAlive = false
let currentDMCount = 0
let monitor_start_timestamp = Date.now()
let last_dm_time = -1
let running = false

// Progress tracking variables
let totalFollowersToProcess = 0
let remainingFollowersToProcess = 0
let nextDMTime = -1
let currentBreakLimit = 20

// Create heartbeat alarm if it doesn't exist
async function createHeartbeatAlarm() {
  const alarms = await Browser.alarms.getAll()
  const alarmExists = alarms.some((alarm) => alarm.name === heartbeatAlarmName)

  if (!alarmExists) {
    Browser.alarms.create(heartbeatAlarmName, {
      periodInMinutes: 0.33, // Run every minute
    })
  }
}

// Create follow-up check alarm if it doesn't exist
async function createFollowUpAlarm() {
  const alarms = await Browser.alarms.getAll()
  const alarmExists = alarms.some((alarm) => alarm.name === followUpAlarmName)

  if (!alarmExists) {
    Browser.alarms.create(followUpAlarmName, {
      periodInMinutes: 1, // Check every 1 minute for follow-ups (more responsive for auto-DMing)
    })
  }
}

async function setStatus(text: string) {
  let config: any = await DataLayer.getConfig()
  const updatedConfig = {
    ...config,
    status: text,
  }
  await DataLayer.setConfig(updatedConfig)
}

// Create alarms on startup
createHeartbeatAlarm()
createFollowUpAlarm()

const getMainTab = async (activateTab: boolean = false) => {
  const mainTabId = await DataLayer.getMainTabId()
  let tab = null
  if (mainTabId) {
    try {
      tab = await Browser.tabs.get(parseInt(mainTabId))
      console.log('Main Tab :: ', tab)
      if (tab) {
        await delay(1000)

        try {
          // console.log("Sending debug signals..")
          // chrome.debugger.attach({ tabId: tab.id }, "1.3", () => {
          //   chrome.debugger.sendCommand({ tabId: tab.id }, "Page.enable", {}, () => {
          //     chrome.debugger.sendCommand({ tabId: tab.id }, "Page.reload"); // Forces full rendering
          //   });
          // });

          // await delay(2000);

          console.log("NOT Sending debug signals end..")
        } catch (error) {
          console.log("err while attaching debugger :: ", error);
        }
      } else {
        tab = await Browser.tabs.create({
          url: 'https://www.instagram.com/direct/inbox/#/',
          active: false,
          // pinned: true,
          // discarded: false,
          // autoDiscardable: false,
        })
        await DataLayer.setMainTabId(tab.id!.toString())

        await delay(1000)

        try {
          console.log('Sending debug signals..')
          chrome.debugger.attach({ tabId: tab.id }, '1.3', () => {
            chrome.debugger.sendCommand({ tabId: tab.id }, 'Page.enable', {}, () => {
              chrome.debugger.sendCommand({ tabId: tab.id }, 'Page.reload') // Forces full rendering
            })
          })

          await delay(2000)

          console.log('Sending debug signals end..')
        } catch (error) {
          console.log('err while attaching debugger :: ', error)
        }
      }
    } catch (error) {
      tab = await Browser.tabs.create({
        url: 'https://www.instagram.com/direct/inbox/#/',
        active: false,
        // pinned: true,
        // discarded: false,
        // autoDiscardable: false,
      })
      await DataLayer.setMainTabId(tab.id!.toString())

      await delay(1000)

      try {
        console.log('Sending debug signals..')
        chrome.debugger.attach({ tabId: tab.id }, '1.3', () => {
          chrome.debugger.sendCommand({ tabId: tab.id }, 'Page.enable', {}, () => {
            chrome.debugger.sendCommand({ tabId: tab.id }, 'Page.reload') // Forces full rendering
          })
        })

        await delay(2000)

        console.log('Sending debug signals end..')
      } catch (error) {
        console.log('err while attaching debugger :: ', error)
      }
    }
  } else {
    tab = await Browser.tabs.create({
      url: 'https://www.instagram.com/direct/inbox/#/',
      active: false,
      // pinned: true,
      // discarded: false,
      // autoDiscardable: false,
    })
    await DataLayer.setMainTabId(tab.id!.toString())

    await delay(1000)

    try {
      console.log('Sending debug signals..')
      chrome.debugger.attach({ tabId: tab.id }, '1.3', () => {
        chrome.debugger.sendCommand({ tabId: tab.id }, 'Page.enable', {}, () => {
          chrome.debugger.sendCommand({ tabId: tab.id }, 'Page.reload') // Forces full rendering
        })
      })

      await delay(2000)

      console.log('Sending debug signals end..')
    } catch (error) {
      console.log('err while attaching debugger :: ', error)
    }
  }

  // if (activateTab && tab.windowId) {
  console.log('Activating tab :: ', tab.windowId)
  await Browser.tabs.update(tab.id!, { autoDiscardable: false })
  // }

  await delay(2000)

  return tab
}

const getSecTab = async (activateTab: boolean = false) => {
  const mainTabId = await DataLayer.getSecTabId()
  let tab = null
  if (mainTabId) {
    try {
      tab = await Browser.tabs.get(parseInt(mainTabId))
      console.log('Main Tab :: ', tab)
      if (tab) {
      } else {
        tab = await Browser.tabs.create({
          url: 'https://www.instagram.com/',
          active: false,
          // pinned: true,
          // autoDiscardable: false,
        })
        await DataLayer.setSecTabId(tab.id!.toString())
      }
    } catch (error) {
      tab = await Browser.tabs.create({
        url: 'https://www.instagram.com/',
        active: false,
        // pinned: true,
        // autoDiscardable: false,
      })
      await DataLayer.setSecTabId(tab.id!.toString())
    }
  } else {
    tab = await Browser.tabs.create({
      url: 'https://www.instagram.com/',
      active: false,
      // pinned: true,
      // autoDiscardable: false,
    })
    await DataLayer.setSecTabId(tab.id!.toString())
  }

  // if (activateTab && tab.windowId) {
  console.log('Activating tab :: ', tab.windowId)
  await Browser.tabs.update(tab.id!, { autoDiscardable: false })
  // }

  await delay(2000)

  return tab
}

async function waitForTabToLoad(tab: Browser.Tabs.Tab) {
  if (!tab || !tab.id) {
    throw new Error('Invalid tab object.')
  }

  const tabInfo = await Browser.tabs.get(tab.id)
  if (tabInfo.status === 'complete') {
    return tab
  }

  await new Promise((resolve) => {
    const listener = (tabId: any, changeInfo: any) => {
      if (tabId === tab.id && changeInfo.status === 'complete') {
        Browser.tabs.onUpdated.removeListener(listener)
        resolve(true)
      }
    }
    Browser.tabs.onUpdated.addListener(listener)
  })

  return tab
}

interface InstaStory {
  id: string
  username: string
  profile_image: string
  type: number
  timestamp: number
}

function hasInstagramUsername(url: string) {
  const pattern = /^https:\/\/www\.instagram\.com\/([^\/]+)\/$/
  return pattern.test(url)
}

function isProfileUrl(url: string, username: string) {
  if (url.includes(username.replaceAll('/', ''))) {
    return true
  }
  return false
}

function extractUsername(instagramUrl: string) {
  try {
    let url = new URL(instagramUrl)
    return url.pathname.split('/')[1] // Extracts the username from the URL path
  } catch (error) {
    console.error('Invalid URL:', error)
    return null
  }
}

const toInstaStoriesFormat = (insta_links: Array<string>) => {
  const arr: Array<InstaStory> = []
  insta_links.map((i) => {
    let uname = extractUsername(i) || ''
    arr.push({
      id: uname,
      username: uname,
      timestamp: -1,
      profile_image: '',
      type: 3,
    })
  })
  return arr
}

const getInstaInbox = async (tab: Browser.Tabs.Tab, conf: GeneralConfig) => {
  if (conf.dmPastFollowers) {
    const sectab = await getSecTab()
    const profile_link = await Browser.tabs.sendMessage(sectab.id!, {
      type: MessageTypes.GET_PROFILE_URL,
    })

    if (!profile_link) {
      console.log("ERROR : couldn't find profile link.")
    }

    // first check if it is already on profile page or not!
    let is_profile_url = await isProfileUrl(sectab.url!, profile_link)

    if (!is_profile_url) {
      let open_profile_link_result = await Browser.tabs.update(sectab.id!, {
        url: `https://instagram.com/${profile_link.replaceAll('/', '')}`,
      })
      await waitForTabToLoad(open_profile_link_result)
      await delay(2000)
    }

    let open_followers_result = await Browser.tabs.sendMessage(sectab.id!, {
      type: MessageTypes.OPEN_FOLLOWERS_PANEL,
      data: {
        username: profile_link.replaceAll('/', ''),
      },
    })

    if (open_followers_result.status !== 'success') {
      console.log('error :: ', open_followers_result)
    }

    let get_followers_list_by_count = await Browser.tabs.sendMessage(sectab.id!, {
      type: MessageTypes.GET_FOLLOWERS_LIST_BY_COUNT,
      data: {
        followerCount: conf.followerAge,
      },
    })

    const insta_stories_format = toInstaStoriesFormat(get_followers_list_by_count)

    return insta_stories_format
  } else {
    const result = await Browser.tabs.sendMessage(tab.id!, {
      type: MessageTypes.GET_INSTA_INBOX,
      data: {
        config: conf,
      },
    })
    console.log('response reading insta inbox :: ', result)
    if (result.status && result.status === 'success') {
      return result.data as Array<InstaStory>
    }
    return []
  }
}

const runScheduledScan = async () => {
  ////// don't go if scheduledscanisalive already true

  if (scheduledScanIsAlive) {
    console.log('Scheduled scan is already alive!!!')
    return
  }

  scheduledScanIsAlive = true
  currentDMCount = 0

  let config = (await DataLayer.getConfig()) as GeneralConfig
  console.log('🚨 DEBUG: Full configuration loaded in runScheduledScan:')
  console.log('🚨 DEBUG: Config:', JSON.stringify(config, null, 2))
  console.log('🚨 DEBUG: dmPastFollowers enabled:', config.dmPastFollowers)
  console.log('🚨 DEBUG: pastFollowerTimeBetweenDM:', config.pastFollowerTimeBetweenDM)

  // Initialize progress tracking
  currentBreakLimit = Math.floor(Math.random() * (config.dmCount[1] - config.dmCount[0] + 1)) + config.dmCount[0]

  const addUserToDMPool = async (profile_id: string) => {
    let config: any = await DataLayer.getConfig()
    if (!(config.dmpool || []).includes(profile_id)) {
      const updatedConfig = {
        ...config,
        dmpool: [...(config.dmpool || []), profile_id],
      }
      await DataLayer.setConfig(updatedConfig)
    }
  }

  const addUserToHaveZeroMessagesPool = async (profile_id: string) => {
    let config: any = await DataLayer.getConfig()
    if (!(config.zero_message_cache || []).includes(profile_id)) {
      const updatedConfig = {
        ...config,
        zero_message_cache: [...(config.zero_message_cache || []), profile_id],
      }
      console.log('not adding peoples to zero message cache pool!')
      await DataLayer.setConfig(updatedConfig)
    }
  }

  const writeMessage = async (message: string, story: InstaStory, tab: Browser.Tabs.Tab, msg_line: Number = 0) => {
    console.log(`🚨 DEBUG: writeMessage called - using API approach`, { message, story, tabId: tab.id, msg_line })
    await setStatus(`Sending "${message}" to ${story.id}.`)

    // With API approach, we don't need to navigate to profile pages or visit user messages
    // We can send messages directly via the createGroupThread + WebSocket flow
    console.log(`🚨 DEBUG: Sending message directly via API to ${story.username} (${story.id})`)

    const r = await Browser.tabs.sendMessage(tab.id!, {
      type: MessageTypes.MESSAGE_PROFILE,
      data: {
        recipent_id: story.id,
        text: message,
      },
    })
    console.log('🚨 DEBUG: MESSAGE_PROFILE response:', r)
    return r
  }

  const getHaveZeroMessages = async (
    conf: GeneralConfig,
    story: InstaStory,
    tab: Browser.Tabs.Tab,
  ) => {
    const isPastFollower = story.timestamp === -1
    console.log(`🚨 DEBUG: getHaveZeroMessages called for ${isPastFollower ? 'Past' : 'New'} follower: ${story.username}`)

    const url = `https://www.instagram.com/${story.username}/`
    console.log(`🚨 DEBUG: Navigating to profile URL: ${url}`)

    let updated_tab = await Browser.tabs.update(tab.id!, {
      url: url,
    })
    console.log('🚨 DEBUG: Updated tab URL, waiting for load...')
    await waitForTabToLoad(updated_tab)
    console.log('🚨 DEBUG: Tab loaded, waiting 4 seconds...')
    await delay(4000)

    let times_tried = 0
    const maxAttempts = 3
    console.log(`🚨 DEBUG: Starting message visitor loop, max attempts: ${maxAttempts}`)

    while (times_tried < maxAttempts) {
      if (!running) {
        console.log('🚨 DEBUG: Process cancelled during zero message check')
        break
      }

      console.log(`🚨 DEBUG: Attempt ${times_tried + 1}/${maxAttempts} - Sending VISIT_USER_MESSAGES`)
      const visitStartTime = Date.now()

      updated_tab = await Browser.tabs.sendMessage(tab.id!, {
        type: MessageTypes.VISIT_USER_MESSAGES,
        data: {
          recipent_id: story.id,
          recipent_username: story.username,
        },
      })

      const visitEndTime = Date.now()
      console.log(`🚨 DEBUG: VISIT_USER_MESSAGES completed in ${visitEndTime - visitStartTime}ms`)
      console.log(`🚨 DEBUG: VISIT_USER_MESSAGES response:`, updated_tab)

      let t = await Browser.tabs.get(tab.id!)
      console.log(`🚨 DEBUG: Current tab URL after visit: ${t.url}`)

      console.log('🚨 DEBUG: Waiting 5 seconds for navigation...')
      await delay(5000)
      console.log('🚨 DEBUG: Done waiting, checking URL...')

      // Check if the updated tab URL includes '/direct'
      const tabInfo = await Browser.tabs.get(tab.id!)
      console.log(`🚨 DEBUG: Final tab URL: ${tabInfo.url}`)

      if (tabInfo.url && tabInfo.url.includes('/direct')) {
        console.log(`🚨 DEBUG: Successfully navigated to direct messages!`)
        break // Exit the loop if the condition is met
      } else {
        console.log(`🚨 DEBUG: Navigation failed, URL doesn't contain '/direct'. Retrying...`)
        console.log(`🚨 DEBUG: Navigating back to profile: ${url}`)
        updated_tab = await Browser.tabs.update(tab.id!, {
          url: url,
        })
        await waitForTabToLoad(t)
        console.log(`🚨 DEBUG: Back to profile, ready for next attempt`)
      }

      times_tried++
      console.log(`🚨 DEBUG: Attempt ${times_tried} completed`)
    }

    if (!running) {
      console.log('🚨 DEBUG: Process cancelled, returning false')
      return false
    }

    if (times_tried >= maxAttempts) {
      console.log(`🚨 DEBUG: All ${maxAttempts} attempts failed to navigate to direct messages`)
      return 'no' // Assume they have messages if we can't check
    }

    console.log('🚨 DEBUG: Waiting 8 seconds before checking zero messages...')
    await delay(8000)

    console.log('🚨 DEBUG: Sending HAVE_ZERO_MESSAGES check...')
    const zeroCheckStartTime = Date.now()

    const result = await Browser.tabs.sendMessage(tab.id!, {
      type: MessageTypes.HAVE_ZERO_MESSAGES,
      data: {
        recipent_id: story.id,
        recipent_username: story.username,
      },
    })

    const zeroCheckEndTime = Date.now()
    console.log(`🚨 DEBUG: HAVE_ZERO_MESSAGES completed in ${zeroCheckEndTime - zeroCheckStartTime}ms`)
    console.log(`🚨 DEBUG: HAVE_ZERO_MESSAGES result:`, result)

    await delay(4000)

    console.log(`🚨 DEBUG: getHaveZeroMessages returning: ${result}`)
    return result
  }

  const messageUser = async (conf: GeneralConfig, story: InstaStory, tab: Browser.Tabs.Tab) => {
    if (!running) {
      console.log('Cancelled messaging!!')
      return
    }

    // Determine if this is a past follower (timestamp === -1) or new follower
    const isPastFollower = story.timestamp === -1

    console.log(`🚨 DEBUG: Processing user ${story.username} (${story.id})`)
    console.log(`🚨 DEBUG: Story timestamp: ${story.timestamp}`)
    console.log(`🚨 DEBUG: Is past follower: ${isPastFollower}`)
    console.log(`🚨 DEBUG: Configuration values:`)
    console.log(`🚨 DEBUG: - conf.pastFollowerTimeBetweenDM:`, conf.pastFollowerTimeBetweenDM)
    console.log(`🚨 DEBUG: - conf.timeBetweenDM:`, conf.timeBetweenDM)
    console.log(`🚨 DEBUG: - conf.pastFollowerDMCount:`, conf.pastFollowerDMCount)
    console.log(`🚨 DEBUG: - conf.dmCount:`, conf.dmCount)
    console.log(`🚨 DEBUG: - conf.pastFollowerBreakTime:`, conf.pastFollowerBreakTime)
    console.log(`🚨 DEBUG: - conf.dmBreakTime:`, conf.dmBreakTime)

    // Use appropriate settings based on follower type
    const dmCountRange = isPastFollower ? conf.pastFollowerDMCount : conf.dmCount
    const breakTimeRange = isPastFollower ? conf.pastFollowerBreakTime : conf.dmBreakTime
    const timeBetweenDMRange = isPastFollower ? conf.pastFollowerTimeBetweenDM : conf.timeBetweenDM

    console.log(`🚨 DEBUG: Selected ranges for ${isPastFollower ? 'Past' : 'New'} follower:`)
    console.log(`🚨 DEBUG: - dmCountRange:`, dmCountRange)
    console.log(`🚨 DEBUG: - breakTimeRange:`, breakTimeRange)
    console.log(`🚨 DEBUG: - timeBetweenDMRange:`, timeBetweenDMRange)

    let randomBreakCount =
      Math.floor(Math.random() * (dmCountRange[1] - dmCountRange[0] + 1)) + dmCountRange[0]

    if (currentDMCount >= randomBreakCount) {
      console.log(`It hitted dm count, so waiting for break time! (${isPastFollower ? 'Past' : 'New'} follower)`)
      let minWait = breakTimeRange[0] * 60 * 1000
      let maxWait = breakTimeRange[1] * 60 * 1000
      let randomDMBreakWait = Math.floor(Math.random() * (maxWait - minWait + 1)) + minWait
      await setStatus(
        `Waiting for ${Math.floor(
          randomDMBreakWait / 1000 / 60,
        )} mins after ${currentDMCount} DMs (${isPastFollower ? 'Past' : 'New'} follower).`,
      )
      await delay(randomDMBreakWait)
      currentDMCount = 0 // reset the dm count!
    } else {
      // await setStatus(`Waiting for ${Math.floor(randomWait/1000/60)} mins.`);
      // console.log(`Waiting for ${randomWait / 1000} seconds for the waittime before DM!...`);
      // await delay(randomWait);
    }

    if (conf.onlyZeroMessages) {
      if (!running) {
        return
      }

      console.log(`🚨 DEBUG: onlyZeroMessages check for ${isPastFollower ? 'Past' : 'New'} follower: ${story.username}`)

      /** Now check if it have only 0 messages or not! */
      await setStatus(`Checking if ${story.username} (${story.id}) have 0 messages.`)
      console.log(`🚨 DEBUG: Calling getHaveZeroMessages for ${isPastFollower ? 'Past' : 'New'} follower`)

      let have_only_0_messages = await getHaveZeroMessages(conf, story, tab)

      console.log('🚨 DEBUG: have 0 message result :: ', have_only_0_messages)

      if (have_only_0_messages === 'no') {
        console.log(
          `Declined to send message because user don't have 0 messages, adding to the pool!`,
        )
        if (!running) {
          console.log('Cancelled.')
          return
        }
        await addUserToHaveZeroMessagesPool(story.id)
        return
      }
    }

    if (last_dm_time !== -1) {
      console.log(`🚨 DEBUG: timeBetweenDM calculation for ${isPastFollower ? 'Past' : 'New'} follower`)
      console.log(`🚨 DEBUG: timeBetweenDMRange:`, timeBetweenDMRange)
      console.log(`🚨 DEBUG: Raw range values: [${timeBetweenDMRange[0]}, ${timeBetweenDMRange[1]}] minutes`)

      let minWait = timeBetweenDMRange[0] * 60 * 1000
      let maxWait = timeBetweenDMRange[1] * 60 * 1000
      console.log(`🚨 DEBUG: Converted to milliseconds: minWait=${minWait}ms, maxWait=${maxWait}ms`)

      let randomWait = Math.floor(Math.random() * (maxWait - minWait + 1)) + minWait
      console.log(`🚨 DEBUG: Generated randomWait: ${randomWait}ms (${randomWait / 1000}s, ${randomWait / 1000 / 60}mins)`)

      let now = Date.now()
      let elapsedTime = now - last_dm_time
      let remainingWait = randomWait - elapsedTime

      console.log(`🚨 DEBUG: Time calculations:`)
      console.log(`🚨 DEBUG: - Current time: ${now}`)
      console.log(`🚨 DEBUG: - Last DM time: ${last_dm_time}`)
      console.log(`🚨 DEBUG: - Elapsed time: ${elapsedTime}ms (${elapsedTime / 1000}s)`)
      console.log(`🚨 DEBUG: - Remaining wait: ${remainingWait}ms (${remainingWait / 1000}s, ${remainingWait / 1000 / 60}mins)`)

      // Validate remaining wait time - if it's negative or unreasonably large, fix it
      const maxReasonableWait = isPastFollower ? 5 * 60 * 1000 : 60 * 60 * 1000 // 5 mins for past followers, 1 hour for new followers

      if (remainingWait < 0) {
        console.log(`🚨 DEBUG: Remaining wait is negative (${remainingWait}ms), setting to 0`)
        remainingWait = 0
      } else if (remainingWait > maxReasonableWait) {
        console.log(`🚨 DEBUG: Remaining wait is unreasonably large (${remainingWait / 1000 / 60} minutes), capping to ${maxReasonableWait / 1000 / 60} minutes for ${isPastFollower ? 'Past' : 'New'} follower`)
        remainingWait = maxWait // Use the configured max wait instead of the unreasonable calculated value
      }

      console.log(`🚨 DEBUG: Final remaining wait: ${remainingWait}ms (${remainingWait / 1000}s, ${remainingWait / 1000 / 60}mins)`)
      console.log(`Waiting for ${remainingWait} milliseconds because of timeBetweenDM (${isPastFollower ? 'Past' : 'New'} follower)`)

      // Set next DM time for progress tracking
      nextDMTime = Math.floor(remainingWait / 1000)

      await setStatus(`Waiting for ${Math.floor(remainingWait / 1000 / 60)} mins (${isPastFollower ? 'Past' : 'New'} follower).`)
      await delay(remainingWait)

      // Clear next DM time after wait
      nextDMTime = -1
    }

    if (!running) {
      console.log('Cancelled messaging!!')
      return
    }

    console.log('Sending final message to user!!')
    let messagesIdx = Math.floor(Math.random() * conf.messages.length)

    let randomMessage = conf.messages[messagesIdx]
    console.log(`Random message :: `, randomMessage)

    let multiple_messages = randomMessage.split('\n')


    /** wait for new follows according to timestamp, only applicable for new followers */
    /** wait for new follows according to timestamp, only applicable for new followers */
    if (story.timestamp !== -1) {
      console.log("story timestamp isn't -1, checking wait time");

      let currentTime = Date.now(); // Current time in milliseconds
      let storyTime = story.timestamp * 1000; // Convert to milliseconds

      let minWait = conf.waitTime[0] * 60 * 1000;
      let maxWait = conf.waitTime[1] * 60 * 1000;
      let randomWait = Math.floor(Math.random() * (maxWait - minWait + 1)) + minWait;

      let elapsedTime = currentTime - storyTime;
      let remainingTime = randomWait - elapsedTime;

      if (remainingTime > 0) {
        await setStatus(`Waiting for ${Math.floor(remainingTime / 1000 / 60)} mins.`);
        await delay(remainingTime);
      }
    }



    for (let message_lines = 0; message_lines < multiple_messages.length; message_lines++) {
      if (!running) {
        break
      }

      const single_message = multiple_messages[message_lines]
      if (single_message && (single_message || '').trim().length > 0) {
        await writeMessage(single_message.trim(), story, tab, message_lines)
        await addUserToDMPool(story.id)
        await addUserToDMPool(story.username)

        // Wait between message lines using configurable delay
        let minWait = conf.messageLineDelay[0] * 1000
        let maxWait = conf.messageLineDelay[1] * 1000
        let randomWaitMs = Math.floor(Math.random() * (maxWait - minWait + 1)) + minWait

        await setStatus(`Waiting for ${Math.floor(randomWaitMs / 1000)} seconds between message lines.`)

        await delay(randomWaitMs)
      }
    }

    if (running) {
      currentDMCount += 1
      const previousLastDmTime = last_dm_time
      last_dm_time = Date.now()
      console.log(`🚨 DEBUG: Updated DM tracking:`)
      console.log(`🚨 DEBUG: - Previous last_dm_time: ${previousLastDmTime}`)
      console.log(`🚨 DEBUG: - New last_dm_time: ${last_dm_time}`)
      console.log(`🚨 DEBUG: - Current DM count: ${currentDMCount}`)
      // update config each time!!
      config = (await DataLayer.getConfig()) as GeneralConfig
    }

    await setStatus('')
  }

  try {
    let tab = await getMainTab()
    console.log('Got main tab:', tab)

    // fake useer interaction!!!
    await Browser.tabs.sendMessage(tab.id!, {
      type: MessageTypes.START_PROCESS,
      data: {},
    })

    // const randomDelay = Math.floor(Math.random() * (180000 - 60000 + 1)) + 60000;
    // console.log(`[${new Date().toISOString()}] Waiting ${randomDelay}ms (${randomDelay/1000}s) before proceeding`);
    // await setStatus(`Doing break for ${(Math.floor(randomDelay/1000)/60)} mins`);
    // await delay(randomDelay);

    // Past followers are DMed immediately (no initial wait time needed)

    await setStatus(`Searching instagram inboxes`)
    const insta_stories = await getInstaInbox(tab, config as any)
    console.log('insta stories :: ', insta_stories)

    // Initialize follower tracking
    totalFollowersToProcess = insta_stories.length
    remainingFollowersToProcess = insta_stories.length

    // let range_for_notification_timestamp = [
    //   config.followerAge[0] * 24 * 60 * 60 * 1000,
    //   config.followerAge[1] * 24 * 60 * 60 * 1000,
    // ]

    let processedUsers = 0
    let skippedUsers = 0

    for (let story_idx = 0; story_idx < insta_stories.length; story_idx++) {
      if (!running) {
        break
      }

      const insta_story = insta_stories[story_idx]
      console.log(`🚨 DEBUG: Processing user ${story_idx + 1}/${insta_stories.length}: ${insta_story.id}`)
      // ms to sec
      let story_timestamp = insta_story.timestamp * 1000

      if (!config.dmPastFollowers) {
        if (story_timestamp < monitor_start_timestamp) {
          console.log('🚨 DEBUG: Not qualified because it is a past follower!!')
          skippedUsers++
          continue
        }
      }

      if (
        ((config.dmpool || []).includes(insta_story.id) ||
          (config.dmpool || []).includes(insta_story.username)) &&
        insta_story.id !== ''
      ) {
        console.log('🚨 DEBUG: User has been already messaged, skipping!!')
        skippedUsers++
        continue
      }

      if (config.onlyZeroMessages) {
        if (
          ((config.zero_message_cache || []).includes(insta_story.id) ||
            (config.zero_message_cache || []).includes(insta_story.username)) &&
          insta_story.id !== ''
        ) {
          console.log('🚨 DEBUG: User has not 0 messages, skipping!!')
          skippedUsers++
          continue
        }
      }

      if (!running) {
        break
      }

      console.log(`🚨 DEBUG: User ${insta_story.id} passed all filters, proceeding to message`)
      await messageUser(config, insta_story, tab)
      processedUsers++

      // Update remaining followers count
      remainingFollowersToProcess--
    }

    console.log(`🚨 DEBUG: Scan completed - Processed: ${processedUsers}, Skipped: ${skippedUsers}, Total: ${insta_stories.length}`)

    // If all users were skipped, stop the process to prevent infinite loop
    if (processedUsers === 0 && skippedUsers > 0) {
      console.log('🚨 DEBUG: All users were skipped - stopping process to prevent infinite loop')
      running = false
      await setStatus('All available users have been processed or skipped')

      // Update config to stop the process
      let config = await DataLayer.getConfig()
      await DataLayer.setConfig({
        ...config,
        power: false,
        status: 'All available users processed - Bot stopped'
      })
    }
  } catch (error) {
    console.log('Error in runScheduledScan :: ', error)
  }

  scheduledScanIsAlive = false
}

const setupScheduledScan = async () => {
  if (!running) {
    return
  }

  // Check if API key is valid before allowing any bot functionality
  const apiKeyResult = await Browser.storage.local.get(['apiKey', 'apiStatus'])
  const apiKey = apiKeyResult.apiKey
  const apiStatus = apiKeyResult.apiStatus

  if (!apiKey) {
    console.log('🚨 Bot stopped: No API key configured')
    running = false
    await setStatus('❌ No API key - Extension disabled')
    return
  }

  // Check if API status exists and is valid (not older than 1 hour)
  const isApiStatusValid = apiStatus &&
    apiStatus.isAuthenticated &&
    apiStatus.lastVerified &&
    (new Date().getTime() - new Date(apiStatus.lastVerified).getTime()) < 3600000 // 1 hour

  if (!isApiStatusValid) {
    console.log('🚨 Bot stopped: API key verification required')
    try {
      const verificationResult = await ApiService.verifyApiKey(apiKey)
      if (!verificationResult.success) {
        console.log('🚨 Bot stopped: Invalid API key')
        running = false
        await setStatus('❌ Invalid API key - Extension disabled')
        return
      }
      // Update API status in storage
      const newApiStatus = {
        isAuthenticated: true,
        organizationName: verificationResult.data?.organizationName,
        organizationSlug: verificationResult.data?.organizationSlug,
        lastVerified: new Date().toISOString(),
        error: undefined
      }
      await Browser.storage.local.set({ apiStatus: newApiStatus })
    } catch (error) {
      console.error('🚨 Bot stopped: API key verification failed', error)
      running = false
      await setStatus('❌ API verification failed - Extension disabled')
      return
    }
  }

  const config = await DataLayer.getConfig()
  if (config.power) {
    running = true
    await runScheduledScan()
  } else {
    running = false
  }
}

const runScheduledScanHeartbeat = async () => {
  if (heartbeatScheduledAlreadyRunning) {
    return
  }

  heartbeatScheduledAlreadyRunning = true

  try {
    await setupScheduledScan()
  } catch (error) {
    console.log('Error in heartbeat [scheduled] :: ', error)
  }

  heartbeatScheduledAlreadyRunning = false
}

const sendStopMessage = async () => {
  const main_tab = await getMainTab()
  await Browser.tabs.sendMessage(main_tab.id!, {
    type: MessageTypes.STOP_PROCESS,
  })
}

// Process external follow-ups (beyond 24-hour window) with auto-DMing
const processExternalFollowUps = async () => {
  console.log('🚨 DEBUG: processExternalFollowUps called - delegating to FollowUpManager')
  const followUpManager = FollowUpManager.getInstance()
  await followUpManager.processFollowUps()
}

Browser.runtime.onMessage.addListener(async (message, sender) => {
  console.log('Bg :: Message :: ', message)

  await createHeartbeatAlarm()

  if (message.type === MessageTypes.TEST_TAB_BUTTON) {
    console.log('Bg :: TEST_TAB_BUTTON :: ', message)
    setTimeout(async () => {
      let tab = await getMainTab()
      if (tab && tab.id) {
        let r = await Browser.tabs.sendMessage(tab.id, {
          type: MessageTypes.TEST_TAB_BUTTON,
        })
        console.log('Bg :: TEST_TAB_BUTTON :: ', r)
        Browser.storage.local.set({
          test_tab_button: r,
        })
      }
    }, 1000)
    return true
  } else if (message.type === MessageTypes.START_BOT) {
    console.log('Bg :: START_BOT :: ', message)
    let config = await DataLayer.getConfig()
    await DataLayer.setConfig({
      ...config,
      ...message.data,
    })
    return true
  } else if (message.type === MessageTypes.STOP_PROCESS) {
    console.log('Bg :: STOP_PROCESS :: ', message)
    let config = await DataLayer.getConfig()
    await DataLayer.setConfig({
      ...config,
      power: false,
      status: '',
    })
    running = false
    await sendStopMessage()
    return true
  } else if (message.type === MessageTypes.START_PROCESS) {
    console.log('Bg :: START_PROCESS :: ', message)
    monitor_start_timestamp = Date.now()
    let config = await DataLayer.getConfig()
    await DataLayer.setConfig({
      ...config,
      power: true,
    })
    running = true
    return true
  } else if (message.type === MessageTypes.SAVE_SETTINGS) {
    console.log('Bg :: SAVE_SETTINGS :: ', message)
    let config = await DataLayer.getConfig()
    await DataLayer.setConfig({
      ...config,
      ...message.data,
    })
    return true
  } else if (message.type === MessageTypes.GET_SETTINGS) {
    console.log('Bg :: GET_SETTINGS :: ', message)
    let config = await DataLayer.getConfig()
    return config
  } else if (message.type === MessageTypes.GET_COOKIE) {
    console.log('Bg :: GET_COOKIE :: ', message)
    let cookie = await Browser.cookies.get({ url: message.data.url, name: message.data.name })
    console.log('Bg :: GET_COOKIE :: ', cookie)
    return cookie?.value
  } else if (message.type === MessageTypes.GET_STATUS) {
    console.log('Bg :: GET_STATUS :: ', message)
    let config = await DataLayer.getConfig()
    return {
      ...config,
      currentDMCount: currentDMCount,
      dmBreakLimit: currentBreakLimit,
      timeUntilNextDM: nextDMTime > 0 ? nextDMTime : 0,
      remainingFollowers: remainingFollowersToProcess,
      totalFollowers: totalFollowersToProcess,
    }
  } else if (message.type === MessageTypes.RESET_DM_POOL) {
    let config = await DataLayer.getConfig()
    await DataLayer.setConfig({
      ...config,
      ['dmpool']: [],
    })
  } else if (message.type === MessageTypes.RESET_NONZERO_CACHE) {
    let config = await DataLayer.getConfig()
    await DataLayer.setConfig({
      ...config,
      ['zero_message_cache']: [],
    })
  } else if (message.type === MessageTypes.SAVE_API_KEY) {
    console.log('Bg :: SAVE_API_KEY :: ', message)
    await Browser.storage.local.set({ apiKey: message.data.apiKey })
    return true
  } else if (message.type === MessageTypes.GET_API_KEY) {
    console.log('Bg :: GET_API_KEY :: ', message)
    const result = await Browser.storage.local.get(['apiKey'])
    return result.apiKey || ''
  } else if (message.type === MessageTypes.VERIFY_API_KEY) {
    console.log('Bg :: VERIFY_API_KEY :: ', message)
    try {
      const result = await ApiService.verifyApiKey(message.data.apiKey)
      const apiStatus = {
        isAuthenticated: result.success,
        organizationName: result.data?.organizationName,
        organizationSlug: result.data?.organizationSlug,
        lastVerified: new Date().toISOString(),
        error: result.success ? undefined : result.message
      }
      await Browser.storage.local.set({ apiStatus })
      return apiStatus
    } catch (error) {
      console.error('Error verifying API key:', error)
      const apiStatus = {
        isAuthenticated: false,
        error: 'Failed to verify API key'
      }
      await Browser.storage.local.set({ apiStatus })
      return apiStatus
    }
  } else if (message.type === MessageTypes.GET_API_STATUS) {
    console.log('Bg :: GET_API_STATUS :: ', message)
    const result = await Browser.storage.local.get(['apiStatus'])
    return result.apiStatus || { isAuthenticated: false }
  } else if (message.type === MessageTypes.SET_AUTO_DM_ENABLED) {
    console.log('🚨 DEBUG: Bg :: SET_AUTO_DM_ENABLED :: ', message)
    await Browser.storage.local.set({ autoDMEnabled: message.data.enabled })
    return { success: true }
  } else if (message.type === MessageTypes.GET_AUTO_DM_STATUS) {
    console.log('🚨 DEBUG: Bg :: GET_AUTO_DM_STATUS :: ', message)
    const result = await Browser.storage.local.get(['autoDMEnabled'])
    return { enabled: result.autoDMEnabled !== false } // Default to true if not set
  } else if (message.type === MessageTypes.FETCH_PENDING_FOLLOWUPS) {
    console.log('Bg :: FETCH_PENDING_FOLLOWUPS :: ', message)
    try {
      const apiKeyResult = await Browser.storage.local.get(['apiKey'])
      const apiKey = apiKeyResult.apiKey
      if (!apiKey) {
        return { success: false, message: 'No API key configured' }
      }
      const result = await ApiService.fetchPendingFollowUps(apiKey)
      return result
    } catch (error) {
      console.error('Error fetching pending follow-ups:', error)
      return { success: false, message: 'Failed to fetch pending follow-ups' }
    }
  } else if (message.type === MessageTypes.MARK_FOLLOWUP_SENT) {
    console.log('Bg :: MARK_FOLLOWUP_SENT :: ', message)
    try {
      const apiKeyResult = await Browser.storage.local.get(['apiKey'])
      const apiKey = apiKeyResult.apiKey
      if (!apiKey) {
        return { success: false, message: 'No API key configured' }
      }
      const result = await ApiService.markFollowUpSent(apiKey, message.data.followUpId)
      return result
    } catch (error) {
      console.error('Error marking follow-up as sent:', error)
      return { success: false, message: 'Failed to mark follow-up as sent' }
    }
  } else if (message.type === MessageTypes.MARK_FOLLOWUP_FAILED) {
    console.log('Bg :: MARK_FOLLOWUP_FAILED :: ', message)
    try {
      const apiKeyResult = await Browser.storage.local.get(['apiKey'])
      const apiKey = apiKeyResult.apiKey
      if (!apiKey) {
        return { success: false, message: 'No API key configured' }
      }
      const result = await ApiService.markFollowUpFailed(apiKey, message.data.followUpId, message.data.error)
      return result
    } catch (error) {
      console.error('Error marking follow-up as failed:', error)
      return { success: false, message: 'Failed to mark follow-up as failed' }
    }
  } else if (message.type === MessageTypes.SET_AUTO_DM_ENABLED) {
    console.log('Bg :: SET_AUTO_DM_ENABLED :: ', message)
    await Browser.storage.local.set({ autoDMEnabled: message.data.enabled })
    return { success: true }
  } else if (message.type === MessageTypes.GET_AUTO_DM_STATUS) {
    console.log('Bg :: GET_AUTO_DM_STATUS :: ', message)
    const result = await Browser.storage.local.get(['autoDMEnabled'])
    return { enabled: result.autoDMEnabled !== false } // Default to true if not set
  } else if (message.type === MessageTypes.SEND_FOLLOWUP_MESSAGE) {
    console.log('🚨 DEBUG: Bg :: SEND_FOLLOWUP_MESSAGE :: ', message)
    console.log('🚨 DEBUG: Message data keys:', Object.keys(message.data))
    console.log('🚨 DEBUG: followUpId in message:', message.data.followUpId)

    const followUpManager = FollowUpManager.getInstance()
    const result = await followUpManager.processManualFollowUp(
      message.data.recipientId,
      message.data.username,
      message.data.message,
      message.data.followUpId // Pass the followUpId for API marking
    )

    return result
  }
})

Browser.alarms.onAlarm.addListener(async (alarm) => {
  if (alarm.name === 'heartbeat') {
    console.log('Heartbeat received')
    setTimeout(async () => {
      await runScheduledScanHeartbeat()
    }, 1)
  } else if (alarm.name === 'followUpCheck') {
    console.log('Follow-up check alarm received')
    setTimeout(async () => {
      await processExternalFollowUps()
    }, 1)
  }
})
