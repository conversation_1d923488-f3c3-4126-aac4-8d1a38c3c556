enum LogLevel {
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

class lg {
  private static getFileName(): string {
    const stackTrace = new Error().stack!.split('\n')
    return stackTrace[3].match(/\/([^\/]+)$/)?.[1] || ''
  }

  private static log(level: LogLevel, ...args: any[]) {
    const fileName = lg.getFileName()
    console.log(`[${fileName}] [${level}]`, ...args)
  }

  static info(...args: any[]) {
    lg.log(LogLevel.INFO, ...args)
  }

  static warn(...args: any[]) {
    lg.log(LogLevel.WARN, ...args)
  }

  static error(...args: any[]) {
    lg.log(LogLevel.ERROR, ...args)
  }
}

export default lg
