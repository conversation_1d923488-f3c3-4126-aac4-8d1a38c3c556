import Browser from 'webextension-polyfill'

export const env = {
}

export interface GeneralConfig {
    power: boolean;
    status: string;
    messages: Array<string>;
    onlyZeroMessages: boolean;
    waitTime: Array<number>;
    timeBetweenDM: Array<number>;
    messageLineDelay: Array<number>;
    followerAge: Array<number>;
    dmpool: Array<string>;
    dmPastFollowers: boolean;
    dmCount: Array<number>;
    dmBreakTime: Array<number>;
    zero_message_cache: Array<string>;  // non zero message cache!!
    // Separate timing for past followers (no wait time needed - immediate DM)
    pastFollowerTimeBetweenDM: Array<number>;
    pastFollowerDMCount: Array<number>;
    pastFollowerBreakTime: Array<number>;
}