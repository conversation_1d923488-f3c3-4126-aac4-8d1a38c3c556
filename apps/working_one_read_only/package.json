{"name": "aisetter-chrome-extension", "version": "1.0.1", "description": "AISetter Chrome Extension for Instagram follow-up management", "scripts": {"build": "node build.mjs", "lint": "eslint --ext .js,.mjs,.jsx .", "lint:fix": "eslint --ext .js,.mjs,.jsx . --fix", "watch": "chokidar src -c 'npm run build'", "package": "npm run build && cd build && zip -r ../aisetter-extension.zip ."}, "dependencies": {"lodash-es": "^4.17.21", "lucide-react": "0.477.0", "preact": "^10.11.3", "react": "npm:@preact/compat@^18.3.1", "react-dom": "npm:@preact/compat@^18.3.1", "tailwindcss": "3.4.17"}, "devDependencies": {"@types/fs-extra": "^9.0.13", "@types/logger": "^0.0.5", "@types/webextension-polyfill": "^0.9.2", "@typescript-eslint/eslint-plugin": "8.26.0", "@typescript-eslint/parser": "8.26.0", "archiver": "^5.3.1", "autoprefixer": "10.4.20", "chokidar-cli": "^3.0.0", "dotenv": "^16.5.0", "esbuild": "^0.17.4", "esbuild-style-plugin": "^1.6.1", "eslint": "9.21.0", "eslint-config-prettier": "10.0.2", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "5.2.0", "fs-extra": "^11.1.0", "husky": "^8.0.0", "lint-staged": "^13.1.0", "postcss": "8.5.3", "postcss-scss": "^4.0.6", "prettier": "3.4.2", "prettier-plugin-organize-imports": "^3.2.1", "sass": "^1.57.1", "typescript": "5.7.2", "uglify-es": "^3.3.9", "webextension-polyfill": "^0.10.0"}, "lint-staged": {"**/*.{js,jsx,ts,tsx,mjs}": ["npx prettier --write", "npx eslint --fix"]}}