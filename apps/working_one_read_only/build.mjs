import archiver from 'archiver'
import autoprefixer from 'autoprefixer'
import * as dotenv from 'dotenv'
import esbuild from 'esbuild'
import postcssPlugin from 'esbuild-style-plugin'
import fs from 'fs-extra'
import path from 'node:path'
import { exec } from 'child_process'
import util from 'util'

dotenv.config()

const execPromise = util.promisify(exec) // Promisify exec for async use
const outdir = 'build'

async function deleteOldDir() {
  await fs.remove(outdir)
}

async function runEsbuild() {
  await esbuild.build({
    entryPoints: ['src/content-script/index.tsx', 'src/background/index.ts'],
    bundle: true,
    outdir: outdir,
    treeShaking: true,
    minify: true,
    legalComments: 'none',
    define: {
      'process.env.NODE_ENV': '"production"'
    },
    jsxFactory: 'h',
    jsxFragment: 'Fragment',
    jsx: 'automatic',
    loader: {
      '.png': 'dataurl',
      '.jsx': 'jsx',
    },
    plugins: [
      postcssPlugin({
        postcss: {
          plugins: [autoprefixer],
        },
      }),
    ],
  })
}

async function runHelperBuild() {
  console.log('Copying helper scripts...')
  await fs.ensureDir(`${outdir}/helpers`)

  // Copy localStorage-based implementation (production solution)
  await fs.copy('src/content-script/helpers/ijsource-localstorage.js', `${outdir}/helpers/ijsource-localstorage.js`)
  console.log('Helper scripts copied successfully')
}

async function runInterfaceBuild() {
  console.log('Running build for src/interface...')
  await execPromise('npm run build', { cwd: path.resolve('src/interface') })
}

async function copyInterfaceDist() {
  const srcDir = 'src/interface/dist'
  const destDir = `${outdir}/chromium`

  await fs.ensureDir(destDir)
  await fs.copy(srcDir, destDir)
}

async function zipFolder(dir) {
  const output = fs.createWriteStream(`${dir}.zip`)
  const archive = archiver('zip', {
    zlib: { level: 9 },
  })
  archive.pipe(output)
  archive.directory(dir, false)
  await archive.finalize()
}

async function copyFiles(entryPoints, targetDir) {
  await fs.ensureDir(targetDir)
  await Promise.all(
    entryPoints.map(async (entryPoint) => {
      await fs.copy(entryPoint.src, `${targetDir}/${entryPoint.dst}`)
    }),
  )
}

async function build() {
  await deleteOldDir()
  await runEsbuild()

  // Run the build command and copy dist files
  await runInterfaceBuild()
  await runHelperBuild()
  await copyInterfaceDist()

  const commonFiles = [
    { src: 'build/content-script/index.js', dst: 'content-script.js' },
    { src: 'build/content-script/index.css', dst: 'content-script.css' },
    { src: 'build/background/index.js', dst: 'background.js' },
    { src: 'build/helpers/ijsource-localstorage.js', dst: 'ijsource-localstorage.js' },
    { src: 'src/logo16.png', dst: 'logo16.png' },
    { src: 'src/logo32.png', dst: 'logo32.png' },
    { src: 'src/logo48.png', dst: 'logo48.png' },
    { src: 'src/logo128.png', dst: 'logo128.png' }
  ]

  // Copy common files to chromium folder
  await copyFiles(
    [...commonFiles, { src: 'src/manifest.json', dst: 'manifest.json' }],
    `./${outdir}/chromium`,
  )

  // Zip the chromium build
  await zipFolder(`./${outdir}/chromium`)

  console.log('Build success.')
}

build()
