# Changelog

All notable changes to this project will be documented in this file.

## [1.0.1] - 2025-05-29

### Fixed
- **Instagram Followers Scraping**: Fixed slow and ineffective scrolling in followers dialog
  - Enhanced scrollable element detection with multiple methods:
    - Direct `overflow-y: scroll` detection in computed styles
    - Max-height + overflow detection for constrained containers
    - Third container targeting for specific Instagram DOM structure
    - CSS class pattern matching for Instagram-specific elements
  - Improved scrolling mechanism with fallback methods:
    - `scrollBy` with smooth behavior (primary method)
    - Direct `scrollTop` manipulation (fallback)
    - Parent element scrolling (when child scrolling fails)
    - Wheel event simulation (last resort)
  - Better link filtering to exclude `/followers` and `/following` URLs
  - Enhanced progress detection and bottom-reached logic
  - Reduced wait times for better performance while maintaining reliability

### Technical Details
- Added `findFollowersScrollContainer()` function for precise element targeting
- Replaced simple `humanLikeScroll()` with multi-method scrolling approach
- Improved error handling and TypeScript null safety
- Enhanced logging for better debugging capabilities

### Impact
- Followers scraping now works reliably and efficiently
- Significantly faster scrolling and data collection
- Better handling of Instagram's dynamic DOM structure
- Reduced likelihood of getting stuck or missing followers

## [1.0.0] - Initial Release
- Basic Instagram DM automation functionality
- Followers panel opening
- Message sending capabilities
- User activity simulation