{"name": "AISetter", "description": "AISetter", "version": "1.0.1", "manifest_version": 3, "icons": {"16": "icons/logo16.png", "32": "icons/logo32.png", "48": "icons/logo48.png", "128": "icons/logo128.png"}, "host_permissions": ["*://*.instagram.com/*", "*://app.aisetter.pl/*", "*://localhost:*/*", "*://127.0.0.1:*/*", "*://*.ngrok-free.app/*", "*://*.ngrok.app/*", "*://*.ngrok.io/*"], "permissions": ["storage", "unlimitedStorage", "cookies", "identity", "alarms", "debugger", "activeTab", "scripting"], "background": {"service_worker": "background.js", "type": "module"}, "action": {"default_popup": "index.html"}, "content_scripts": [{"run_at": "document_end", "matches": ["https://www.instagram.com/*"], "js": ["content.js"], "css": ["content.css"], "all_frames": false}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "web_accessible_resources": [{"resources": ["inject/ijsource-localstorage.js"], "matches": ["*://*/*"]}]}