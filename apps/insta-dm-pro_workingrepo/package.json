{"name": "aisetter-chrome-extension-working", "version": "1.0.1", "description": "AISetter Chrome Extension for Instagram follow-up management", "type": "module", "scripts": {"dev": "vite", "build": "node build-extension.mjs", "build:old": "vite build", "preview": "vite preview", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "package": "npm run build && cd dist && zip -r ../aisetter-extension.zip ."}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "2.1.6", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "1.1.2", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash-es": "^4.17.21", "lucide-react": "0.477.0", "react": "19.0.0", "react-dom": "19.0.0", "tailwind-merge": "3.0.2", "tailwindcss-animate": "1.0.7"}, "devDependencies": {"@eslint/js": "9.21.0", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/webextension-polyfill": "^0.9.2", "@typescript-eslint/eslint-plugin": "8.26.0", "@typescript-eslint/parser": "8.26.0", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "10.4.20", "eslint": "9.21.0", "eslint-config-prettier": "10.0.2", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "5.2.0", "globals": "16.0.0", "postcss": "8.5.3", "prettier": "3.4.2", "prettier-plugin-organize-imports": "^3.2.1", "tailwindcss": "3.4.17", "typescript": "5.7.2", "vite": "^6.0.5", "webextension-polyfill": "^0.10.0"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"]}}