# User Mimicking and Tab Management Implementation

## Overview

Enhanced the Instagram DM Pro Chrome extension with advanced user mimicking behaviors and intelligent tab management during breaks and pause windows.

## New Features Implemented

### 1. User Mimicking During Wait Times

**Location**: [`background/index.ts`](src/background/index.ts:162-236)

When waiting between users (3-8 minutes based on API settings), the extension now performs realistic user actions:

- **Random Actions**: 1-3 actions during each wait period
- **Action Types**:

  - Navigate to Instagram home (`https://www.instagram.com/`)
  - Navigate to Instagram explore (`https://www.instagram.com/explore/`)
  - Navigate to Instagram direct inbox (`https://www.instagram.com/direct/inbox/`)
  - Perform scroll actions via content script

- **Timing**: Actions are spaced evenly throughout the wait period
- **Realistic Delays**: 2-5 seconds after navigation, 1-3 seconds after scrolling

### 2. Instagram Tab Closure During Breaks/Pauses

**Location**: [`background/index.ts`](src/background/index.ts:122-159)

The extension now completely closes Instagram tabs during:

#### Break Logic (API-driven)

- When reaching message limits (8-15 messages before break)
- During break periods (10-20 minutes)
- Closes both main and secondary Instagram tabs
- Clears stored tab IDs from storage

#### Pause Windows (API-driven)

- During configured pause hours (00:30-07:00 by default)
- Completely stops all Instagram activity
- Closes all Instagram tabs to avoid detection

### 3. Content Script Integration

**Location**: [`content/Component.tsx`](src/content/Component.tsx:1725-1735)

Added `MIMIC_SCROLL` message handler to perform scrolling actions when requested by the background script.

## Implementation Details

### API Settings Integration

All timing is controlled by API settings:

- `timeBetweenDMsMin/Max`: Controls user mimicking duration
- `messagesBeforeBreakMin/Max`: Controls when to take breaks
- `breakDurationMin/Max`: Controls break duration
- `pauseStart/pauseStop`: Controls pause window hours

### Enhanced Flow Comparison

#### Before (Simple Delay)

```typescript
await delay(randomWait); // Just wait
```

#### After (User Mimicking)

```typescript
await mimicUserActionsDuringWait(tab, randomWait); // Realistic actions
```

#### Before (Continue During Breaks)

```typescript
await delay(breakDurationMs); // Just wait
```

#### After (Complete Tab Closure)

```typescript
await closeInstagramTabs(); // Close all Instagram tabs
await delay(breakDurationMs); // Wait with no Instagram presence
```

## Key Functions

### `mimicUserActionsDuringWait(tab, totalWaitTime)`

- Calculates number of actions to perform (1-3)
- Spaces actions evenly throughout wait time
- Performs random Instagram navigation
- Includes realistic delays between actions
- Fallback to simple delay if errors occur

### `closeInstagramTabs()`

- Gets stored main and secondary tab IDs
- Safely closes tabs with error handling
- Clears stored tab IDs from DataLayer
- Logs all actions for debugging

## Benefits

1. **Stealth Operation**: Mimics real user behavior during wait times
2. **Detection Avoidance**: Completely removes Instagram presence during breaks
3. **API Compliance**: All timing respects organization API settings
4. **Robust Error Handling**: Graceful fallbacks if actions fail
5. **Dual Flow Support**: Works with both new API-driven and legacy flows

## Usage

The features are automatically active when:

- Extension is running with valid API key
- API settings are properly configured
- Wait times between users occur (3-8 minutes)
- Break logic is triggered (after 8-15 messages)
- Pause windows are active (00:30-07:00 by default)

## Testing

Build the extension:

```bash
cd apps/insta-dm-pro
npm run build
```

The extension will automatically use these features during normal operation.
