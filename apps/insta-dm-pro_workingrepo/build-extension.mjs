import { build } from 'vite'
import path from 'path'
import react from '@vitejs/plugin-react'

const __dirname = path.dirname(new URL(import.meta.url).pathname)

// Shared configuration
const sharedConfig = {
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@background': path.resolve(__dirname, './src/background'),
      '@content': path.resolve(__dirname, './src/content'),
      '@popup': path.resolve(__dirname, './src/popup'),
      '@shared': path.resolve(__dirname, './src/shared'),
      '@types': path.resolve(__dirname, './src/types')
    }
  },
  plugins: [react()],
  define: {
    global: 'globalThis',
    'process.env.NODE_ENV': '"production"'
  }
}

// Build background script as ES module (for service worker)
console.log('Building background script (ES module)...')
await build({
  configFile: false,
  ...sharedConfig,
  build: {
    outDir: 'dist',
    emptyOutDir: false,
    lib: {
      entry: path.resolve(__dirname, 'src/background/index.ts'),
      name: 'Background',
      fileName: 'background',
      formats: ['es']
    },
    rollupOptions: {
      external: [],
      output: {
        entryFileNames: 'background.js'
      }
    }
  }
})

// Build content script as IIFE (for content script compatibility)
console.log('Building content script (IIFE)...')
await build({
  configFile: false,
  ...sharedConfig,
  build: {
    outDir: 'dist',
    emptyOutDir: false,
    lib: {
      entry: path.resolve(__dirname, 'src/content/index.tsx'),
      name: 'ContentScript',
      fileName: 'content',
      formats: ['iife']
    },
    rollupOptions: {
      external: [],
      output: {
        entryFileNames: 'content.js'
      }
    }
  }
})

// Build popup with regular Vite build
console.log('Building popup...')
await build({
  configFile: false,
  ...sharedConfig,
  build: {
    outDir: 'dist',
    emptyOutDir: false,
    rollupOptions: {
      input: {
        popup: path.resolve(__dirname, 'index.html')
      },
      output: {
        entryFileNames: '[name].js',
        assetFileNames: (assetInfo) => {
          if (assetInfo.name?.endsWith('.css')) {
            if (assetInfo.name.includes('content')) return 'content.css'
            return 'index.css'
          }
          return 'assets/[name][extname]'
        },
        format: 'es'
      }
    }
  }
})

console.log('✅ Extension build completed!')