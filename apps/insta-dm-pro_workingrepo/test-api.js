#!/usr/bin/env node

/**
 * Simple API test script for Chrome extension
 * Tests the API connections and endpoints
 */

import { ApiService } from './dist/shared/api-service.js';

const TEST_API_KEY = 'api_b777c538fad684d76b261053485ce620';

async function testApiEndpoints() {
  console.log('🧪 Testing AISetter Chrome Extension API...\n');
  
  try {
    // Test 1: Verify API key
    console.log('1️⃣ Testing API key verification...');
    const keyResult = await ApiService.verifyApiKey(TEST_API_KEY);
    console.log('Result:', keyResult);
    
    if (!keyResult.success) {
      console.log('❌ API key verification failed. Please check your API key.');
      return;
    }
    
    console.log('✅ API key verified successfully\n');
    
    // Test 2: Get Chrome extension settings
    console.log('2️⃣ Testing Chrome extension settings...');
    const settingsResult = await ApiService.getChromeExtensionSettings(TEST_API_KEY);
    console.log('Result:', settingsResult);
    console.log(settingsResult.success ? '✅ Settings retrieved' : '❌ Settings failed');
    console.log('');
    
    // Test 3: Get attack list
    console.log('3️⃣ Testing attack list...');
    const attackListResult = await ApiService.getAttackList(TEST_API_KEY, 5);
    console.log('Result:', attackListResult);
    console.log(attackListResult.success ? '✅ Attack list retrieved' : '❌ Attack list failed');
    console.log('');
    
    // Test 4: Get pending follow-ups
    console.log('4️⃣ Testing pending follow-ups...');
    const followUpsResult = await ApiService.fetchPendingFollowUps(TEST_API_KEY);
    console.log('Result:', followUpsResult);
    console.log(followUpsResult.success ? '✅ Follow-ups retrieved' : '❌ Follow-ups failed');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Check if API key is provided
if (TEST_API_KEY === 'your-test-api-key-here') {
  console.log('❌ Please update TEST_API_KEY in test-api.js with your actual API key');
  process.exit(1);
}

testApiEndpoints();