import { createRoot } from 'react-dom/client'
import '@/base.css'
import Component from './Component'
import lg from '@shared/logger'

async function mount() {
  const container = document.createElement('div')
  container.className = 'insta-container'
  container.id = 'instaContainer'
  const bdy = document.querySelector('body')
  if (bdy) {
    bdy.prepend(container)
  }

  lg.info("[index.tsx] mounting component root.")

  const root = createRoot(container)
  root.render(<Component />)
}

async function run() {
  mount()
}

run()
