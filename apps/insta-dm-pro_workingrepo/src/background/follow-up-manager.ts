import Browser from 'webextension-polyfill'
import { MessageTypes } from '@shared/messaging'
import { ApiService } from '@shared/api-service'
import delay from './Utils/delay'

export interface PendingFollowUp {
    id: string
    contactId: string
    recipientId: string
    username: string
    message: string
    scheduledTime: string
    followUpNumber: number
}

export class FollowUpManager {
    private static instance: FollowUpManager
    private isProcessing = false
    private currentTab: Browser.Tabs.Tab | null = null
    private retryAttempts = 3
    private retryDelay = 5000
    private processedFollowUps = new Set<string>() // Track processed follow-ups to prevent duplicates

    static getInstance(): FollowUpManager {
        if (!FollowUpManager.instance) {
            FollowUpManager.instance = new FollowUpManager()
        }
        return FollowUpManager.instance
    }

    private constructor() { }

    /**
     * Get or create a valid Instagram tab
     */
    private async getValidInstagramTab(): Promise<Browser.Tabs.Tab> {
        console.log('🚨 DEBUG: Getting valid Instagram tab...')

        try {
            // Check if current tab is still valid
            if (this.currentTab?.id) {
                try {
                    const tabInfo = await Browser.tabs.get(this.currentTab.id)
                    if (tabInfo && tabInfo.url?.includes('instagram.com')) {
                        console.log('🚨 DEBUG: Current tab is still valid:', tabInfo.id)
                        return tabInfo
                    }
                } catch (error) {
                    console.log('🚨 DEBUG: Current tab is invalid, creating new one')
                }
            }

            // Find existing Instagram tab
            const tabs = await Browser.tabs.query({ url: '*://www.instagram.com/*' })
            if (tabs.length > 0) {
                console.log('🚨 DEBUG: Found existing Instagram tab:', tabs[0].id)
                this.currentTab = tabs[0]
                return tabs[0]
            }

            // Create new Instagram tab
            console.log('🚨 DEBUG: Creating new Instagram tab')
            const newTab = await Browser.tabs.create({
                url: 'https://www.instagram.com/direct/inbox/',
                active: false
            })

            if (!newTab.id) {
                throw new Error('Failed to create Instagram tab')
            }

            // Wait for tab to load
            await this.waitForTabToLoad(newTab)
            await delay(3000) // Additional time for Instagram to fully load

            this.currentTab = newTab
            return newTab
        } catch (error) {
            console.error('🚨 ERROR: Failed to get valid Instagram tab:', error)
            throw new Error(`Tab management failed: ${error}`)
        }
    }

    /**
     * Wait for tab to finish loading
     */
    private async waitForTabToLoad(tab: Browser.Tabs.Tab): Promise<void> {
        if (!tab.id) return

        return new Promise((resolve) => {
            const checkStatus = async () => {
                try {
                    const tabInfo = await Browser.tabs.get(tab.id!)
                    if (tabInfo.status === 'complete') {
                        resolve()
                    } else {
                        setTimeout(checkStatus, 500)
                    }
                } catch (error) {
                    console.error('Error checking tab status:', error)
                    resolve() // Continue anyway
                }
            }
            checkStatus()
        })
    }

    /**
     * Send message to content script with retry logic
     */
    private async sendMessageToTab(tabId: number, message: any, retries = 3): Promise<any> {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                console.log(`🚨 DEBUG: Sending message to tab ${tabId}, attempt ${attempt}:`, message.type)

                // Check if tab still exists
                await Browser.tabs.get(tabId)

                const result = await Browser.tabs.sendMessage(tabId, message)
                console.log(`🚨 DEBUG: Message sent successfully on attempt ${attempt}`)
                return result
            } catch (error) {
                console.error(`🚨 ERROR: Message send attempt ${attempt} failed:`, error)

                if (attempt === retries) {
                    throw error
                }

                // Wait before retry
                await delay(1000 * attempt)

                // Try to refresh the tab connection
                try {
                    await Browser.tabs.reload(tabId)
                    await delay(3000)
                } catch (reloadError) {
                    console.error('Failed to reload tab:', reloadError)
                }
            }
        }
    }

    /**
     * Navigate to user profile and prepare for messaging
     */
    private async navigateToUserProfile(tab: Browser.Tabs.Tab, username: string): Promise<void> {
        if (!tab.id) throw new Error('Invalid tab ID')

        console.log(`🚨 DEBUG: Navigating to ${username}'s profile`)

        const profileUrl = `https://www.instagram.com/${username}/`
        await Browser.tabs.update(tab.id, { url: profileUrl })

        // Wait for navigation to complete
        await this.waitForTabToLoad(tab)
        await delay(4000)

        console.log(`🚨 DEBUG: Profile navigation completed for ${username}`)
    }

    /**
     * Visit user messages (click message button)
     */
    private async visitUserMessages(tab: Browser.Tabs.Tab, username: string): Promise<void> {
        if (!tab.id) throw new Error('Invalid tab ID')

        console.log(`🚨 DEBUG: Visiting messages for ${username}`)

        await this.sendMessageToTab(tab.id, {
            type: MessageTypes.VISIT_USER_MESSAGES,
            data: { recipent_username: username }
        })

        // Wait for DM page to load
        await delay(6000)

        console.log(`🚨 DEBUG: Message page loaded for ${username}`)
    }

    /**
     * Send message to user
     */
    private async sendMessageToUser(tab: Browser.Tabs.Tab, recipientId: string, message: string): Promise<boolean> {
        if (!tab.id) throw new Error('Invalid tab ID')

        console.log(`🚨 DEBUG: Sending message to recipient ${recipientId}`)

        const result = await this.sendMessageToTab(tab.id, {
            type: MessageTypes.MESSAGE_PROFILE,
            data: {
                recipent_id: recipientId,
                text: message
            }
        })

        console.log(`🚨 DEBUG: Message send result:`, result, typeof result)
        return result === true
    }

    /**
     * Process a single follow-up with full error handling
     */
    private async processSingleFollowUp(followUp: PendingFollowUp, apiKey: string): Promise<boolean> {
        console.log(`🚨 DEBUG: Processing follow-up ${followUp.id} to ${followUp.username}`)

        // Check if this follow-up has already been processed in this session
        if (this.processedFollowUps.has(followUp.id)) {
            console.log(`🚨 DEBUG: Follow-up ${followUp.id} already processed in this session, skipping`)
            return true
        }

        // Mark as being processed
        this.processedFollowUps.add(followUp.id)
        console.log(`🚨 DEBUG: Added follow-up ${followUp.id} to processed set`)

        try {
            // Step 1: Get valid tab
            const tab = await this.getValidInstagramTab()

            // Step 2: Navigate to profile
            await this.navigateToUserProfile(tab, followUp.username)

            // Step 3: Visit messages
            await this.visitUserMessages(tab, followUp.username)

            // Step 4: Send message
            const success = await this.sendMessageToUser(tab, followUp.recipientId, followUp.message)

            if (success) {
                console.log(`✅ Follow-up ${followUp.id} sent successfully to ${followUp.username}`)
                console.log(`🚨 DEBUG: Making API call to mark follow-up ${followUp.id} as SENT`)
                try {
                    const apiResult = await ApiService.markFollowUpSent(apiKey, followUp.id)
                    console.log(`🚨 DEBUG: API markFollowUpSent result:`, apiResult)
                    if (apiResult.success) {
                        console.log(`✅ Follow-up ${followUp.id} successfully marked as SENT in API`)
                    } else {
                        console.error(`❌ Failed to mark follow-up ${followUp.id} as sent in API:`, apiResult.message)
                    }
                } catch (apiError) {
                    console.error(`❌ API error marking follow-up ${followUp.id} as sent:`, apiError)
                }
                return true
            } else {
                console.log(`❌ Follow-up ${followUp.id} failed to send to ${followUp.username}`)
                console.log(`🚨 DEBUG: Making API call to mark follow-up ${followUp.id} as FAILED`)
                try {
                    const apiResult = await ApiService.markFollowUpFailed(apiKey, followUp.id, 'Message sending failed')
                    console.log(`🚨 DEBUG: API markFollowUpFailed result:`, apiResult)
                    if (apiResult.success) {
                        console.log(`✅ Follow-up ${followUp.id} successfully marked as FAILED in API`)
                    } else {
                        console.error(`❌ Failed to mark follow-up ${followUp.id} as failed in API:`, apiResult.message)
                    }
                } catch (apiError) {
                    console.error(`❌ API error marking follow-up ${followUp.id} as failed:`, apiError)
                }
                return false
            }
        } catch (error) {
            console.error(`❌ Error processing follow-up ${followUp.id}:`, error)
            console.log(`🚨 DEBUG: Making API call to mark follow-up ${followUp.id} as FAILED due to exception`)
            try {
                const apiResult = await ApiService.markFollowUpFailed(apiKey, followUp.id, `Processing error: ${error}`)
                console.log(`🚨 DEBUG: API markFollowUpFailed (exception) result:`, apiResult)
                if (apiResult.success) {
                    console.log(`✅ Follow-up ${followUp.id} successfully marked as FAILED in API (exception)`)
                } else {
                    console.error(`❌ Failed to mark follow-up ${followUp.id} as failed in API (exception):`, apiResult.message)
                }
            } catch (markError) {
                console.error(`❌ API error marking follow-up ${followUp.id} as failed (exception):`, markError)
            }
            return false
        }
    }

    /**
     * Process all pending follow-ups
     */
    async processFollowUps(): Promise<void> {
        if (this.isProcessing) {
            console.log('🚨 DEBUG: Follow-up processing already in progress, skipping...')
            return
        }

        this.isProcessing = true
        console.log('🚨 DEBUG: Starting follow-up processing...')

        try {
            // Get API configuration
            const apiKeyResult = await Browser.storage.local.get(['apiKey', 'apiStatus', 'autoDMEnabled'])
            const { apiKey, apiStatus, autoDMEnabled } = apiKeyResult

            if (!apiKey || !apiStatus?.isAuthenticated) {
                console.log('🚨 DEBUG: No valid API configuration, skipping follow-ups')
                return
            }

            if (autoDMEnabled === false) {
                console.log('🚨 DEBUG: Auto-DM disabled, skipping follow-ups')
                return
            }

            // Fetch pending follow-ups
            const result = await ApiService.fetchPendingFollowUps(apiKey)
            if (!result.success || !result.data || result.data.length === 0) {
                console.log('🚨 DEBUG: No pending follow-ups found')
                return
            }

            console.log(`🚨 DEBUG: Found ${result.data.length} pending follow-ups`)

            // Process each follow-up
            for (const followUp of result.data) {
                if (!this.isProcessing) {
                    console.log('🚨 DEBUG: Processing stopped, breaking loop')
                    break
                }

                // Check if follow-up is scheduled for now or past due
                const scheduledTime = new Date(followUp.scheduledTime)
                const now = new Date()

                if (scheduledTime > now) {
                    console.log(`🚨 DEBUG: Follow-up ${followUp.id} scheduled for ${scheduledTime.toISOString()}, current time ${now.toISOString()} - skipping (not due yet)`)
                    continue
                }

                console.log(`🚨 DEBUG: Follow-up ${followUp.id} is due (scheduled: ${scheduledTime.toISOString()}, now: ${now.toISOString()})`)
                await this.processSingleFollowUp(followUp, apiKey)

                // Wait between follow-ups to avoid rate limiting
                await delay(8000)
            }

            console.log('✅ Follow-up processing completed')

            // Clear processed follow-ups set after successful batch processing
            this.clearProcessedFollowUps()
        } catch (error) {
            console.error('❌ Error in follow-up processing:', error)
        } finally {
            this.isProcessing = false
        }
    }

    /**
     * Process manual follow-up
     */
    async processManualFollowUp(recipientId: string, username: string, message: string, followUpId?: string): Promise<{ success: boolean; message?: string }> {
        if (this.isProcessing) {
            return {
                success: false,
                message: 'Another follow-up is currently being processed. Please wait.'
            }
        }

        this.isProcessing = true
        console.log(`🚨 DEBUG: Processing manual follow-up to ${username}`)

        try {
            // Step 1: Get valid tab
            const tab = await this.getValidInstagramTab()

            // Step 2: Navigate to profile
            await this.navigateToUserProfile(tab, username)

            // Step 3: Visit messages
            await this.visitUserMessages(tab, username)

            // Step 4: Send message
            const success = await this.sendMessageToUser(tab, recipientId, message)

            if (success) {
                console.log(`✅ Manual follow-up sent successfully to ${username}`)

                // Step 5: Mark as sent in API if followUpId is provided
                if (followUpId) {
                    console.log(`🚨 DEBUG: Making API call to mark manual follow-up ${followUpId} as SENT`)
                    try {
                        // Get API key from storage
                        const apiKeyResult = await Browser.storage.local.get(['apiKey'])
                        const apiKey = apiKeyResult.apiKey

                        if (apiKey) {
                            const apiResult = await ApiService.markFollowUpSent(apiKey, followUpId)
                            console.log(`🚨 DEBUG: API markFollowUpSent (manual) result:`, apiResult)
                            if (apiResult.success) {
                                console.log(`✅ Manual follow-up ${followUpId} successfully marked as SENT in API`)
                            } else {
                                console.error(`❌ Failed to mark manual follow-up ${followUpId} as sent in API:`, apiResult.message)
                            }
                        } else {
                            console.log(`🚨 DEBUG: No API key found, skipping API call for manual follow-up`)
                        }
                    } catch (apiError) {
                        console.error(`❌ API error marking manual follow-up ${followUpId} as sent:`, apiError)
                    }
                }

                return { success: true }
            } else {
                console.log(`❌ Manual follow-up failed to send to ${username}`)

                // Mark as failed in API if followUpId is provided
                if (followUpId) {
                    console.log(`🚨 DEBUG: Making API call to mark manual follow-up ${followUpId} as FAILED`)
                    try {
                        const apiKeyResult = await Browser.storage.local.get(['apiKey'])
                        const apiKey = apiKeyResult.apiKey

                        if (apiKey) {
                            const apiResult = await ApiService.markFollowUpFailed(apiKey, followUpId, 'Manual send failed')
                            console.log(`🚨 DEBUG: API markFollowUpFailed (manual) result:`, apiResult)
                        }
                    } catch (apiError) {
                        console.error(`❌ API error marking manual follow-up ${followUpId} as failed:`, apiError)
                    }
                }

                return { success: false, message: 'Failed to send message' }
            }
        } catch (error) {
            console.error(`❌ Error processing manual follow-up to ${username}:`, error)

            // Mark as failed in API if followUpId is provided
            if (followUpId) {
                console.log(`🚨 DEBUG: Making API call to mark manual follow-up ${followUpId} as FAILED due to exception`)
                try {
                    const apiKeyResult = await Browser.storage.local.get(['apiKey'])
                    const apiKey = apiKeyResult.apiKey

                    if (apiKey) {
                        const apiResult = await ApiService.markFollowUpFailed(apiKey, followUpId, `Manual processing error: ${error}`)
                        console.log(`🚨 DEBUG: API markFollowUpFailed (manual exception) result:`, apiResult)
                    }
                } catch (apiError) {
                    console.error(`❌ API error marking manual follow-up ${followUpId} as failed (exception):`, apiError)
                }
            }

            return {
                success: false,
                message: `Error: ${error}`
            }
        } finally {
            this.isProcessing = false
        }
    }

    /**
     * Stop processing
     */
    stop(): void {
        console.log('🚨 DEBUG: Stopping follow-up processing')
        this.isProcessing = false
    }

    /**
     * Check if currently processing
     */
    isCurrentlyProcessing(): boolean {
        return this.isProcessing
    }

    /**
     * Clear processed follow-ups set (called after successful API marking)
     */
    clearProcessedFollowUps(): void {
        console.log(`🚨 DEBUG: Clearing processed follow-ups set (had ${this.processedFollowUps.size} entries)`)
        this.processedFollowUps.clear()
    }
}