/**
 * Environment configuration for the AISetter Chrome Extension
 */

export interface Environment {
  apiBaseUrl: string;
  isDevelopment: boolean;
  version: string;
}

// For development, always use localhost. You can change this to production URL when deploying.
export const environment: Environment = {
  apiBaseUrl: 'https://app.aisetter.pl', // Updated ngrok URL
  isDevelopment: true, // Force development mode for now
  version: '1.0.1'
};

export default environment;
