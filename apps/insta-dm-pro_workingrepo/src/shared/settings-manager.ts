/**
 * Simplified Settings Manager for Chrome Extension
 * Handles automatic synchronization of settings from organization API
 */

import Browser from 'webextension-polyfill'
import { ApiService } from './api-service'
import logger from './logger'

export interface ApiSettings {
  timeBetweenDMsMin: number
  timeBetweenDMsMax: number
  messagesBeforeBreakMin: number
  messagesBeforeBreakMax: number
  breakDurationMin: number
  breakDurationMax: number
  pauseStart: string
  pauseStop: string
  smartFocus: boolean
}

export interface LocalState {
  power: boolean
  status: string
  messages: Array<string>
  onlyZeroMessages: boolean
  dmpool: Array<string>
  zero_message_cache: Array<string>
  lastSyncTime: number
}

export class SettingsManager {
  private static instance: SettingsManager
  private readonly STORAGE_KEY = '_extension_state'
  
  private constructor() {}
  
  static getInstance(): SettingsManager {
    if (!SettingsManager.instance) {
      SettingsManager.instance = new SettingsManager()
    }
    return SettingsManager.instance
  }
  
  /**
   * Sync settings from the organization API
   */
  async syncSettings(): Promise<ApiSettings | null> {
    try {
      logger.info('🔧 Syncing settings from organization API...')
      
      // Get API key
      const apiKeyResult = await Browser.storage.local.get(['apiKey'])
      const apiKey = apiKeyResult.apiKey
      
      if (!apiKey) {
        logger.info('🔧 No API key available, skipping sync')
        return null
      }
      
      // Fetch settings from API
      const settingsResult = await ApiService.getChromeExtensionSettings(apiKey)
      
      if (!settingsResult.success || !settingsResult.data) {
        logger.error('🔧 Failed to fetch settings from API:', settingsResult.message)
        return null
      }
      
      const apiSettings = settingsResult.data
      logger.info('🔧 Received settings from API:', apiSettings)
      
      // Update last sync time
      await this.updateLocalState({ lastSyncTime: Date.now() })
      
      return {
        timeBetweenDMsMin: apiSettings.timeBetweenDMsMin,
        timeBetweenDMsMax: apiSettings.timeBetweenDMsMax,
        messagesBeforeBreakMin: apiSettings.messagesBeforeBreakMin,
        messagesBeforeBreakMax: apiSettings.messagesBeforeBreakMax,
        breakDurationMin: apiSettings.breakDurationMin,
        breakDurationMax: apiSettings.breakDurationMax,
        pauseStart: apiSettings.pauseStart,
        pauseStop: apiSettings.pauseStop,
        smartFocus: apiSettings.smartFocus
      }
      
    } catch (error) {
      logger.error('🔧 Settings sync failed:', error)
      return null
    }
  }
  
  /**
   * Get current local state
   */
  async getLocalState(): Promise<LocalState> {
    const result = await Browser.storage.local.get([this.STORAGE_KEY])
    return result[this.STORAGE_KEY] || this.getDefaultLocalState()
  }
  
  /**
   * Update local state
   */
  async updateLocalState(updates: Partial<LocalState>): Promise<void> {
    const currentState = await this.getLocalState()
    const updatedState = { ...currentState, ...updates }
    await Browser.storage.local.set({
      [this.STORAGE_KEY]: updatedState
    })
  }
  
  /**
   * Get default local state
   */
  private getDefaultLocalState(): LocalState {
    return {
      power: false,
      status: '',
      messages: ['Hello! 👋'],
      onlyZeroMessages: false,
      dmpool: [],
      zero_message_cache: [],
      lastSyncTime: 0
    }
  }
  
  /**
   * Check if settings are within pause window
   */
  async isInPauseWindow(apiSettings: ApiSettings): Promise<boolean> {
    const now = new Date()
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    
    return currentTime >= apiSettings.pauseStart && currentTime <= apiSettings.pauseStop
  }
}