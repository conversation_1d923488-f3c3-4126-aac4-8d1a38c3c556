import { useEffect, useState } from "react"
import { But<PERSON> } from "../components/ui/button"
import { Textarea } from "../components/ui/textarea"
import { Trash2, Plus } from "lucide-react"
import Browser from "webextension-polyfill"
import { MessageTypes } from "@shared/messaging"

export default function AutoDMMessages() {
  const [messages, setMessages] = useState<string[]>([])
  const [newMessage, setNewMessage] = useState("")
  const [isSaving, setIsSaving] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    Browser.runtime.sendMessage({
      type: MessageTypes.GET_SETTINGS
    }).then((settings) => {
      setMessages(settings.messages || [])
      setIsLoading(false)
    })
  }, [])
  const addMessage = () => {
    if (newMessage.trim()) {
      setMessages([...messages, newMessage.trim()])
      setNewMessage("")
    }
  }

  const deleteMessage = (index: number) => {
    setMessages(messages.filter((_, i) => i !== index))
  }

  const updateMessage = (index: number, value: string) => {
    const updatedMessages = [...messages]
    updatedMessages[index] = value
    setMessages(updatedMessages)
  }

  const handleSave = () => {
    // TODO: Implement save functionality
    console.log("Saving Auto DM Messages:", messages)
    setIsSaving(true)
    Browser.runtime.sendMessage({
      type: MessageTypes.SAVE_SETTINGS,
      data: { messages }
    }).then(() => {
      setTimeout(() => {
        setIsSaving(false)
      }, 1000);
    })
  }

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold">Auto DM Messages</h1>

      {isLoading ? <div>Loading...</div> : <>
        
      <div className="space-y-4">
        {messages.map((message, index) => (
          <div key={index} className="flex items-start space-x-2">
            <Textarea value={message} onChange={(e) => updateMessage(index, e.target.value)} rows={3} />
            <Button variant="destructive" size="icon" onClick={() => deleteMessage(index)}>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>

      <div className="flex items-start space-x-2">
        <Textarea
          placeholder="Enter new message"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          rows={3}
        />
        <Button onClick={addMessage}>
          <Plus className="h-4 w-4 mr-2" />
          Add Message
        </Button>
      </div>
      </>}
      <Button onClick={handleSave} disabled={isSaving || isLoading}>{isSaving ? "Saving..." : "Save Messages"}</Button>
    </div>
  )
}

