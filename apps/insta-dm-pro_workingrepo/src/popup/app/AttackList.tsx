import { useState, useEffect } from 'react';
import Browser from 'webextension-polyfill';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { MessageTypes } from "@shared/messaging";
import { ApiService } from '@shared/api-service';

export default function AttackList() {
  const [attackListData, setAttackListData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastRefresh, setLastRefresh] = useState(null);

  useEffect(() => {
    loadAttackList();
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadAttackList, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadAttackList = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get API key from background script
      const apiKey = await Browser.runtime.sendMessage({
        type: MessageTypes.GET_API_KEY
      });

      if (!apiKey) {
        setError('No API key configured. Please set up your API key first.');
        return;
      }

      // Fetch attack list data
      const result = await ApiService.getMessagesToSend(apiKey);
      
      if (result.success) {
        setAttackListData(result);
        setLastRefresh(new Date());
      } else {
        setError(result.message || 'Failed to load attack list');
      }
    } catch (err) {
      console.error('Error loading attack list:', err);
      setError('Failed to connect to API. Make sure the dashboard is running.');
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'followup':
        return 'bg-blue-500';
      case 'batch_sequence':
        return 'bg-green-500';
      case 'batch':
        return 'bg-purple-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'followup':
        return 'Follow-up';
      case 'batch_sequence':
        return 'Batch Sequence';
      case 'batch':
        return 'Single Batch';
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">🎯 Attack List</h2>
        <Button 
          onClick={loadAttackList} 
          disabled={isLoading}
          variant="outline"
          size="sm"
        >
          {isLoading ? "Loading..." : "Refresh"}
        </Button>
      </div>

      {/* Summary Card */}
      {attackListData && (
        <Card>
          <CardHeader>
            <CardTitle>Summary</CardTitle>
            <CardDescription>
              Current attack list status
              {lastRefresh && (
                <span className="block text-xs mt-1">
                  Last updated: {lastRefresh.toLocaleTimeString()}
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Messages Ready:</span>
                <span className="ml-2 text-lg font-bold text-green-600">
                  {attackListData.count}
                </span>
              </div>
              <div>
                <span className="font-medium">Total Contacts:</span>
                <span className="ml-2 text-lg font-bold">
                  {attackListData.metadata.totalReadyContacts}
                </span>
              </div>
              <div>
                <span className="font-medium">Smart Focus:</span>
                <Badge 
                  variant={attackListData.metadata.smartFocusEnabled ? "default" : "outline"}
                  className={attackListData.metadata.smartFocusEnabled ? "bg-blue-500" : ""}
                >
                  {attackListData.metadata.smartFocusEnabled ? "Enabled" : "Disabled"}
                </Badge>
              </div>
              <div>
                <span className="font-medium">API Time:</span>
                <span className="ml-2 text-xs">
                  {formatTime(attackListData.metadata.timestamp)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-red-800">
              <strong>Error:</strong> {error}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {isLoading && !attackListData && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              Loading attack list...
            </div>
          </CardContent>
        </Card>
      )}

      {/* Messages List */}
      {attackListData && attackListData.data.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Ready to Send ({attackListData.count})</h3>
          {attackListData.data.map((message, index) => (
            <Card key={message.id} className="border-l-4 border-l-green-500">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">
                    @{message.username}
                  </CardTitle>
                  <Badge className={getTypeColor(message.type)}>
                    {getTypeLabel(message.type)}
                  </Badge>
                </div>
                <CardDescription className="text-xs">
                  Contact ID: {message.id}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Main Message */}
                <div>
                  <span className="text-sm font-medium">Message:</span>
                  <div className="mt-1 p-2 bg-gray-50 rounded text-sm">
                    {message.message}
                  </div>
                </div>

                {/* Batch Messages */}
                {message.batchMessages && message.batchMessages.length > 0 && (
                  <div>
                    <span className="text-sm font-medium">
                      Batch Sequence ({message.batchMessages.length} messages):
                    </span>
                    <div className="mt-2 space-y-2">
                      {message.batchMessages.map((batch, bIndex) => (
                        <div key={bIndex} className="flex items-start gap-2">
                          <Badge variant="outline" className="text-xs">
                            {batch.sequenceNumber}
                          </Badge>
                          <div className="flex-1 p-2 bg-blue-50 rounded text-sm">
                            {batch.message}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Follow-up Info */}
                {message.followUpId && (
                  <div className="text-xs text-muted-foreground">
                    Follow-up ID: {message.followUpId} | Sequence: {message.sequenceNumber}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Empty State */}
      {attackListData && attackListData.data.length === 0 && (
        <Card>
          <CardContent className="pt-6 text-center">
            <div className="text-muted-foreground">
              <div className="text-4xl mb-2">📭</div>
              <div className="font-medium">No messages ready to send</div>
              <div className="text-sm mt-1">
                All contacts are either already processed or not yet ready for messaging.
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}