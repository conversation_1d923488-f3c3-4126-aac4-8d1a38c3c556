import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "../components/ui/button"
import { Progress } from "../components/ui/progress"
import { Card, CardContent, CardHeader, CardTitle } from "../components/ui/card"
import { Badge } from "../components/ui/badge"
import { StatusMonitor } from "../components/status-monitor"
import Browser from "webextension-polyfill"
import { MessageTypes } from "@shared/messaging"

export default function Home() {
  const [botRunning, setBotRunning] = useState(false)
  const [status, setStatus] = useState('')
  const [currentDMCount, setCurrentDMCount] = useState(0)
  const [dmBreakLimit, setDMBreakLimit] = useState(20)
  const [timeUntilNextDM, setTimeUntilNextDM] = useState(0)
  const [remainingFollowers, setRemainingFollowers] = useState(0)
  const [totalFollowers, setTotalFollowers] = useState(0)
  const [apiStatus, setApiStatus] = useState<any>({ isAuthenticated: false })
  const [pendingFollowUps, setPendingFollowUps] = useState(0)
  const [isCheckingScraping, setIsCheckingScraping] = useState(false)
  const [isPollingConversations, setIsPollingConversations] = useState(false)

  useEffect(() => {
    const updateStatus = () => {
      Browser.runtime.sendMessage({ type: MessageTypes.GET_STATUS }).then((status: any) => {
        setBotRunning(status.power);
        setStatus(status.status);

        // Update progress indicators
        setCurrentDMCount(status.currentDMCount || 0);
        setDMBreakLimit(status.dmBreakLimit || 20);
        setTimeUntilNextDM(status.timeUntilNextDM || 0);
        setRemainingFollowers(status.remainingFollowers || 0);
        setTotalFollowers(status.totalFollowers || 0);
      });
    };

    const updateApiStatus = () => {
      Browser.runtime.sendMessage({ type: MessageTypes.GET_API_STATUS }).then((status: any) => {
        setApiStatus(status || { isAuthenticated: false });
      });
    };

    const updateFollowUps = () => {
      Browser.runtime.sendMessage({ type: MessageTypes.FETCH_PENDING_FOLLOWUPS }).then((result: any) => {
        if (result.success && result.data) {
          setPendingFollowUps(result.data.length);
        } else {
          setPendingFollowUps(0);
        }
      });
    };

    updateStatus();
    updateApiStatus();
    updateFollowUps();

    const interval = setInterval(updateStatus, 1000);
    const apiInterval = setInterval(updateApiStatus, 30000); // Check API status every 30 seconds
    const followUpInterval = setInterval(updateFollowUps, 60000); // Check follow-ups every minute

    return () => {
      clearInterval(interval);
      clearInterval(apiInterval);
      clearInterval(followUpInterval);
    };
  }, [])

  const toggleBot = () => {
    // if(!botRunning) {
    run()
    // }
    setBotRunning(!botRunning)
  }

  const run = () => {
    // Browser.runtime.sendMessage({type: "testTabButton", data: {test: 'test'}})
    Browser.runtime.sendMessage(botRunning ? { type: MessageTypes.STOP_PROCESS } : { type: MessageTypes.START_PROCESS })
  }

  const formatTime = (seconds: number) => {
    if (seconds <= 0) return "0s";
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return mins > 0 ? `${mins}m ${secs}s` : `${secs}s`;
  };

  const triggerScrapingCheck = async () => {
    setIsCheckingScraping(true);
    try {
      // Send a message to background script to manually trigger scraping check
      await Browser.runtime.sendMessage({ type: MessageTypes.START_FOLLOWER_SCRAPING });
      console.log('✅ Manually triggered scraping check');
    } catch (error) {
      console.error('❌ Failed to trigger scraping check:', error);
    } finally {
      setIsCheckingScraping(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center h-full space-y-6 p-6">
      <h1 className="text-4xl font-bold mb-4">AISetter</h1>

      {/* Status Monitor */}
      <div className="w-full max-w-2xl">
        <StatusMonitor />
      </div>

      {/* API Status Card */}
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            API Connection
            {apiStatus.isAuthenticated ? (
              <Badge variant="default" className="bg-green-500">Connected</Badge>
            ) : (
              <Badge variant="destructive">Not Connected</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {apiStatus.isAuthenticated ? (
            <div className="space-y-3">
              <p className="text-sm">Organization: <span className="font-medium">{apiStatus.organizationName}</span></p>
              {pendingFollowUps > 0 && (
                <p className="text-sm">
                  Pending follow-ups: <span className="font-medium text-orange-500">{pendingFollowUps}</span>
                </p>
              )}
              <div className="pt-2 border-t">
                <Button 
                  onClick={triggerScrapingCheck} 
                  variant="outline" 
                  size="sm"
                  disabled={isCheckingScraping}
                  className="w-full"
                >
                  {isCheckingScraping ? "Checking..." : "🔍 Check Scraping Status"}
                </Button>
                <p className="text-xs text-muted-foreground mt-1 text-center">
                  Manually trigger a scraping condition check
                </p>
              </div>
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">
              Configure your API key in API Settings to enable follow-up functionality.
            </p>
          )}
        </CardContent>
      </Card>

      <Button onClick={toggleBot} variant={botRunning ? "destructive" : "default"} size="lg">
        {botRunning ? "STOP BOT" : "START BOT"}
      </Button>

      <div className="text-center">
        <p className="text-lg">
          Bot status:{" "}
          <span className={botRunning ? "text-green-500" : "text-red-500"}>
            {botRunning ? "Running" : "Stopped"}
          </span>
        </p>
        {status && <p className="text-sm text-gray-600 mt-1">{status}</p>}
      </div>

      {botRunning && (
        <div className="w-full max-w-2xl space-y-4">
          {/* DM Progress Card */}
          <Card>
            <CardHeader>
              <CardTitle>DM Progress</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>DMs sent before break</span>
                  <span>{currentDMCount} / {dmBreakLimit}</span>
                </div>
                <Progress value={currentDMCount} max={dmBreakLimit} />
              </div>

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Followers processed</span>
                  <span>{totalFollowers - remainingFollowers} / {totalFollowers}</span>
                </div>
                <Progress value={totalFollowers - remainingFollowers} max={totalFollowers} />
              </div>
            </CardContent>
          </Card>

          {/* Timing Card */}
          <Card>
            <CardHeader>
              <CardTitle>Timing</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-blue-500">{formatTime(timeUntilNextDM)}</p>
                  <p className="text-sm text-gray-600">Until next DM</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-500">{remainingFollowers}</p>
                  <p className="text-sm text-gray-600">Followers remaining</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

