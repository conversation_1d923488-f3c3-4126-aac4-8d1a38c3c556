
/* eslint-disable */

import { useEffect, useState } from "react"
import { Label } from "../components/ui/label"
import { Button } from "../components/ui/button"
import Browser from "webextension-polyfill"
import { MessageTypes } from "@shared/messaging"
import { RangeSlider } from "../components/ui/range_slider"
import { guard } from "@shared/common-utils"

export default function Settings() {
  const [timeBetweenDM, setTimeBetweenDM] = useState([1, 1])
  const [messageLineDelay, setMessageLineDelay] = useState([10, 30])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [reset, setReset] = useState(false);
  const [resetCache, setResetCache] = useState(false);
  const [lastSettingsSync, setLastSettingsSync] = useState(0)
  const [apiSettingsEnabled, setApiSettingsEnabled] = useState(false)

  useEffect(() => {
    Browser.runtime.sendMessage({
      type: MessageTypes.GET_SETTINGS
    }).then((settings) => {
      setTimeBetweenDM(guard(settings.timeBetweenDM, [1,1]));
      setMessageLineDelay(guard(settings.messageLineDelay, [10, 30]));
      setLastSettingsSync(settings.lastSettingsSync || 0);
      setApiSettingsEnabled(settings.apiSettingsEnabled || false);
      setIsLoading(false)
    })
  }, [])

  const validateSettings = () => {
    const errors = []

    if (timeBetweenDM[0] >= timeBetweenDM[1]) {
      errors.push("Time between DMs: minimum must be less than maximum")
    }
    if (messageLineDelay[0] >= messageLineDelay[1]) {
      errors.push("Message line delay: minimum must be less than maximum")
    }

    return errors
  }

  const handleSave = () => {
    const validationErrors = validateSettings()

    if (validationErrors.length > 0) {
      alert("Please fix the following errors:\n" + validationErrors.join("\n"))
      return
    }

    setIsSaving(true)
    console.log("Saving Auto DM settings:", { timeBetweenDM, messageLineDelay })
    Browser.runtime.sendMessage({
      type: MessageTypes.SAVE_SETTINGS,
      data: { timeBetweenDM, messageLineDelay }
    }).then(() => {
      setTimeout(() => {
        setIsSaving(false)
      }, 1000);
    })
  }

  const resetDMPool = () => {
    Browser.runtime.sendMessage({
        type: MessageTypes.RESET_DM_POOL
    });
    setReset(true);
    setTimeout(() => {
        setReset(false);
    }, 1000);
  }

  const resetNonZeroMessage = () => {
    Browser.runtime.sendMessage({
      type: MessageTypes.RESET_NONZERO_CACHE
  });
  setResetCache(true);
  setTimeout(() => {
      setResetCache(false);
  }, 1000);
  }

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold">Auto DM Settings</h1>

    {isLoading ? <div>Loading...</div> : <>
    
    {/* Settings Sync Status */}
    {apiSettingsEnabled && (
      <div className="space-y-2 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-800">🔧 Organization Settings Sync</h3>
        <p className="text-sm text-blue-600">
          Settings are automatically synced from your organization every 10 minutes.
        </p>
        {lastSettingsSync > 0 && (
          <p className="text-xs text-blue-500">
            Last sync: {new Date(lastSettingsSync).toLocaleString()}
          </p>
        )}
      </div>
    )}
    
    <div className="space-y-4">
    <Label>How long to wait between DM's? ({timeBetweenDM.join('-')} minutes)</Label>
    {apiSettingsEnabled ? (
      <div className="text-sm text-gray-600 italic">
        ℹ️ This setting is managed by your organization and synced automatically.
      </div>
    ) : (
      <RangeSlider value={timeBetweenDM} onValueChange={(value: number[]) => setTimeBetweenDM(value)} min={1} max={99} step={1} />
    )}
    </div>

    <div className="space-y-4">
    <Label>Delay between message lines? ({messageLineDelay.join('-')} seconds)</Label>
    {apiSettingsEnabled ? (
      <div className="text-sm text-gray-600 italic">
        ℹ️ Message line delay is managed locally and not synced from organization settings.
      </div>
    ) : null}
    <RangeSlider value={messageLineDelay} onValueChange={(value: number[]) => setMessageLineDelay(value)} min={1} max={120} step={1} />
    </div>
    <div className="space-y-4">
    <Label>Clear DM Pool? (if you do this it may resend the messages for the notifications you've not yet viewed)</Label>
    <br/>
    <Button onClick={resetDMPool}>{reset ? "Cleared!" : "Clear DM Pool"}</Button>
    </div>
    <div className="space-y-4">
    <Label>Clear Non-Zero message cache? (if you do this it may remake this if you use only zero message setting.)</Label>
    <br/>
    <Button onClick={resetNonZeroMessage}>{resetCache ? "Cleared!" : "Clear Non-Zero Message Cache"}</Button>
    </div>
    </>}

      <Button onClick={handleSave} disabled={isSaving || isLoading}>{isSaving ? "Saving..." : "Save Settings"}</Button>
    </div>
  )
}


/* eslint-disable */