/* eslint-disable */

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "../components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card"
import { Badge } from "../components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../components/ui/table"
import { Switch } from "../components/ui/switch"
import { Label } from "../components/ui/label"
import Browser from "webextension-polyfill"
import { MessageTypes } from "@shared/messaging"

interface PendingFollowUp {
  id: string;
  contactId: string;
  recipientId: string;
  username: string;
  message: string;
  scheduledTime: string;
  followUpNumber: number;
}

export default function FollowUps() {
  const [followUps, setFollowUps] = useState<PendingFollowUp[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<string>("")
  const [apiStatus, setApiStatus] = useState<any>({ isAuthenticated: false })
  const [autoDMEnabled, setAutoDMEnabled] = useState(true)

  useEffect(() => {
    loadFollowUps()
    loadApiStatus()
    loadAutoDMStatus()
  }, [])

  const loadApiStatus = async () => {
    try {
      const status = await Browser.runtime.sendMessage({
        type: MessageTypes.GET_API_STATUS
      })
      setApiStatus(status || { isAuthenticated: false })
    } catch (error) {
      console.error('Error loading API status:', error)
    }
  }

  const loadAutoDMStatus = async () => {
    try {
      const result = await Browser.runtime.sendMessage({
        type: MessageTypes.GET_AUTO_DM_STATUS
      })
      setAutoDMEnabled(result.enabled)
    } catch (error) {
      console.error('Error loading auto-DM status:', error)
    }
  }

  const handleAutoDMToggle = async (enabled: boolean) => {
    try {
      await Browser.runtime.sendMessage({
        type: MessageTypes.SET_AUTO_DM_ENABLED,
        data: { enabled }
      })
      setAutoDMEnabled(enabled)
    } catch (error) {
      console.error('Error setting auto-DM status:', error)
    }
  }

  const loadFollowUps = async () => {
    try {
      setIsLoading(true)

      const result = await Browser.runtime.sendMessage({
        type: MessageTypes.FETCH_PENDING_FOLLOWUPS
      })

      if (result.success && result.data) {
        setFollowUps(result.data)
        setLastUpdated(new Date().toLocaleString())
      } else {
        console.error('Error fetching follow-ups:', result.message)
        setFollowUps([])
      }
    } catch (error) {
      console.error('Error loading follow-ups:', error)
      setFollowUps([])
    } finally {
      setIsLoading(false)
    }
  }

  const handleRefresh = async () => {
    setIsRefreshing(true)
    await loadFollowUps()
    setIsRefreshing(false)
  }

  const handleSendFollowUp = async (followUp: PendingFollowUp) => {
    try {
      console.log('🚨 DEBUG: Manual follow-up send started for:', followUp.username)

      const result = await Browser.runtime.sendMessage({
        type: MessageTypes.SEND_FOLLOWUP_MESSAGE,
        data: {
          recipientId: followUp.recipientId,
          username: followUp.username,
          message: followUp.message,
          followUpId: followUp.id // ✅ ADD MISSING followUpId
        }
      })

      console.log('🚨 DEBUG: Manual follow-up send result:', result)

      if (result.success) {
        // ✅ No need for separate API call - FollowUpManager handles it automatically
        console.log('🚨 DEBUG: Follow-up sent successfully, API call handled by FollowUpManager')

        // Refresh the list
        await loadFollowUps()
        alert(`Follow-up sent to ${followUp.username}`)
      } else {
        // ✅ No need for separate API call - FollowUpManager handles it automatically
        console.log('🚨 DEBUG: Follow-up failed, API call handled by FollowUpManager')
        alert(`Failed to send follow-up to ${followUp.username}: ${result.message}`)
      }
    } catch (error) {
      console.error('Error sending follow-up:', error)
      alert(`Error sending follow-up: ${error}`)
    }
  }

  const formatScheduledTime = (timeString: string) => {
    try {
      return new Date(timeString).toLocaleString()
    } catch {
      return timeString
    }
  }

  if (!apiStatus.isAuthenticated) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Follow-ups</h1>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">
                You need to configure your API key to view follow-ups.
              </p>
              <p className="text-sm text-muted-foreground">
                Go to API Settings to set up your connection to AISetter.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Follow-ups</h1>
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {followUps.length} pending
          </Badge>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            {isRefreshing ? "Refreshing..." : "Refresh"}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Pending Follow-up Messages</CardTitle>
              <CardDescription>
                Follow-up messages scheduled beyond the 24-hour Instagram window.
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="auto-dm"
                checked={autoDMEnabled}
                onCheckedChange={handleAutoDMToggle}
              />
              <Label htmlFor="auto-dm" className="text-sm">
                Auto-DM {autoDMEnabled ? 'ON' : 'OFF'}
              </Label>
            </div>
          </div>
          {autoDMEnabled && (
            <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-sm text-green-800">
                ✅ Auto-DM is enabled. Follow-ups will be sent automatically when their scheduled time arrives.
              </p>
            </div>
          )}
          {!autoDMEnabled && (
            <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                ⚠️ Auto-DM is disabled. You'll need to send follow-ups manually using the "Send Now" button.
              </p>
            </div>
          )}
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <p>Loading follow-ups...</p>
            </div>
          ) : followUps.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No pending follow-ups found.</p>
              <p className="text-sm text-muted-foreground mt-2">
                Follow-ups will appear here when they're scheduled beyond the 24-hour Instagram messaging window.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Username</TableHead>
                    <TableHead>Follow-up #</TableHead>
                    <TableHead>Message</TableHead>
                    <TableHead>Scheduled</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {followUps.map((followUp) => (
                    <TableRow key={followUp.id}>
                      <TableCell className="font-medium">
                        {followUp.username}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          #{followUp.followUpNumber}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs truncate" title={followUp.message}>
                          {followUp.message}
                        </div>
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {formatScheduledTime(followUp.scheduledTime)}
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          onClick={() => handleSendFollowUp(followUp)}
                        >
                          Send Now
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {lastUpdated && (
            <div className="text-xs text-muted-foreground mt-4">
              Last updated: {lastUpdated}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>How it works</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground space-y-2">
            <p>• Instagram only allows messaging within 24 hours of the last interaction</p>
            <p>• Follow-ups scheduled beyond this window appear here</p>
            <p>• The extension automatically checks for new follow-ups every minute</p>
            <p>• <strong>Auto-DM ON:</strong> Follow-ups are sent automatically when their time arrives</p>
            <p>• <strong>Auto-DM OFF:</strong> Click "Send Now" to manually deliver messages</p>
            <p>• All sent messages are automatically marked as completed in your dashboard</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/* eslint-enable */
