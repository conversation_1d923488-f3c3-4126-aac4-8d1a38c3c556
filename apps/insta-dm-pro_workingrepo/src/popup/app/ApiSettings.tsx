/* eslint-disable */

import { useEffect, useState } from "react"
import { Label } from "../components/ui/label"
import { Button } from "../components/ui/button"
import { Input } from "../components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card"
import { Badge } from "../components/ui/badge"
import Browser from "webextension-polyfill"
import { MessageTypes } from "@shared/messaging"

interface ApiStatus {
  isAuthenticated: boolean;
  organizationName?: string;
  organizationSlug?: string;
  lastVerified?: string;
  error?: string;
}

export default function ApiSettings() {
  const [apiKey, setApiKey] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [apiStatus, setApiStatus] = useState<ApiStatus>({ isAuthenticated: false })
  const [showApiKey, setShowApiKey] = useState(false)

  useEffect(() => {
    loadApiSettings()
  }, [])

  const loadApiSettings = async () => {
    try {
      setIsLoading(true)

      // Get API key from background script
      const storedApiKey = await Browser.runtime.sendMessage({
        type: MessageTypes.GET_API_KEY
      })

      if (storedApiKey) {
        setApiKey(storedApiKey)
        // Get API status
        const status = await Browser.runtime.sendMessage({
          type: MessageTypes.GET_API_STATUS
        })
        setApiStatus(status || { isAuthenticated: false })
      }
    } catch (error) {
      console.error('Error loading API settings:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveApiKey = async () => {
    if (!apiKey.trim()) {
      alert("Please enter an API key")
      return
    }

    try {
      setIsSaving(true)

      // Save API key via background script
      await Browser.runtime.sendMessage({
        type: MessageTypes.SAVE_API_KEY,
        data: { apiKey: apiKey.trim() }
      })

      // Verify the API key
      await handleVerifyApiKey()

    } catch (error) {
      console.error('Error saving API key:', error)
      alert('Failed to save API key')
    } finally {
      setIsSaving(false)
    }
  }

  const handleVerifyApiKey = async () => {
    if (!apiKey.trim()) {
      alert("Please enter an API key")
      return
    }

    try {
      setIsVerifying(true)

      const result = await Browser.runtime.sendMessage({
        type: MessageTypes.VERIFY_API_KEY,
        data: { apiKey: apiKey.trim() }
      })

      setApiStatus(result)

      if (result.isAuthenticated) {
        alert(`Successfully authenticated with organization: ${result.organizationName}`)
      } else {
        alert(`Authentication failed: ${result.error || 'Invalid API key'}`)
      }
    } catch (error) {
      console.error('Error verifying API key:', error)
      alert('Failed to verify API key')
    } finally {
      setIsVerifying(false)
    }
  }

  const handleClearApiKey = async () => {
    if (confirm('Are you sure you want to clear the API key?')) {
      try {
        setApiKey("")
        await Browser.runtime.sendMessage({
          type: MessageTypes.SAVE_API_KEY,
          data: { apiKey: "" }
        })
        setApiStatus({ isAuthenticated: false })
      } catch (error) {
        console.error('Error clearing API key:', error)
      }
    }
  }



  if (isLoading) {
    return <div className="p-4">Loading API settings...</div>
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">API Settings</h1>

      <Card>
        <CardHeader>
          <CardTitle>AISetter API Key</CardTitle>
          <CardDescription>
            Connect your Chrome extension to your AISetter dashboard to enable follow-up message delivery.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* API Status */}
          <div className="flex items-center gap-2">
            <Label>Status:</Label>
            {apiStatus.isAuthenticated ? (
              <Badge variant="default" className="bg-green-500">
                Connected to {apiStatus.organizationName}
              </Badge>
            ) : (
              <Badge variant="destructive">
                Not Connected
              </Badge>
            )}
          </div>

          {apiStatus.error && (
            <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
              {apiStatus.error}
            </div>
          )}

          {/* API Key Input */}
          <div className="space-y-2">
            <Label htmlFor="apiKey">API Key</Label>
            <div className="flex gap-2">
              <Input
                id="apiKey"
                type={showApiKey ? "text" : "password"}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your API key from AISetter dashboard"
                className="font-mono"
              />
              <Button
                variant="outline"
                onClick={() => setShowApiKey(!showApiKey)}
                type="button"
              >
                {showApiKey ? "Hide" : "Show"}
              </Button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleSaveApiKey}
              disabled={isSaving || isVerifying || !apiKey.trim()}
            >
              {isSaving ? "Saving..." : "Save API Key"}
            </Button>

            <Button
              variant="outline"
              onClick={handleVerifyApiKey}
              disabled={isSaving || isVerifying || !apiKey.trim()}
            >
              {isVerifying ? "Verifying..." : "Verify"}
            </Button>

            {apiKey && (
              <Button
                variant="destructive"
                onClick={handleClearApiKey}
                disabled={isSaving || isVerifying}
              >
                Clear
              </Button>
            )}
          </div>

          {/* Instructions */}
          <div className="rounded-md bg-muted p-4">
            <h4 className="mb-2 text-sm font-medium">How to get your API key</h4>
            <ol className="list-decimal pl-4 text-sm text-muted-foreground space-y-1">
              <li>Go to your AISetter dashboard at app.aisetter.pl</li>
              <li>Navigate to Instagram Settings</li>
              <li>Find the "Chrome Extension API Key" section</li>
              <li>Click "Generate API Key" and copy it</li>
              <li>Paste the API key here and click "Save API Key"</li>
            </ol>
          </div>

          {apiStatus.lastVerified && (
            <div className="text-xs text-muted-foreground">
              Last verified: {new Date(apiStatus.lastVerified).toLocaleString()}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

/* eslint-enable */
