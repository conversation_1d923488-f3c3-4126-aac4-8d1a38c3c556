import { Home, MessageCircle, Repeat, MessageSquare, Settings, Key, Lock, /*MessagesSquare*/ } from "lucide-react"
import { cn } from "../lib/utils"
import { useState, useEffect } from "react"
import Browser from "webextension-polyfill"

const sidebarItems = [
  { icon: Home, href: "/", label: "Home", requiresApi: true },
  { icon: MessageCircle, href: "/auto-dm", label: "Auto DM", requiresApi: true },
  { icon: Repeat, href: "/follow-ups", label: "Follow-ups", requiresApi: true },
  { icon: MessageSquare, href: "/auto-dm-messages", label: "Auto DM Messages", requiresApi: true },
  { icon: Key, href: "/api-settings", label: "API Settings", requiresApi: false },
  { icon: Settings, href: "/settings", label: "Settings", requiresApi: true },
  // { icon: MessagesSquare, href: "/follow-ups-messages", label: "Follow-ups Messages", requiresApi: true },
]

export function Sidebar({ activePathName, changeActivePathName }: { activePathName: string, changeActivePathName: (path: string) => void }) {
  const [isApiAuthenticated, setIsApiAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const pathname = activePathName;

  // Check API authentication status
  useEffect(() => {
    const checkApiStatus = async () => {
      try {
        // Get API status from Browser storage
        const result = await Browser.storage.local.get(['apiStatus'])
        const apiStatus = result.apiStatus
        setIsApiAuthenticated(apiStatus?.isAuthenticated || false)
      } catch (error) {
        console.error('Error checking API status:', error)
        setIsApiAuthenticated(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkApiStatus()

    // Listen for API status changes
    const handleStorageChange = (changes: any) => {
      if (changes.apiStatus) {
        setIsApiAuthenticated(changes.apiStatus.newValue?.isAuthenticated || false)
      }
    }

    Browser.storage.onChanged.addListener(handleStorageChange)
    return () => Browser.storage.onChanged.removeListener(handleStorageChange)
  }, [])

  // Redirect to API settings if not authenticated and trying to access protected route
  useEffect(() => {
    if (!isLoading && !isApiAuthenticated && pathname !== "/api-settings") {
      changeActivePathName("/api-settings")
    }
  }, [isApiAuthenticated, isLoading, pathname, changeActivePathName])

  const handleNavigation = (href: string, requiresApi: boolean) => {
    if (requiresApi && !isApiAuthenticated) {
      // Show tooltip or redirect to API settings
      changeActivePathName("/api-settings")
      return
    }
    changeActivePathName(href)
  }

  return (
    <aside className="flex h-screen w-16 flex-col items-center space-y-8 bg-gray-900 py-8">
      {sidebarItems.map(({ icon: Icon, href, label, requiresApi }) => {
        const isLocked = requiresApi && !isApiAuthenticated
        const IconComponent = isLocked ? Lock : Icon

        return (
          <div key={href} className="relative group">
            <a onClick={() => handleNavigation(href, requiresApi)}>
              <div
                className={cn(
                  "flex h-12 w-12 items-center justify-center rounded-lg transition-colors",
                  isLocked
                    ? "text-gray-600 cursor-not-allowed"
                    : "text-gray-400 hover:bg-gray-800 hover:text-white cursor-pointer",
                  pathname === href && !isLocked && "bg-gray-800 text-white",
                )}
              >
                <IconComponent className="h-6 w-6" />
                <span className="sr-only">{label}</span>
              </div>
            </a>
            {isLocked && (
              <div className="absolute left-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                API key required
              </div>
            )}
          </div>
        )
      })}

      {!isApiAuthenticated && !isLoading && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-red-600 text-white text-xs px-2 py-1 rounded text-center">
          <div className="text-xs font-semibold">🔒 LOCKED</div>
          <div className="text-xs">Add API Key</div>
        </div>
      )}
    </aside>
  )
}

