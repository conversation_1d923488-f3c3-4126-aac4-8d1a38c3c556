# Instagram Follower Scraping Process Documentation

## Overview

This document describes the complete follower scraping workflow for the Instagram Chrome extension, including status management and API integration.

## Process Flow

### 1. Initial Status Check

- Extension checks app status via API
- Possible statuses:
  - `NOT_STARTED_YET` - No scraping has been done
  - `NOT_SCRAPED_YET` - Ready to scrape 250 followers
  - `SCRAPED_250` - 250 followers scraped and sent to app
  - `ALL_SCRAPED` - All available followers scraped (reached bottom)

### 2. Scraping Logic

#### For Status: `NOT_STARTED_YET` or `NOT_SCRAPED_YET`

1. **Target**: Scrape exactly 250 followers (or all available if less than 250)
2. **Process**:
   - Open followers dialog
   - Scroll and collect follower data
   - Continue until 250 followers OR reach bottom
3. **Result Handling**:
   - If 250+ followers found: Send 250 to API → Status becomes `SCRAPED_250`
   - If <250 followers found (reached bottom): Send all to API → Status becomes `ALL_SCRAPED`

#### For Status: `SCRAPED_250` (Resume Scraping)

1. **Target**: Continue from last position, scrape next 250
2. **Process**:
   - Get last 10 scraped usernames from API (for position detection)
   - Find approximate position in followers list
   - Continue scraping from that position
   - Collect next 250 followers OR until bottom
3. **Result Handling**:
   - If 250+ more followers found: Send 250 to API → Status remains `SCRAPED_250`
   - If <250 more followers found (reached bottom): Send remaining to API → Status becomes `ALL_SCRAPED`

### 3. API Integration

#### Status Check Endpoint

```
GET /api/instagram-scraping-status
Response: { status: "NOT_STARTED_YET" | "NOT_SCRAPED_YET" | "SCRAPED_250" | "ALL_SCRAPED" }
```

#### Send Followers Endpoint

```
POST /api/followers
Body: {
  followers: [
    {
      instagramNickname: string,
      instagramId?: string,
      avatar?: string,
      followerCount?: number,
      isVerified: boolean
    }
  ],
  isComplete: boolean, // true if reached bottom (ALL_SCRAPED)
  totalScraped: number
}
```

#### Get Last Scraped Endpoint (for resume)

```
GET /api/last-scraped-followers?limit=10
Response: {
  followers: [{ instagramNickname: string, ... }]
}
```

### 4. Position Detection for Resume

When resuming from `SCRAPED_250` status:

1. Get last 10 scraped usernames from API
2. Scroll through followers list looking for matches
3. Find the position where most recent usernames appear
4. Account for potential unfollows (some users might not be found)
5. Start scraping from estimated position + small buffer

### 5. Error Handling

- **Network Errors**: Retry API calls with exponential backoff
- **DOM Changes**: Fallback to alternative selectors
- **Position Detection Failures**: Start from beginning with skip logic
- **Partial Scraping**: Save progress and allow resume

### 6. Robustness Features

- **Duplicate Prevention**: Check against already scraped usernames
- **Progress Persistence**: Save scraping state locally
- **Retry Logic**: Handle temporary Instagram loading issues
- **Rate Limiting**: Human-like scrolling delays

## Technical Implementation

### Key Functions

1. `checkScrapingStatus()` - Check current status from API
2. `scrapeFollowersBatch(targetCount, startPosition)` - Core scraping logic
3. `sendFollowersToAPI(followers, isComplete)` - Send results to API
4. `findResumePosition(lastScrapedUsernames)` - Find position for resume
5. `updateScrapingStatus(newStatus)` - Update status in API

### Status Transitions

```
NOT_STARTED_YET → SCRAPED_250 (if 250+ followers)
NOT_STARTED_YET → ALL_SCRAPED (if <250 followers)
NOT_SCRAPED_YET → SCRAPED_250 (if 250+ followers)
NOT_SCRAPED_YET → ALL_SCRAPED (if <250 followers)
SCRAPED_250 → SCRAPED_250 (continue scraping, 250+ more found)
SCRAPED_250 → ALL_SCRAPED (reached bottom)
ALL_SCRAPED → [final state]
```

## Configuration

- **Batch Size**: 250 followers per batch
- **Resume Buffer**: Look for last 10 usernames
- **Scroll Delay**: 1500ms between scrolls
- **API Timeout**: 15 seconds
- **Retry Attempts**: 3 times with exponential backoff

## Monitoring & Debugging

- All actions logged with timestamps
- Progress tracking with detailed metrics
- Error logging with context
- Performance monitoring (scraping speed, API response times)

## Future Enhancements

- **Smart Resume**: Better position detection algorithms
- **Batch Optimization**: Dynamic batch sizes based on performance
- **Parallel Processing**: Multiple scraping sessions
- **Data Enrichment**: Additional follower metadata
