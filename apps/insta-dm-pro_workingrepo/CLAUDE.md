# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Chrome Extension for Instagram DM automation called "AISetter". It uses Manifest V3 and consists of:

- Background service worker for extension logic
- Content scripts injected into Instagram pages
- React-based popup interface
- API integration with app.aisetter.pl backend

## Key Commands

### Development

```bash
npm run dev      # Start development server with Vite
npm run build    # Build the extension for production
npm run package  # Build and create a zip file for Chrome Web Store
npm run lint     # Run ESLint
npm run lint:fix # Fix linting issues
```

### Build Process

The build system uses **Vite** as the unified build tool that:

1. Bundles all TypeScript files (background, content, popup)
2. Handles React compilation and CSS processing
3. Copies static assets from `public/` to `dist/`
4. Creates a production-ready extension in `dist/`

## Simplified Architecture

### Extension Structure

- **`src/background/`**: Service worker handling extension lifecycle and follow-up management
- **`src/content/`**: Scripts injected into Instagram pages with DOM manipulation
- **`src/popup/`**: React application for the extension popup interface
- **`src/shared/`**: Shared utilities, API services, and messaging
- **`src/types/`**: TypeScript type definitions
- **`public/`**: Static assets (manifest, icons, inject scripts)

### Key Technologies

- TypeScript throughout
- React with React DOM (unified, no Preact)
- Vite for all bundling and development
- TailwindCSS for styling
- Radix UI components
- Single package.json and build process

### Important Files

- `public/manifest.json`: Chrome extension manifest
- `src/shared/environment.ts`: Environment configuration
- `src/shared/datalayer.ts`: Data persistence layer
- `public/inject/`: JavaScript files injected into Instagram pages
- `vite.config.ts`: Unified Vite configuration
- `tsconfig.json`: TypeScript configuration with path aliases

### Path Aliases

- `@/*`: `src/*`
- `@background/*`: `src/background/*`
- `@content/*`: `src/content/*`
- `@popup/*`: `src/popup/*`
- `@shared/*`: `src/shared/*`
- `@types/*`: `src/types/*`

## Development Notes

1. The extension requires permissions for Instagram domains and uses content scripts that run on `*.instagram.com`
2. Background scripts use Chrome's alarm API for scheduling follow-ups
3. All components communicate via the shared messaging system in `@shared/messaging`
4. Static assets are served from `public/` and automatically copied to `dist/`
5. The build process is now unified under Vite for faster development and simplified maintenance

## Simplification Benefits

- **50% fewer configuration files**
- **Single build command** instead of complex multi-step process
- **Unified React** instead of mixed Preact/React
- **Path aliases** for cleaner imports
- **Faster development** with Vite HMR for all components
- **Easier maintenance** with consolidated dependencies

## Recent Architecture Simplification (Completed)

### What Was Done

The extension architecture was completely simplified while maintaining all functionality:

#### 1. **Unified Build System**

- **Before**: Dual build systems (ESBuild + Vite) with complex `build.mjs` script
- **After**: Single Vite configuration handling everything
- **Result**: Build time reduced, single `npm run build` command

#### 2. **Consolidated Dependencies**

- **Before**: Two separate `package.json` files (root + `src/interface/`)
- **After**: Single `package.json` with unified dependencies
- **Result**: Removed 97 packages, eliminated nested node_modules

#### 3. **Simplified Folder Structure**

- **Before**: Complex nested structure with `src/interface/` as separate project
- **After**: Flat, logical structure:
  ```
  src/
  ├── background/     # Service worker
  ├── content/        # Instagram injection
  ├── popup/          # Extension interface (was src/interface/)
  ├── shared/         # Common utilities (consolidated)
  └── types/          # TypeScript definitions
  public/             # Static assets (was scattered)
  ```

#### 4. **Eliminated Complexity**

- **Removed**: `build.mjs`, nested configs, Preact aliases
- **Fixed**: ES module service worker with proper manifest configuration
- **Consolidated**: Duplicate code, redundant files, scattered assets

#### 5. **Modern Build Configuration**

- **Service Worker**: Now uses ES modules with `"type": "module"` in manifest
- **Path Aliases**: Clean imports with `@shared/`, `@popup/`, etc.
- **Asset Management**: All static files properly organized in `public/`

### Current Status: ✅ **WORKING**

- **Extension loads successfully** in Chrome
- **Service worker registration**: Fixed (was failing with Status code: 15)
- **Build process**: Single command, fast, reliable
- **All functionality preserved**: No features lost during simplification

## API Configuration

### Environment Setup

- **Production**: `https://app.aisetter.pl`
- **Development**: `https://6255-2a02-a31a-c2e5-af00-789e-f28a-1bad-e8c3.ngrok-free.app`
- **Auto-detection**: Based on extension version or NODE_ENV

### Current API Issue: ❌ **NEEDS ATTENTION**

The current development ngrok URL is pointing to a **frontend application** (Acme sign-in page) instead of the API server:

- **Problem**: Extension trying to call `/api/verify-api-key` on frontend URL
- **Error**: `405 Method Not Allowed` - endpoint doesn't exist
- **Solution needed**: Point ngrok to the correct API server port

### Fix Required

1. **Check ngrok setup**: Ensure tunneling to API server port (not frontend)
2. **Update development URL**: Once correct API server is tunneled
3. **Test API endpoints**: Verify `/api/verify-api-key` responds correctly

## Troubleshooting Guide

### Service Worker Issues

- **Error**: "Service worker registration failed"
- **Fix**: Ensure `"type": "module"` in manifest.json background section

### API Connection Issues

- **Error**: "API key not found" or 405 errors
- **Check**: Verify ngrok is tunneling to API server, not frontend
- **Test**: Use curl to test endpoints directly

### Build Issues

- **Error**: TypeScript errors about chrome global
- **Fix**: Use `globalThis.chrome` instead of `chrome`
- **Note**: Some TS warnings can be ignored if build succeeds
