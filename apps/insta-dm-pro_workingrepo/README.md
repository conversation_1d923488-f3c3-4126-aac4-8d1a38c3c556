# AISetter Chrome Extension

## Development Setup

### ✅ Current Configuration
- **API URL**: `http://localhost:3000` (development)
- **API Key**: `api_b777c538fad684d76b261053485ce620`
- **Status**: Ready for development

### 🚀 Quick Start

1. **Start the dashboard API server**:
   ```bash
   cd /Users/<USER>/Documents/GitHub/ai-setter
   pnpm --filter dashboard dev
   ```

2. **Build the extension**:
   ```bash
   npm run build
   ```

3. **Load in Chrome**:
   - Go to `chrome://extensions/`
   - Enable "Developer mode" 
   - Click "Load unpacked"
   - Select the `dist/` folder

### 🔧 Environment Configuration

**For Development (localhost)**:
```typescript
// src/shared/environment.ts
export const environment: Environment = {
  apiBaseUrl: 'http://localhost:3000',
  isDevelopment: true,
  version: '1.0.1'
};
```

**For Production**:
```typescript
// src/shared/environment.ts
export const environment: Environment = {
  apiBaseUrl: 'https://app.aisetter.pl',
  isDevelopment: false,
  version: '1.0.1'
};
```

### 🧪 Testing API

Run the test script to verify API connections:
```bash
node test-api-simple.mjs
```

### 📝 Notes

- **No ngrok needed** for development - uses localhost directly
- Extension has localhost permissions in manifest
- All API endpoints are working with CORS headers
- Remember to rebuild after environment changes

### 🔄 Switching Environments

1. Edit `src/shared/environment.ts`
2. Run `npm run build`  
3. Reload extension in Chrome (`chrome://extensions/` → reload button)