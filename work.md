# AISetter Project Overview

## Current Components

- **Dashboard** (apps/dashboard): Main application interface
- **Instagram DM Pro** (apps/insta-dm-pro): Instagram messaging automation
- **Public API** (apps/public-api): External API endpoints
- **Marketing** (apps/marketing): Marketing website
- **Prisma Studio** (apps/prisma-studio): Database management interface
- **React Email Preview** (apps/react-email-preview): Email template preview system
- **Requirements Check** (apps/requirements-check): System requirements verification

## Core Packages

- **Instagram** (packages/instagram): Instagram API integration
- **Instagram Bot** (packages/instagram-bot): Conversation automation logic
- **Database** (packages/database): Database models and migrations

## Tech Stack

- Next.js App Router
- pnpm (Package Manager)
- TypeScript
- Prisma (Database ORM)
- Claude 3.7 Sonnet API (AI Integration)

## Main Features

- Instagram DM automation
- AI-powered conversation handling
- Contact management
- Prompt management
- Automated follow-ups

## Deployment

- GitHub Actions for CI/CD
- Production URL: https://app.aisetter.pl
