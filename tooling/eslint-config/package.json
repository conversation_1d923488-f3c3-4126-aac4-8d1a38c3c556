{"name": "@workspace/eslint-config", "version": "0.0.0", "private": true, "type": "module", "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "typecheck": "tsc --noEmit"}, "devDependencies": {"@eslint/js": "9.21.0", "@next/eslint-plugin-next": "15.2.1", "@typescript-eslint/eslint-plugin": "8.26.0", "@typescript-eslint/parser": "8.26.0", "eslint": "9.21.0", "eslint-config-prettier": "10.0.2", "eslint-plugin-only-warn": "1.1.0", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-turbo": "2.4.4", "globals": "16.0.0", "typescript": "5.7.2", "typescript-eslint": "8.26.0"}, "exports": {"./base": "./base.js", "./next": "./next.js", "./react-internal": "./react-internal.js"}}