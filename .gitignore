# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# Build Outputs
.next/
out/
build
dist

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
!.env.example

# vercel
.vercel
.turbo

# typescript
*.tsbuildinfo

# Content Collections
.content-collections

# Ide
.idea/

# Package files
package.json
package-lock.json
yarn.lock
pnpm-lock.yaml

# Database
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
.cache/

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Chrome extension builds
*.zip
*.crx
*.pem

# Backup files
*.bak
*.backup
