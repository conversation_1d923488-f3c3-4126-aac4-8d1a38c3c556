name: Restart Application

on:
  workflow_dispatch:

jobs:
  restart:
    runs-on: ubuntu-latest
    steps:
      - name: Restart application
        uses: appleboy/ssh-action@master
        with:
          host: **************
          username: root
          password: biFdptij7UPpsniEVRuj
          script: |
            # Stop the current application
            pm2 stop dashboard-dev || true

            # Start the application
            cd /var/www/aisetter && pm2 start --name dashboard-dev 'NODE_ENV=production HUSKY=0 pnpm --filter dashboard start'

            # Restart Nginx to ensure proper proxy configuration
            systemctl restart nginx
