{"name": "@workspace/ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@lexical/react": "0.27.1", "@radix-ui/react-accordion": "1.2.3", "@radix-ui/react-alert-dialog": "1.1.6", "@radix-ui/react-aspect-ratio": "1.1.2", "@radix-ui/react-avatar": "1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "1.1.3", "@radix-ui/react-context-menu": "2.2.6", "@radix-ui/react-dialog": "1.1.6", "@radix-ui/react-dropdown-menu": "2.1.6", "@radix-ui/react-hover-card": "1.1.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "1.1.6", "@radix-ui/react-navigation-menu": "1.2.5", "@radix-ui/react-popover": "1.1.6", "@radix-ui/react-portal": "1.1.4", "@radix-ui/react-progress": "1.1.2", "@radix-ui/react-radio-group": "1.2.3", "@radix-ui/react-scroll-area": "1.2.3", "@radix-ui/react-select": "2.1.6", "@radix-ui/react-separator": "1.1.2", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "1.2.6", "@radix-ui/react-toggle": "1.1.2", "@radix-ui/react-toggle-group": "1.1.2", "@radix-ui/react-tooltip": "1.1.8", "@tanstack/react-table": "8.21.2", "@workspace/common": "workspace:*", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "3.6.0", "embla-carousel-autoplay": "8.5.2", "embla-carousel-react": "8.5.2", "emoji-picker-react": "4.12.0", "framer-motion": "12.4.10", "input-otp": "1.4.2", "lexical": "0.27.1", "lucide-react": "0.477.0", "next": "15.2.1", "next-themes": "0.4.4", "react": "19.0.0", "react-accessible-treeview": "2.11.0", "react-day-picker": "8.10.1", "react-dom": "19.0.0", "react-dropzone": "14.3.8", "react-hook-form": "7.54.2", "react-image-crop": "11.0.7", "react-is": "19.0.0", "react-remove-scroll": "2.6.3", "react-resizable-panels": "2.1.7", "recharts": "2.15.1", "sonner": "2.0.1", "tailwind-merge": "3.0.2", "vaul": "1.1.2"}, "devDependencies": {"@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/tailwind-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "autoprefixer": "10.4.20", "postcss": "8.5.3", "tailwindcss": "3.4.17", "tailwindcss-animate": "1.0.7"}, "prettier": "@workspace/prettier-config", "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./tailwind.config": "./tailwind.config.ts", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}}