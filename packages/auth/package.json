{"name": "@workspace/auth", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/prisma-adapter": "2.7.2", "@t3-oss/env-nextjs": "0.12.0", "@workspace/common": "workspace:*", "@workspace/database": "workspace:*", "@workspace/email": "workspace:*", "@workspace/image-processing": "workspace:*", "@workspace/rate-limit": "workspace:*", "@workspace/routes": "workspace:*", "bcryptjs": "2.4.3", "date-fns": "3.6.0", "next": "15.2.1", "next-auth": "5.0.0-beta.25", "react": "19.0.0", "uuid": "11.1.0", "zod": "3.24.2"}, "devDependencies": {"@types/bcryptjs": "2.4.6", "@types/node": "22.13.9", "@types/react": "19.0.10", "@types/uuid": "10.0.0", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "prettier": "@workspace/prettier-config", "exports": {"./keys": "./keys.ts", "./adapter": "./src/adapter.ts", "./callbacks": "./src/callbacks.ts", "./client-context": "./src/client-context.ts", "./constants": "./src/constants.ts", "./context": "./src/context-app-router.ts", "./context-legacy": "./src/context.ts", "./cookies": "./src/cookies.ts", "./encryption": "./src/encryption.ts", "./errors": "./src/errors.ts", "./events": "./src/events.ts", "./headers-helper": "./src/headers-helper.ts", ".": "./src/index.ts", "./invitations": "./src/invitations.ts", "./password": "./src/password.ts", "./permissions": "./src/permissions.ts", "./providers": "./src/providers.ts", "./providers.types": "./src/providers.types.ts", "./redirect": "./src/redirect.ts", "./schemas": "./src/schemas.ts", "./server-actions": "./src/server-actions.ts", "./server-context": "./src/server-context.ts", "./session": "./src/session.ts", "./verification": "./src/verification.ts"}}