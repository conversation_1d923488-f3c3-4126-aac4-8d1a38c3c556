import axios from 'axios';
export class OpenRouterClient {
    client;
    apiKey;
    constructor(apiKey, options) {
        this.apiKey = apiKey;
        this.client = axios.create({
            baseURL: options?.baseURL || 'https://openrouter.ai/api/v1',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                ...(options?.appUrl && { 'HTTP-Referer': options.appUrl }),
                ...(options?.appName && { 'X-Title': options.appName }),
            },
            timeout: 120000, // 2 minutes timeout for AI requests
        });
    }
    /**
     * Fetch all available models from OpenRouter
     */
    async getModels() {
        try {
            const response = await this.client.get('/models');
            return response.data.data;
        }
        catch (error) {
            if (axios.isAxiosError(error)) {
                throw new Error(`Failed to fetch OpenRouter models: ${error.message}`);
            }
            throw error;
        }
    }
    /**
     * Get models organized by provider
     */
    async getModelsByProvider() {
        const models = await this.getModels();
        const providerMap = new Map();
        models.forEach(model => {
            // Extract provider from model id (e.g., "openai/gpt-4" -> "openai")
            const provider = model.id.split('/')[0] || 'unknown';
            if (!providerMap.has(provider)) {
                providerMap.set(provider, []);
            }
            providerMap.get(provider).push(model);
        });
        // Sort providers alphabetically and sort models within each provider
        return Array.from(providerMap.entries())
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([provider, models]) => ({
            provider: provider.charAt(0).toUpperCase() + provider.slice(1),
            models: models.sort((a, b) => a.name.localeCompare(b.name))
        }));
    }
    /**
     * Check if a model supports reasoning
     */
    modelSupportsReasoning(model) {
        return model.supported_parameters.includes('reasoning') ||
            model.pricing.internal_reasoning !== undefined;
    }
    /**
     * Get estimated cost for a request
     */
    estimateCost(model, promptTokens, completionTokens, useReasoning = false) {
        const promptCost = parseFloat(model.pricing.prompt) * promptTokens;
        const completionCost = parseFloat(model.pricing.completion) * completionTokens;
        let reasoningCost = 0;
        if (useReasoning && model.pricing.internal_reasoning) {
            // Estimate reasoning tokens (typically 10-20% of completion tokens)
            const estimatedReasoningTokens = Math.ceil(completionTokens * 0.15);
            reasoningCost = parseFloat(model.pricing.internal_reasoning) * estimatedReasoningTokens;
        }
        return promptCost + completionCost + reasoningCost;
    }
    /**
     * Create a chat completion
     */
    async createChatCompletion(request) {
        try {
            const response = await this.client.post('/chat/completions', request);
            return response.data;
        }
        catch (error) {
            if (axios.isAxiosError(error)) {
                const status = error.response?.status;
                const data = error.response?.data;
                if (status === 401) {
                    throw new Error('Invalid OpenRouter API key');
                }
                else if (status === 402) {
                    throw new Error('Insufficient credits on OpenRouter account');
                }
                else if (status === 429) {
                    throw new Error('Rate limit exceeded on OpenRouter');
                }
                else {
                    throw new Error(`OpenRouter API error: ${data?.error?.message || error.message}`);
                }
            }
            throw error;
        }
    }
    /**
     * Test the API key by making a simple request
     */
    async testApiKey() {
        try {
            await this.getModels();
            return true;
        }
        catch {
            return false;
        }
    }
}
