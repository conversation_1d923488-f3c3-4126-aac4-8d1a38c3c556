export interface OpenRouterModel {
  id: string;
  name: string;
  created: number;
  description: string;
  context_length: number;
  architecture: {
    input_modalities: string[];
    output_modalities: string[];
    tokenizer: string;
    instruct_type?: string;
  };
  pricing: {
    prompt: string;
    completion: string;
    image?: string;
    request?: string;
    internal_reasoning?: string;
  };
  top_provider: {
    is_moderated: boolean;
  };
  per_request_limits?: Record<string, unknown>;
  supported_parameters: string[];
}

export interface OpenRouterModelsResponse {
  data: OpenRouterModel[];
}

export interface OpenRouterMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface OpenRouterChatRequest {
  model: string;
  messages: OpenRouterMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string | string[];
  stream?: boolean;
  // OpenRouter-specific parameters
  provider?: {
    order?: string[];
  };
  // Reasoning support (for models that support it)
  reasoning?: {
    max_tokens?: number;
  };
}

export interface OpenRouterChatResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface ModelCategory {
  provider: string;
  models: OpenRouterModel[];
}

export interface AIProviderConfig {
  provider: 'claude' | 'openrouter';
  apiKey: string;
  model?: string;
  useReasoning?: boolean;
  reasoningBudget?: number;
}