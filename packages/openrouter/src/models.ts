import { OpenRouterClient } from './client';
import type { OpenRouterModel, ModelCategory } from './types';

/**
 * Cache for models to avoid repeated API calls
 */
class ModelCache {
  private models: OpenRouterModel[] | null = null;
  private lastFetch: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  async getModels(client: OpenRouterClient): Promise<OpenRouterModel[]> {
    const now = Date.now();
    
    if (this.models && (now - this.lastFetch) < this.CACHE_DURATION) {
      return this.models;
    }

    this.models = await client.getModels();
    this.lastFetch = now;
    return this.models;
  }

  invalidate() {
    this.models = null;
    this.lastFetch = 0;
  }
}

const modelCache = new ModelCache();

/**
 * Get all available models with caching
 */
export async function getCachedModels(client: OpenRouterClient): Promise<OpenRouterModel[]> {
  return modelCache.getModels(client);
}

/**
 * Get models organized by provider with caching
 */
export async function getCachedModelsByProvider(client: OpenRouterClient): Promise<ModelCategory[]> {
  const models = await getCachedModels(client);
  const providerMap = new Map<string, OpenRouterModel[]>();

  models.forEach(model => {
    const provider = model.id.split('/')[0] || 'unknown';
    
    if (!providerMap.has(provider)) {
      providerMap.set(provider, []);
    }
    providerMap.get(provider)!.push(model);
  });

  return Array.from(providerMap.entries())
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([provider, models]) => ({
      provider: provider.charAt(0).toUpperCase() + provider.slice(1),
      models: models.sort((a, b) => a.name.localeCompare(b.name))
    }));
}

/**
 * Find a model by ID
 */
export async function findModel(client: OpenRouterClient, modelId: string): Promise<OpenRouterModel | null> {
  const models = await getCachedModels(client);
  return models.find(model => model.id === modelId) || null;
}

/**
 * Get recommended models for Instagram DM automation
 */
export async function getRecommendedModels(client: OpenRouterClient): Promise<OpenRouterModel[]> {
  const models = await getCachedModels(client);
  
  // Filter for models that are good for conversational AI
  const recommended = models.filter(model => {
    const id = model.id.toLowerCase();
    const name = model.name.toLowerCase();
    
    // Include popular conversational models
    return (
      id.includes('gpt-4') ||
      id.includes('claude') ||
      id.includes('llama') ||
      name.includes('chat') ||
      name.includes('instruct')
    );
  });

  // Sort by context length (higher is better for conversations) and then by name
  return recommended.sort((a, b) => {
    if (a.context_length !== b.context_length) {
      return b.context_length - a.context_length;
    }
    return a.name.localeCompare(b.name);
  });
}

/**
 * Filter models by capabilities
 */
export async function filterModelsByCapabilities(
  client: OpenRouterClient, 
  options: {
    supportsReasoning?: boolean;
    minContextLength?: number;
    maxCostPerToken?: number;
    providers?: string[];
  }
): Promise<OpenRouterModel[]> {
  const models = await getCachedModels(client);
  
  return models.filter(model => {
    if (options.supportsReasoning && !client.modelSupportsReasoning(model)) {
      return false;
    }
    
    if (options.minContextLength && model.context_length < options.minContextLength) {
      return false;
    }
    
    if (options.maxCostPerToken) {
      const promptCost = parseFloat(model.pricing.prompt);
      if (promptCost > options.maxCostPerToken) {
        return false;
      }
    }
    
    if (options.providers && options.providers.length > 0) {
      const provider = model.id.split('/')[0];
      if (!options.providers.includes(provider)) {
        return false;
      }
    }
    
    return true;
  });
}

/**
 * Invalidate the model cache (useful when API key changes)
 */
export function invalidateModelCache() {
  modelCache.invalidate();
}