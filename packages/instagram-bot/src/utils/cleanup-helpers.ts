/**
 * Utility functions for cleaning up data and simplifying operations
 */

import { prisma } from '@workspace/database/client';

/**
 * Clean up old follow-up messages that are no longer relevant
 */
export async function cleanupOldFollowUps(organizationId: string, daysOld: number = 30): Promise<number> {
  const cutoffDate = new Date(Date.now() - (daysOld * 24 * 60 * 60 * 1000));
  
  const result = await prisma.instagramFollowUp.deleteMany({
    where: {
      contact: {
        organizationId
      },
      createdAt: {
        lte: cutoffDate
      },
      OR: [
        { status: 'SENT' },
        { status: 'FAILED' }
      ]
    }
  });
  
  return result.count;
}

/**
 * Remove duplicate contacts that might have been created due to race conditions
 */
export async function removeDuplicateContacts(organizationId: string): Promise<number> {
  // Find contacts with duplicate Instagram IDs
  const duplicates = await prisma.instagramContact.groupBy({
    by: ['instagramId'],
    where: {
      organizationId
    },
    having: {
      instagramId: {
        _count: {
          gt: 1
        }
      }
    }
  });
  
  let removedCount = 0;
  
  for (const duplicate of duplicates) {
    // Keep the newest contact, remove others
    const contacts = await prisma.instagramContact.findMany({
      where: {
        organizationId,
        instagramId: duplicate.instagramId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    // Remove all but the first (newest) contact
    const contactsToRemove = contacts.slice(1);
    for (const contact of contactsToRemove) {
      await prisma.instagramContact.delete({
        where: { id: contact.id }
      });
      removedCount++;
    }
  }
  
  return removedCount;
}

/**
 * Consolidate message history for better performance
 */
export async function consolidateMessageHistory(contactId: string, maxMessages: number = 50): Promise<void> {
  const messageCount = await prisma.instagramMessage.count({
    where: { contactId }
  });
  
  if (messageCount <= maxMessages) {
    return; // No consolidation needed
  }
  
  // Keep the most recent messages
  const messagesToDelete = await prisma.instagramMessage.findMany({
    where: { contactId },
    orderBy: { timestamp: 'desc' },
    skip: maxMessages
  });
  
  await prisma.instagramMessage.deleteMany({
    where: {
      id: {
        in: messagesToDelete.map(msg => msg.id)
      }
    }
  });
}