/**
 * AI Provider Abstraction Interface
 * 
 * This interface defines a common contract for different AI providers
 * (<PERSON>, OpenRouter, etc.) used in the Instagram bot system.
 */

export interface AIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface AIProviderConfig {
  provider: 'claude' | 'openrouter';
  apiKey: string;
  model?: string;
  useReasoning?: boolean;
  reasoningBudget?: number;
  // Cache settings (for Claude)
  enableCache?: boolean;
  cacheType?: '5m' | '1h';
  enableConversationCache?: boolean;
  maxConversationMessages?: number;
}

export interface AIResponse {
  // Primary response data
  message: string;
  messages?: string[];
  stage?: string;
  priority?: number;
  reason?: string;
  
  // Follow-up messages
  followUps?: Array<{
    message: string;
    delayHours: number;
  }>;
  
  // Additional metadata
  linkToSend?: string;
  messageDelaySeconds?: number;
  
  // Debug information
  thinking?: string;
  rawResponse?: unknown;
  
  // Usage statistics
  usage?: {
    inputTokens: number;
    outputTokens: number;
    reasoningTokens?: number;
    cacheHit?: boolean;
    cost?: number;
  };
  
  // Legacy format support
  message1?: string;
  message2?: string;
  message3?: string;
  message4?: string;
  fu1_message?: string;
  fu1_time?: string;
  fu1_status?: string;
  fu2_message?: string;
  fu2_time?: string;
  fu2_status?: string;
  fu3_message?: string;
  fu3_time?: string;
  fu3_status?: string;
  fu4_message?: string;
  fu4_time?: string;
  fu4_status?: string;
}

export interface AIProviderOptions {
  prompt: string;
  conversationHistory: string;
  organizationId: string;
  disableCache?: boolean;
  maxTokens?: number;
  temperature?: number;
}

/**
 * Abstract base class for AI providers
 */
export abstract class BaseAIProvider {
  protected config: AIProviderConfig;
  
  constructor(config: AIProviderConfig) {
    this.config = config;
  }
  
  /**
   * Generate a response using the AI provider
   */
  abstract generateResponse(options: AIProviderOptions): Promise<AIResponse>;
  
  /**
   * Test if the provider configuration is valid
   */
  abstract testConnection(): Promise<boolean>;
  
  /**
   * Get the provider name
   */
  abstract getProviderName(): string;
  
  /**
   * Get the model being used
   */
  getModel(): string | undefined {
    return this.config.model;
  }
  
  /**
   * Check if reasoning is enabled
   */
  isReasoningEnabled(): boolean {
    return this.config.useReasoning || false;
  }
  
  /**
   * Get reasoning token budget
   */
  getReasoningBudget(): number {
    return this.config.reasoningBudget || 4000;
  }
  
  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AIProviderConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

/**
 * Factory for creating AI providers
 */
export class AIProviderFactory {
  static async create(config: AIProviderConfig): Promise<BaseAIProvider> {
    switch (config.provider) {
      case 'claude': {
        const { ClaudeProvider } = await import('./providers/claude-provider');
        return new ClaudeProvider(config);
      }
      case 'openrouter': {
        const { OpenRouterProvider } = await import('./providers/openrouter-provider');
        return new OpenRouterProvider(config);
      }
      default:
        throw new Error(`Unsupported AI provider: ${config.provider}`);
    }
  }
}