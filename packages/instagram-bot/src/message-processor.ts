import { prisma } from '@workspace/database/client';
import { getProfile, sendMessage } from '@workspace/instagram';
import { analyzeImage, transcribeAudio } from '@workspace/openai';
import { buildFullPrompt } from './prompt-builder';
import { ProcessMessageOptions } from './types';
import { formatConversationHistory, generateInstagramResponse, processResponse, scheduleFollowUps, updateContactStage } from './ai-response';
import { withRetry, logError, ErrorContext, InstagramBotError } from './error-handler';
import { logger, createTimer, logTranscription } from '@workspace/common/logger';
import axios from 'axios';

/**
 * Download media from a URL
 */
async function downloadMedia(url: string): Promise<Buffer> {
  try {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    return Buffer.from(response.data);
  } catch (error) {
    console.error('Error downloading media:', error);
    throw new Error('Failed to download media');
  }
}

/**
 * Process media based on its type with enhanced error handling
 */
async function processMediaWithType(mediaUrl: string, mediaType: string): Promise<string> {
  switch (mediaType.toLowerCase()) {
    case 'audio':
    case 'voice':
      return await processAudioMedia(mediaUrl);

    case 'image':
    case 'photo':
      return await processImageMedia(mediaUrl);

    case 'video':
      return await processVideoMedia(mediaUrl);

    default:
      return `[${mediaType.toUpperCase()} Message]: The user sent a ${mediaType} file.`;
  }
}

/**
 * Process audio media with transcription
 */
async function processAudioMedia(mediaUrl: string): Promise<string> {
  const timer = createTimer('Audio Transcription');

  try {
    logTranscription('Starting audio transcription', {
      operation: 'processAudioMedia',
      mediaType: 'audio',
      mediaUrl
    });

    const downloadTimer = createTimer('Audio Download');
    const audioBuffer = await downloadMedia(mediaUrl);
    downloadTimer.end({
      mediaUrl,
      fileSize: audioBuffer.length
    });

    logTranscription('Audio downloaded, starting transcription', {
      operation: 'processAudioMedia',
      mediaType: 'audio',
      mediaUrl,
      fileSize: audioBuffer.length
    });

    const transcription = await transcribeAudio({
      audioBuffer,
      filename: 'voice_message.mp3'
    });

    if (!transcription || transcription.trim().length === 0) {
      logTranscription('Empty transcription result', {
        operation: 'processAudioMedia',
        mediaType: 'audio',
        mediaUrl,
        transcriptionLength: 0
      });
      throw new Error('Empty transcription result');
    }

    logTranscription('Audio transcription completed successfully', {
      operation: 'processAudioMedia',
      mediaType: 'audio',
      mediaUrl,
      transcriptionLength: transcription.length,
      model: 'whisper-1'
    });

    timer.end({
      mediaType: 'audio',
      mediaUrl,
      transcriptionLength: transcription.length,
      success: true
    });

    return `[Voice Message Transcription]: ${transcription}`;
  } catch (error) {
    logTranscription('Audio transcription failed', {
      operation: 'processAudioMedia',
      mediaType: 'audio',
      mediaUrl
    });

    timer.end({
      mediaType: 'audio',
      mediaUrl,
      success: false,
      error: true
    });

    throw new InstagramBotError(
      'Failed to transcribe audio message',
      'AUDIO_TRANSCRIPTION_FAILED',
      { operation: 'transcribe_audio' },
      true // retryable
    );
  }
}

/**
 * Process image media with analysis
 */
async function processImageMedia(mediaUrl: string): Promise<string> {
  const timer = createTimer('Image Analysis');

  try {
    logTranscription('Starting image analysis', {
      operation: 'processImageMedia',
      mediaType: 'image',
      mediaUrl
    });

    const imageDescription = await analyzeImage({
      imageUrl: mediaUrl,
      prompt: 'Describe this image in detail for an Instagram conversation context. Focus on any text, objects, people, or important visual elements that would be relevant for customer service or sales conversations.'
    });

    if (!imageDescription || imageDescription.trim().length === 0) {
      logTranscription('Empty image analysis result', {
        operation: 'processImageMedia',
        mediaType: 'image',
        mediaUrl,
        transcriptionLength: 0
      });
      throw new Error('Empty image analysis result');
    }

    logTranscription('Image analysis completed successfully', {
      operation: 'processImageMedia',
      mediaType: 'image',
      mediaUrl,
      transcriptionLength: imageDescription.length,
      model: 'gpt-4o'
    });

    timer.end({
      mediaType: 'image',
      mediaUrl,
      transcriptionLength: imageDescription.length,
      success: true
    });

    return `[Image Description]: ${imageDescription}`;
  } catch (error) {
    logTranscription('Image analysis failed', {
      operation: 'processImageMedia',
      mediaType: 'image',
      mediaUrl
    });

    timer.end({
      mediaType: 'image',
      mediaUrl,
      success: false,
      error: true
    });

    throw new InstagramBotError(
      'Failed to analyze image',
      'IMAGE_ANALYSIS_FAILED',
      { operation: 'analyze_image' },
      true // retryable
    );
  }
}

/**
 * Process video media (enhanced with potential audio extraction)
 */
async function processVideoMedia(mediaUrl: string): Promise<string> {
  try {
    // For now, we'll provide a basic description
    // In the future, we could implement video frame analysis or audio extraction
    console.log('Video processing - basic description only');

    // TODO: Implement video frame extraction and analysis
    // TODO: Implement audio extraction from video for transcription

    return `[Video Message]: The user sent a video. Video content analysis is not yet available, but the video has been received and saved.`;
  } catch (error) {
    console.error('Error processing video:', error);
    throw new InstagramBotError(
      'Failed to process video',
      'VIDEO_PROCESSING_FAILED',
      { operation: 'process_video' },
      false // not retryable for now
    );
  }
}

/**
 * Get fallback description when media processing fails
 */
function getFallbackMediaDescription(mediaType: string, error: Error): string {
  const errorMessage = error.message.toLowerCase();

  // Provide specific fallback messages based on error type
  if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
    return `[${mediaType.toUpperCase()} Processing Error]: Network error occurred while processing the ${mediaType}. The file was received but could not be analyzed due to connectivity issues.`;
  }

  if (errorMessage.includes('format') || errorMessage.includes('unsupported')) {
    return `[${mediaType.toUpperCase()} Processing Error]: The ${mediaType} format is not supported for analysis. The file was received and saved.`;
  }

  if (errorMessage.includes('size') || errorMessage.includes('large')) {
    return `[${mediaType.toUpperCase()} Processing Error]: The ${mediaType} file is too large for processing. The file was received and saved.`;
  }

  // Generic fallback
  return `[${mediaType.toUpperCase()} Processing Error]: There was an error processing the ${mediaType} file. The file was received and saved, but automatic analysis is not available.`;
}

/**
 * Process a message from Instagram
 */
export async function processMessage({
  organizationId,
  senderId,
  messageId,
  messageText,
  mediaUrl,
  mediaType,
  timestamp
}: ProcessMessageOptions): Promise<void> {
  let contact: any = null; // Declare contact variable at function scope

  try {
    // Get Instagram settings with organization and user info
    const settings = await prisma.instagramSettings.findUnique({
      where: { organizationId },
      include: {
        Organization: {
          include: {
            memberships: {
              where: {
                role: 'ADMIN'
              },
              include: {
                user: true
              },
              take: 1
            }
          }
        }
      }
    });

    if (!settings || !settings.isBotEnabled || !settings.instagramToken) {
      console.log('Bot is disabled or missing Instagram token');
      return;
    }

    // Additional echo prevention: Check if sender matches our connected Instagram account
    if (settings.instagramAccountId && senderId === settings.instagramAccountId) {
      console.log(`Skipping message processing - echo message from connected Instagram account: ${senderId}`);
      return;
    }

    const userId = settings.Organization.memberships[0]?.user.id;
    if (!userId) {
      console.log('No admin user found for organization');
      return;
    }

    // Calculate random response delay
    const minDelay = settings.minResponseTime * 1000; // Convert to milliseconds
    const maxDelay = settings.maxResponseTime * 1000; // Convert to milliseconds
    const responseDelay = Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay;

    console.log(`Will respond after ${responseDelay / 1000} seconds delay`);

    // Check if contact exists, if not create it
    contact = await prisma.instagramContact.findFirst({
      where: {
        organizationId,
        instagramId: senderId
      }
    });

    if (!contact) {
      // Get profile info from Instagram
      const profile = await getProfile(senderId, settings.instagramToken);

      contact = await prisma.instagramContact.create({
        data: {
          userId,
          organizationId,
          instagramId: senderId,
          instagramNickname: profile.username || 'unknown',
          avatar: profile.profile_picture,
          stage: 'initial'
        }
      });
    }

    // Check if user has taken control
    if (contact.isTakeControl) {
      console.log('User has taken control of this conversation');

      // Still save the message
      await saveMessage(contact.id, messageId, messageText, mediaUrl, mediaType, true, undefined, timestamp);

      return;
    }

    // Check if contact is in a terminal stage - don't process further
    const terminalStages = ['converted', 'disqualified', 'blocked', 'suspicious'];
    if (terminalStages.includes(contact.stage)) {
      console.log(`Contact ${contact.id} is in terminal stage ${contact.stage}, skipping AI processing`);

      // Still save the message for record keeping
      await saveMessage(contact.id, messageId, messageText, mediaUrl, mediaType, true, undefined, timestamp);

      return;
    }

    // Process media if present with enhanced error handling and retry logic
    let finalMessageText = messageText;
    let finalMediaUrl = mediaUrl;
    let finalMediaType = mediaType;
    let mediaDescription = '';

    if (mediaUrl && mediaType) {
      console.log(`Processing media of type: ${mediaType}`);

      const errorContext: ErrorContext = {
        operation: 'process_media',
        organizationId,
        messageId,
        contactId: contact.id
      };

      try {
        mediaDescription = await withRetry(
          () => processMediaWithType(mediaUrl, mediaType),
          {
            maxRetries: 2,
            baseDelay: 2000,
            exponentialBackoff: true
          },
          errorContext
        );
        console.log(`Media processing completed for ${mediaType}`);

        // For audio messages, convert to text and remove media
        if (mediaType === 'audio' || mediaType === 'voice') {
          // Extract transcription from the formatted response
          const transcriptionMatch = mediaDescription.match(/\[Voice Message Transcription\]: (.+)/);
          if (transcriptionMatch) {
            const transcription = transcriptionMatch[1];
            finalMessageText = `[AUDIO MESSAGE]: ${transcription}`;
            finalMediaUrl = null;
            finalMediaType = null;
            mediaDescription = '';
            console.log(`Converted audio to text: ${finalMessageText.substring(0, 100)}${finalMessageText.length > 100 ? '...' : ''}`);
          }
        }
      } catch (error) {
        await logError(error as Error, errorContext);

        // For audio errors, still convert to text format
        if (mediaType === 'audio' || mediaType === 'voice') {
          finalMessageText = '[AUDIO MESSAGE]: Unable to transcribe audio';
          finalMediaUrl = null;
          finalMediaType = null;
          mediaDescription = '';
        } else {
          // Provide fallback description based on media type
          mediaDescription = getFallbackMediaDescription(mediaType, error as Error);
        }
        console.error(`Error processing ${mediaType}, using fallback description:`, error);
      }
    }

    // Save the message
    await saveMessage(contact.id, messageId, finalMessageText, finalMediaUrl, finalMediaType, true, mediaDescription, timestamp);

    // Update message count only (lastInteractionAt is handled by webhook processing)
    await prisma.instagramContact.update({
      where: { id: contact.id },
      data: {
        messageCount: { increment: 1 }
      }
    });

    // Get conversation history
    const messages = await prisma.instagramMessage.findMany({
      where: { contactId: contact.id },
      orderBy: { timestamp: 'asc' }
    });

    // Format conversation history in User/Ja format
    const conversationHistory = formatConversationHistory(messages);

    // Build prompt
    const prompt = await buildFullPrompt(organizationId);

    // Add artificial delay before generating response
    await new Promise(resolve => setTimeout(resolve, responseDelay));

    // Log message processing trigger for AI orchestration debugging
    console.log(`[MESSAGE_PROCESSOR] Triggering AI response generation for contact ${contact.id} (${contact.instagramNickname}) - Organization: ${organizationId}`);
    console.log(`[MESSAGE_PROCESSOR] Last user message: "${messageText?.substring(0, 100)}${(messageText?.length || 0) > 100 ? '...' : ''}"`);
    console.log(`[MESSAGE_PROCESSOR] Conversation history length: ${conversationHistory.length} characters`);

    // Generate response
    const response = await generateInstagramResponse({
      prompt,
      conversationHistory,
      organizationId
    });

    console.log('Generated AI response:', JSON.stringify(response, null, 2));

    // Create a function to send messages to Instagram
    const sendInstagramMessage = async (contactId: string, message: string) => {
      try {
        const contact = await prisma.instagramContact.findUnique({
          where: { id: contactId }
        });

        if (!contact) {
          console.error(`Contact not found: ${contactId}`);
          throw new Error('Contact not found');
        }

        if (!settings.instagramToken) {
          console.error('Instagram token is not configured');
          throw new Error('Instagram token is not configured');
        }

        // Log the message we're about to send
        console.log(`Sending message to Instagram user ${contact.instagramNickname} (${contact.instagramId}): ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}`);

        // Validate recipientId
        if (!contact.instagramId) {
          throw new Error('Contact Instagram ID is missing');
        }

        // Send message to Instagram
        await sendMessage({
          recipientId: contact.instagramId,
          message,
          accessToken: settings.instagramToken
        });

        console.log('Message sent successfully');
      } catch (error) {
        console.error('Error sending message to Instagram:', error);
        throw new Error('Failed to send message to Instagram');
      }
    };

    // Process response with the send message function and message delay settings
    await processResponse(
      contact.id,
      response,
      settings.instagramToken,
      sendInstagramMessage,
      {
        messageDelayMin: settings.messageDelayMin || 3,
        messageDelayMax: settings.messageDelayMax || 5
      },
      conversationHistory
    );

    // Schedule follow-ups if present
    await scheduleFollowUps(contact.id, response);

    // Update contact stage
    await updateContactStage(contact.id, response.stage);

  } catch (error) {
    const errorContext: ErrorContext = {
      operation: 'process_instagram_message',
      organizationId,
      messageId,
      contactId: contact?.id
    };

    await logError(error as Error, errorContext);
    console.error('Error processing message:', error);

    // Provide more specific error handling
    if (error instanceof InstagramBotError) {
      throw error; // Re-throw custom errors with context
    }

    throw new InstagramBotError(
      'Failed to process Instagram message',
      'MESSAGE_PROCESSING_FAILED',
      errorContext,
      true // retryable
    );
  }
}

async function saveMessage(
  contactId: string,
  messageId: string,
  content?: string,
  mediaUrl?: string | null,
  mediaType?: string | null,
  isFromUser: boolean = true,
  mediaDescription?: string | null,
  timestamp?: Date
): Promise<void> {
  // Check if message already exists to prevent duplicates
  const existingMessage = await prisma.instagramMessage.findFirst({
    where: {
      contactId,
      messageId
    }
  });

  if (!existingMessage) {
    // Normalize timestamp for consistent handling
    const messageTimestamp = timestamp ? new Date(timestamp) : new Date();

    await prisma.instagramMessage.create({
      data: {
        contactId,
        messageId,
        content: content || '',
        isFromUser,
        mediaUrl: mediaUrl || null,
        mediaType: mediaType || null,
        mediaDescription: mediaDescription || null,
        timestamp: messageTimestamp
      }
    });
    console.log(`Saved message: ${messageId} for contact: ${contactId} with timestamp: ${messageTimestamp.toISOString()}`);
  } else {
    // If message exists, check if we need to update it with transcribed content or media description
    const needsUpdate =
      (mediaDescription && !existingMessage.mediaDescription) ||
      (content && content !== existingMessage.content) ||
      (mediaUrl !== existingMessage.mediaUrl) ||
      (mediaType !== existingMessage.mediaType);

    if (needsUpdate) {
      const updateData: {
        content?: string;
        mediaDescription?: string | null;
        mediaUrl?: string | null;
        mediaType?: string | null;
      } = {};

      if (content && content !== existingMessage.content) {
        updateData.content = content;
      }
      if (mediaDescription && !existingMessage.mediaDescription) {
        updateData.mediaDescription = mediaDescription;
      }
      if (mediaUrl !== existingMessage.mediaUrl) {
        updateData.mediaUrl = mediaUrl;
      }
      if (mediaType !== existingMessage.mediaType) {
        updateData.mediaType = mediaType;
      }

      await prisma.instagramMessage.update({
        where: { id: existingMessage.id },
        data: updateData
      });

      console.log(`Updated message ${messageId} with new content: ${content?.substring(0, 50)}${(content?.length || 0) > 50 ? '...' : ''}`);
    } else {
      console.log(`Message ${messageId} already exists for contact ${contactId}, skipping duplicate`);
    }
  }
}


