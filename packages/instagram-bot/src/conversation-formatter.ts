/**
 * Conversation formatting utilities for Instagram messages
 */

export interface MessageData {
  isFromUser: boolean;
  content?: string;
  mediaDescription: string | null;
  mediaUrl: string | null;
  mediaType: string | null;
}

/**
 * Format conversation history in the User/Ja format
 */
export function formatConversationHistory(messages: MessageData[]): string {
  return messages.map(msg => {
    const role = msg.isFromUser ? 'User' : 'Ja';
    let content = msg.content || '';

    // Add media description if available
    if (msg.mediaDescription) {
      content += `\n[${msg.mediaType || 'media'}: ${msg.mediaDescription}]`;
    } else if (msg.mediaUrl && msg.mediaType) {
      content += `\n[${msg.mediaType}]`;
    }

    return `${role}: ${content}`;
  }).join('\n');
}

/**
 * Clean and normalize message content for AI processing
 */
export function normalizeMessageContent(content: string): string {
  return content
    .replace(/```json\n|```\n|```/g, '') // Remove markdown
    .replace(/„([^"]*?)"/g, '\\"$1\\"')  // Replace „text" with \"text\"
    .replace(/„([^"]*?)"/g, '\\"$1\\"')  // Handle different quote combinations
    .replace(/[\u201C\u201D]/g, '"') // Replace smart quotes with regular quotes
    .replace(/[\u2018\u2019]/g, "'") // Replace smart single quotes
    .replace(/\u2026/g, '...') // Replace ellipsis character
    .trim();
}

/**
 * Extract JSON from AI response text
 */
export function extractJsonFromResponse(textContent: string): string {
  // First try to extract JSON from code blocks
  const jsonBlockMatch = textContent.match(/```json\n([\s\S]*?)\n```/) || textContent.match(/```\n([\s\S]*?)\n```/);
  if (jsonBlockMatch && jsonBlockMatch[1]) {
    return jsonBlockMatch[1];
  }
  
  // If no code blocks, try to find JSON directly
  const jsonMatch = textContent.match(/{[\s\S]*?}/);
  if (jsonMatch) {
    return jsonMatch[0];
  }
  
  // If still no match, use the entire text
  return textContent;
}

/**
 * Validate message to ensure it's not raw JSON
 */
export function validateMessageContent(message: string): string {
  // Check if the message looks like raw JSON
  if (message.trim().startsWith('{') && message.trim().endsWith('}')) {
    console.error('Detected raw JSON being sent as message:', message.substring(0, 100) + '...');
    return 'Sorry, I encountered an error processing your message. Please try again.';
  }
  return message;
}