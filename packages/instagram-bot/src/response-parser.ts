/**
 * Response parsing and validation utilities for AI responses
 */

import { extractJsonFromResponse, normalizeMessageContent, validateMessageContent } from './conversation-formatter';

export interface ParsedResponse {
  message?: string;
  messages?: string[];
  stage?: string;
  priority?: number;
  reason?: string;
  followUps?: Array<{
    message: string;
    delayHours: number;
  }>;
  linkToSend?: string;
  messageDelaySeconds?: number;
}

export interface LegacyResponse {
  message1?: string;
  message2?: string;
  message3?: string;
  message4?: string;
  stage?: string;
  priority?: number;
  reason?: string;
  fu1_message?: string;
  fu1_time?: string;
  fu1_status?: string;
  fu2_message?: string;
  fu2_time?: string;
  fu2_status?: string;
  fu3_message?: string;
  fu3_time?: string;
  fu3_status?: string;
  fu4_message?: string;
  fu4_time?: string;
  fu4_status?: string;
  linkToSend?: string;
}

/**
 * Parse AI response text into structured data
 */
export function parseAIResponse(textContent: string): ParsedResponse | null {
  try {
    const jsonText = extractJsonFromResponse(textContent);
    const normalized = normalizeMessageContent(jsonText);
    const parsed = JSON.parse(normalized);
    
    return validateParsedResponse(parsed);
  } catch (error) {
    console.error('Failed to parse AI response as JSON:', error);
    
    // Try to extract fallback message from text
    const fallbackMessage = extractFallbackMessage(textContent);
    if (fallbackMessage) {
      return {
        message: fallbackMessage,
        stage: 'engaged',
        priority: 1,
        reason: 'Fallback message extracted from unparseable response'
      };
    }
    
    return null;
  }
}

/**
 * Validate and normalize parsed response
 */
function validateParsedResponse(parsed: any): ParsedResponse {
  const response: ParsedResponse = {};
  
  // Handle messages
  if (parsed.message && typeof parsed.message === 'string') {
    response.message = validateMessageContent(parsed.message.trim());
  }
  
  if (parsed.messages && Array.isArray(parsed.messages)) {
    response.messages = parsed.messages
      .filter(msg => typeof msg === 'string')
      .map(msg => validateMessageContent(msg.trim()));
  }
  
  // Handle stage and priority
  if (parsed.stage && typeof parsed.stage === 'string') {
    response.stage = parsed.stage;
  }
  
  if (typeof parsed.priority === 'number') {
    response.priority = Math.max(1, Math.min(5, parsed.priority));
  }
  
  if (parsed.reason && typeof parsed.reason === 'string') {
    response.reason = parsed.reason;
  }
  
  // Handle follow-ups
  if (parsed.followUps && Array.isArray(parsed.followUps)) {
    response.followUps = parsed.followUps
      .filter(fu => fu.message && typeof fu.delayHours === 'number')
      .map(fu => ({
        message: validateMessageContent(fu.message.trim()),
        delayHours: Math.max(1, fu.delayHours)
      }));
  }
  
  // Handle link
  if (parsed.linkToSend && typeof parsed.linkToSend === 'string') {
    response.linkToSend = parsed.linkToSend;
  }
  
  // Handle message delay
  if (typeof parsed.messageDelaySeconds === 'number') {
    response.messageDelaySeconds = Math.max(1, parsed.messageDelaySeconds);
  }
  
  return response;
}

/**
 * Convert legacy response format to modern format
 */
export function convertLegacyResponse(legacy: LegacyResponse): ParsedResponse {
  const response: ParsedResponse = {};
  
  // Collect messages
  const messages: string[] = [];
  for (let i = 1; i <= 4; i++) {
    const message = legacy[`message${i}` as keyof LegacyResponse] as string;
    if (message && message.trim()) {
      messages.push(validateMessageContent(message.trim()));
    }
  }
  
  if (messages.length > 0) {
    if (messages.length === 1) {
      response.message = messages[0];
    } else {
      response.messages = messages;
    }
  }
  
  // Handle metadata
  if (legacy.stage) response.stage = legacy.stage;
  if (legacy.priority) response.priority = legacy.priority;
  if (legacy.reason) response.reason = legacy.reason;
  if (legacy.linkToSend) response.linkToSend = legacy.linkToSend;
  
  // Handle follow-ups
  const followUps: Array<{ message: string; delayHours: number }> = [];
  
  for (let i = 1; i <= 4; i++) {
    const message = legacy[`fu${i}_message` as keyof LegacyResponse] as string;
    const time = legacy[`fu${i}_time` as keyof LegacyResponse] as string;
    const status = legacy[`fu${i}_status` as keyof LegacyResponse] as string;
    
    if (message && message.trim() && time && status === 'active') {
      const delayHours = parseTimeToHours(time);
      if (delayHours > 0) {
        followUps.push({
          message: validateMessageContent(message.trim()),
          delayHours
        });
      }
    }
  }
  
  if (followUps.length > 0) {
    response.followUps = followUps;
  }
  
  return response;
}

/**
 * Parse time string to hours (e.g., "2h" -> 2, "1d" -> 24)
 */
function parseTimeToHours(timeStr: string): number {
  const match = timeStr.match(/^(\d+)([hd])$/);
  if (!match) return 0;
  
  const value = parseInt(match[1]);
  const unit = match[2];
  
  return unit === 'h' ? value : value * 24;
}

/**
 * Extract fallback message from unparseable response
 */
function extractFallbackMessage(text: string): string | null {
  // Try to find quoted strings that look like messages
  const quotedMatch = text.match(/"([^"]+)"/);
  if (quotedMatch && quotedMatch[1].length > 10) {
    return quotedMatch[1];
  }
  
  // Try to find sentences
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
  if (sentences.length > 0) {
    return sentences[0].trim();
  }
  
  return null;
}