import { OpenRouterClient, findModel } from '@workspace/openrouter';
import { logger, createTimer } from '@workspace/common/logger';
import { BaseAIProvider, type AIProviderConfig, type AIProviderOptions, type AIResponse } from '../ai-provider-interface';

/**
 * OpenRouter AI provider implementation
 * Provides access to 200+ models through the OpenRouter API
 */
export class OpenRouterProvider extends BaseAIProvider {
  private client: OpenRouterClient;

  constructor(config: AIProviderConfig) {
    super(config);
    
    if (!config.apiKey) {
      throw new Error('OpenRouter API key is required');
    }

    this.client = new OpenRouterClient(config.apiKey, {
      appUrl: 'https://aisetter.com',
      appName: 'AISetter Instagram Bot'
    });
  }

  getProviderName(): string {
    return 'OpenRouter';
  }

  async testConnection(): Promise<boolean> {
    try {
      return await this.client.testApiKey();
    } catch {
      return false;
    }
  }

  async generateResponse(options: AIProviderOptions): Promise<AIResponse> {
    const timer = createTimer('OpenRouter AI Response Generation');

    if (!this.config.model) {
      throw new Error('OpenRouter model must be specified in configuration');
    }

    try {
      // Build the system prompt
      let systemPrompt: string;
      
      // Check if this is a conversation gathering scenario
      const isConversationGathering = options.prompt.includes('CONVERSATION GATHERING') ||
        options.organizationId.includes('gathering') ||
        options.prompt.includes('user has NOT sent us a new message');

      if (options.organizationId) {
        try {
          if (isConversationGathering) {
            const { buildConversationGatheringPrompt } = await import('../prompt-builder');
            systemPrompt = await buildConversationGatheringPrompt(options.organizationId);
          } else {
            const { buildFullPrompt } = await import('../prompt-builder');
            systemPrompt = await buildFullPrompt(options.organizationId);
          }
        } catch (error) {
          console.warn('Failed to build enhanced prompt, falling back to basic prompt:', error);
          systemPrompt = options.prompt;
        }
      } else {
        systemPrompt = options.prompt;
      }

      // Add conversation context for normal conversations
      if (!isConversationGathering) {
        systemPrompt += `

You are an Instagram DM bot that helps convert leads into appointments.
You should respond as "Ja" in the conversation.

The conversation history is formatted as:
User: [user message]
Ja: [your previous response]
User: [user message]
...

NORMAL CONVERSATION: The user has sent you a new message and expects an immediate response.
- Provide immediate response messages (message1, message2, etc)
- Also set follow-up messages if appropriate

CRITICAL: You must ALWAYS respond with valid JSON in this exact format:
{
  "stage": "new|initial|engaged|qualified|formsent|disqualified|converted",
  "priority": 1-5,
  "message1": "Your response message",
  "message2": "Optional second message",
  "message3": "Optional third message",  
  "message4": "Optional fourth message",
  "fu1_message": "Optional follow-up message",
  "fu1_time": "2025-06-01T12:00:00+01:00",
  "fu1_status": "pending",
  "fu2_message": "Optional second follow-up message",
  "fu2_time": "2025-06-02T12:00:00+01:00",
  "fu2_status": "pending"
}

Never respond with plain text. Always return valid JSON even for old conversations.`;
      }

      const userMessage = `Here is the conversation history:\n\n${options.conversationHistory}\n\nPlease respond to the user's last message.`;

      // Get model information for reasoning support
      const model = await findModel(this.client, this.config.model);
      const supportsReasoning = model ? this.client.modelSupportsReasoning(model) : false;

      // Build the request
      const request = {
        model: this.config.model,
        messages: [
          {
            role: 'system' as const,
            content: systemPrompt
          },
          {
            role: 'user' as const,
            content: userMessage
          }
        ],
        max_tokens: options.maxTokens || 10000,
        temperature: options.temperature || 0.7,
        ...(this.isReasoningEnabled() && supportsReasoning && {
          reasoning: {
            max_tokens: this.getReasoningBudget()
          }
        })
      };

      logger.info('Making OpenRouter request', {
        operation: 'openrouter-request',
        organizationId: options.organizationId,
        model: this.config.model,
        hasReasoning: this.isReasoningEnabled() && supportsReasoning,
        reasoningBudget: this.isReasoningEnabled() ? this.getReasoningBudget() : undefined
      });

      const response = await this.client.createChatCompletion(request);

      // Parse the response
      const aiResponse = await this.parseOpenRouterResponse(response, model);

      logger.info('OpenRouter response completed', {
        operation: 'openrouter-response',
        organizationId: options.organizationId,
        model: this.config.model,
        inputTokens: response.usage?.prompt_tokens || 0,
        outputTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0
      });

      timer.end({
        organizationId: options.organizationId,
        success: true,
        model: this.config.model,
        messageCount: aiResponse.messages?.length || 0,
        stage: aiResponse.stage
      });

      return aiResponse;

    } catch (error) {
      logger.error('Error generating OpenRouter AI response', {
        operation: 'openrouter-generate',
        organizationId: options.organizationId,
        model: this.config.model
      }, error as Error);

      timer.end({
        organizationId: options.organizationId,
        success: false,
        error: true
      });

      throw error;
    }
  }

  private async parseOpenRouterResponse(response: any, model: any): Promise<AIResponse> {
    const content = response.choices?.[0]?.message?.content || '';

    try {
      // Find JSON in the response
      let jsonStr = '';

      const jsonBlockMatch = content.match(/```json\n([\s\S]*?)\n```/) || 
        content.match(/```\n([\s\S]*?)\n```/);
      
      if (jsonBlockMatch && jsonBlockMatch[1]) {
        jsonStr = jsonBlockMatch[1];
      } else {
        const jsonMatch = content.match(/{[\s\S]*?}/);
        if (jsonMatch) {
          jsonStr = jsonMatch[0];
        } else {
          jsonStr = content;
        }
      }

      // Clean up JSON
      jsonStr = jsonStr
        .replace(/```json\n|```\n|```/g, '')
        .replace(/„([^"]*?)"/g, '\\"$1\\"')
        .replace(/[\u201C\u201D]/g, '"')
        .replace(/[\u2018\u2019]/g, "'")
        .replace(/\u2026/g, '...')
        .trim();

      const parsedResponse = JSON.parse(jsonStr);

      // Handle all possible formats
      let messages: string[] = [];

      if (parsedResponse.messages && Array.isArray(parsedResponse.messages)) {
        messages = parsedResponse.messages;
      } else if (parsedResponse.message) {
        messages = [parsedResponse.message];
      } else {
        if (parsedResponse.message1) messages.push(parsedResponse.message1);
        if (parsedResponse.message2) messages.push(parsedResponse.message2);
        if (parsedResponse.message3) messages.push(parsedResponse.message3);
        if (parsedResponse.message4) messages.push(parsedResponse.message4);
      }

      // Handle follow-ups
      const followUps = parsedResponse.followUps || [];
      if (!followUps.length) {
        if (parsedResponse.fu1_message) {
          followUps.push({
            message: parsedResponse.fu1_message,
            delayHours: 24
          });
        }
        if (parsedResponse.fu2_message) {
          followUps.push({
            message: parsedResponse.fu2_message,
            delayHours: 48
          });
        }
        if (parsedResponse.fu3_message) {
          followUps.push({
            message: parsedResponse.fu3_message,
            delayHours: 72
          });
        }
        if (parsedResponse.fu4_message) {
          followUps.push({
            message: parsedResponse.fu4_message,
            delayHours: 96
          });
        }
      }

      return {
        message: messages[0] || '',
        messages: messages,
        stage: parsedResponse.stage || 'initial',
        priority: parsedResponse.priority || 3,
        reason: parsedResponse.reason,
        followUps: followUps,
        linkToSend: parsedResponse.linkToSend,
        messageDelaySeconds: parsedResponse.messageDelaySeconds || 3,
        thinking: undefined, // OpenRouter doesn't support thinking mode yet
        rawResponse: response,
        usage: {
          inputTokens: response.usage?.prompt_tokens || 0,
          outputTokens: response.usage?.completion_tokens || 0,
          reasoningTokens: 0, // OpenRouter doesn't expose reasoning tokens separately yet
          cacheHit: false, // OpenRouter doesn't have the same caching system as Claude
          cost: this.calculateCost(response.usage, model)
        },
        // Legacy format fields
        message1: parsedResponse.message1,
        message2: parsedResponse.message2,
        message3: parsedResponse.message3,
        message4: parsedResponse.message4,
        fu1_message: parsedResponse.fu1_message,
        fu1_time: parsedResponse.fu1_time,
        fu1_status: parsedResponse.fu1_status,
        fu2_message: parsedResponse.fu2_message,
        fu2_time: parsedResponse.fu2_time,
        fu2_status: parsedResponse.fu2_status,
        fu3_message: parsedResponse.fu3_message,
        fu3_time: parsedResponse.fu3_time,
        fu3_status: parsedResponse.fu3_status,
        fu4_message: parsedResponse.fu4_message,
        fu4_time: parsedResponse.fu4_time,
        fu4_status: parsedResponse.fu4_status
      };

    } catch (parseError) {
      console.error('Error parsing OpenRouter response:', parseError);
      console.error('Raw content:', content);

      // Fallback response
      const fallbackMessage = 'Sorry, I encountered an error processing your message. Please try again.';
      
      return {
        message: fallbackMessage,
        messages: [fallbackMessage],
        message1: fallbackMessage,
        stage: 'initial',
        messageDelaySeconds: 3,
        thinking: undefined,
        rawResponse: response,
        usage: {
          inputTokens: response.usage?.prompt_tokens || 0,
          outputTokens: response.usage?.completion_tokens || 0,
          reasoningTokens: 0,
          cacheHit: false,
          cost: this.calculateCost(response.usage, model)
        }
      };
    }
  }

  private calculateCost(usage: any, model: any): number {
    if (!usage || !model) return 0;
    
    const promptCost = parseFloat(model.pricing.prompt) * (usage.prompt_tokens || 0);
    const completionCost = parseFloat(model.pricing.completion) * (usage.completion_tokens || 0);
    
    // Add reasoning cost if applicable
    let reasoningCost = 0;
    if (this.isReasoningEnabled() && model.pricing.internal_reasoning) {
      // Estimate reasoning tokens (we don't have exact numbers from OpenRouter yet)
      const estimatedReasoningTokens = Math.ceil((usage.completion_tokens || 0) * 0.15);
      reasoningCost = parseFloat(model.pricing.internal_reasoning) * estimatedReasoningTokens;
    }
    
    return promptCost + completionCost + reasoningCost;
  }
}