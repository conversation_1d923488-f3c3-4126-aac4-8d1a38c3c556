import axios from 'axios';
import { prisma } from '@workspace/database/client';
import { keys } from '../../keys';
import { logger, createTimer, logClaudeCache } from '@workspace/common/logger';
import {
  compressConversationHistory,
  createCacheOptimizedConversation,
  logCachePerformance
} from '../conversation-compressor';
import { BaseAIProvider, type AIProviderConfig, type AIProviderOptions, type AIResponse } from '../ai-provider-interface';

/**
 * Claude AI provider implementation
 * Wraps the existing Claude functionality to conform to the AI provider interface
 */
export class ClaudeProvider extends BaseAIProvider {
  private claudeApiKey: string;
  private claudeModel: string;

  constructor(config: AIProviderConfig) {
    super(config);
    
    const env = keys();
    this.claudeApiKey = config.apiKey || env.ANTHROPIC_API_KEY || '';
    this.claudeModel = config.model || env.CLAUDE_MODEL || 'claude-sonnet-4-20250514';
    
    if (!this.claude<PERSON><PERSON><PERSON><PERSON>) {
      throw new Error('Claude API key is required');
    }
  }

  getProviderName(): string {
    return '<PERSON> (Anthropic)';
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await axios.post(
        'https://api.anthropic.com/v1/messages',
        {
          model: this.claudeModel,
          max_tokens: 10,
          messages: [{ role: 'user', content: 'Hi' }]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': this.claudeApiKey,
            'anthropic-version': '2023-06-01'
          }
        }
      );
      
      return response.status === 200;
    } catch {
      return false;
    }
  }

  async generateResponse(options: AIProviderOptions): Promise<AIResponse> {
    const timer = createTimer('Claude AI Response Generation');
    
    logClaudeCache('Starting Claude AI response generation', {
      operation: 'generateInstagramResponse',
      organizationId: options.organizationId,
      conversationHistoryLength: options.conversationHistory.length,
      hasAnthropicKey: !!this.claudeApiKey,
      claudeModel: this.claudeModel,
      disableCache: options.disableCache
    });

    try {
      // Get admin settings for caching configuration
      const adminSettings = await prisma.adminSettings.findFirst({
        select: {
          cacheForAllUsers: true,
          cacheType: true,
          enableConversationCache: true,
          maxConversationMessages: true
        }
      });

      const shouldUseCache = !options.disableCache && 
        (this.config.enableCache !== false) &&
        (adminSettings?.cacheForAllUsers || false);
      
      const cacheType = (this.config.cacheType || adminSettings?.cacheType as '5m' | '1h') || '5m';
      const enableConversationCache = this.config.enableConversationCache ?? 
        (adminSettings?.enableConversationCache ?? true);
      const maxConversationMessages = this.config.maxConversationMessages || 
        adminSettings?.maxConversationMessages || 200;

      // Check if this is a conversation gathering scenario
      const isConversationGathering = options.prompt.includes('CONVERSATION GATHERING') ||
        options.organizationId.includes('gathering') ||
        options.prompt.includes('user has NOT sent us a new message');

      // Use the enhanced prompt builder if organizationId is provided
      let systemPrompt: string;
      if (options.organizationId) {
        try {
          if (isConversationGathering) {
            const { buildConversationGatheringPrompt } = await import('../prompt-builder');
            systemPrompt = await buildConversationGatheringPrompt(options.organizationId);
          } else {
            const { buildFullPrompt } = await import('../prompt-builder');
            systemPrompt = await buildFullPrompt(options.organizationId);
          }
        } catch (error) {
          console.warn('Failed to build enhanced prompt, falling back to basic prompt:', error);
          systemPrompt = options.prompt;
        }
      } else {
        systemPrompt = options.prompt;
      }

      // Add conversation context for normal conversations only
      if (!isConversationGathering) {
        systemPrompt += `

You are an Instagram DM bot that helps convert leads into appointments.
You should respond as "Ja" in the conversation.

The conversation history is formatted as:
User: [user message]
Ja: [your previous response]
User: [user message]
...

NORMAL CONVERSATION: The user has sent you a new message and expects an immediate response.
- Provide immediate response messages (message1, message2, etc)
- Also set follow-up messages if appropriate

CRITICAL: You must ALWAYS respond with valid JSON in this exact format:
{
  "stage": "new|initial|engaged|qualified|formsent|disqualified|converted",
  "priority": 1-5,
  "message1": "Your response message",
  "message2": "Optional second message",
  "message3": "Optional third message",
  "message4": "Optional fourth message",
  "fu1_message": "Optional follow-up message",
  "fu1_time": "2025-06-01T12:00:00+01:00",
  "fu1_status": "pending",
  "fu2_message": "Optional second follow-up message",
  "fu2_time": "2025-06-02T12:00:00+01:00",
  "fu2_status": "pending"
}

Never respond with plain text. Always return valid JSON even for old conversations.`;
      }

      let systemMessage: string | any[];
      let userMessage: string;
      let cacheInfo = { systemCached: false, conversationCached: false, estimatedTokens: 0 };

      if (shouldUseCache) {
        const cacheTimer = createTimer('Claude Cache - Conversation Processing');

        // Parse conversation history for compression
        const messages = options.conversationHistory.split('\n').map(line => {
          const isFromUser = line.startsWith('User:');
          const content = line.replace(/^(User|Ja):\s*/, '');
          return {
            isFromUser,
            content,
            mediaDescription: null,
            mediaUrl: null,
            mediaType: null
          };
        }).filter(msg => msg.content.trim().length > 0);

        // Compress conversation history
        const compressed = compressConversationHistory(messages, maxConversationMessages);

        // Create cache-optimized conversation
        const optimized = createCacheOptimizedConversation(
          systemPrompt,
          compressed.compressedHistory,
          cacheType,
          enableConversationCache
        );

        systemMessage = optimized.systemMessage;
        userMessage = optimized.userMessage;
        cacheInfo = optimized.cacheInfo;

        cacheTimer.end({
          organizationId: options.organizationId,
          compressionRatio: compressed.compressionRatio,
          systemCached: cacheInfo.systemCached,
          conversationCached: cacheInfo.conversationCached
        });
      } else {
        // No caching - use simple format
        systemMessage = systemPrompt;
        userMessage = `Here is the conversation history:\n\n${options.conversationHistory}\n\nPlease respond to the user's last message.`;
      }

      const requestBody = {
        model: this.claudeModel,
        max_tokens: options.maxTokens || 10000,
        system: systemMessage,
        messages: [
          {
            role: 'user',
            content: userMessage
          }
        ],
        ...(options.temperature && { temperature: options.temperature }),
        ...(this.isReasoningEnabled() && {
          thinking: {
            type: "enabled",
            budget_tokens: this.getReasoningBudget()
          }
        })
      };

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'x-api-key': this.claudeApiKey,
        'anthropic-version': '2023-06-01'
      };

      // Add beta headers for prompt caching if needed
      if (shouldUseCache) {
        headers['anthropic-beta'] = 'prompt-caching-2024-07-31';
        if (cacheType === '1h') {
          headers['anthropic-beta'] += ',extended-cache-ttl-2025-04-11';
        }
      }

      const apiTimer = createTimer('Anthropic API Request');
      const response = await axios.post(
        'https://api.anthropic.com/v1/messages',
        requestBody,
        { headers }
      );

      // Check for Claude 4 refusal stop reason
      if (response.data.stop_reason === 'refusal') {
        throw new Error('Claude refused to generate content for safety reasons. Please modify your prompt.');
      }

      // Log cache performance if available
      if (response.data.usage && shouldUseCache) {
        logCachePerformance(response.data.usage, cacheInfo);
      }

      apiTimer.end({
        organizationId: options.organizationId,
        model: requestBody.model,
        inputTokens: response.data.usage?.input_tokens || 0,
        outputTokens: response.data.usage?.output_tokens || 0,
        cacheHit: shouldUseCache && (response.data.usage?.cache_read_input_tokens || 0) > 0
      });

      // Extract the text content and thinking from the response
      const textContent = response.data.content.find(
        (block: { type: string; text?: string }) => block.type === 'text'
      )?.text || '';

      const thinkingContent = response.data.content.find(
        (block: { type: string; thinking?: string }) => block.type === 'thinking'
      )?.thinking || null;

      // Parse the JSON response
      const aiResponse = await this.parseClaudeResponse(
        textContent, 
        thinkingContent, 
        response.data,
        shouldUseCache && (response.data.usage?.cache_read_input_tokens || 0) > 0
      );

      timer.end({
        organizationId: options.organizationId,
        success: true,
        messageCount: aiResponse.messages?.length || 0,
        stage: aiResponse.stage
      });

      return aiResponse;

    } catch (error) {
      logger.error('Error generating Claude AI response', {
        operation: 'generateInstagramResponse',
        organizationId: options.organizationId
      }, error as Error);

      timer.end({
        organizationId: options.organizationId,
        success: false,
        error: true
      });

      throw error;
    }
  }

  private async parseClaudeResponse(
    textContent: string, 
    thinkingContent: string | null, 
    rawResponse: any,
    cacheHit: boolean = false
  ): Promise<AIResponse> {
    try {
      // Find JSON in the response
      let jsonStr = '';

      const jsonBlockMatch = textContent.match(/```json\n([\s\S]*?)\n```/) || 
        textContent.match(/```\n([\s\S]*?)\n```/);
      
      if (jsonBlockMatch && jsonBlockMatch[1]) {
        jsonStr = jsonBlockMatch[1];
      } else {
        const jsonMatch = textContent.match(/{[\s\S]*?}/);
        if (jsonMatch) {
          jsonStr = jsonMatch[0];
        } else {
          jsonStr = textContent;
        }
      }

      // Clean up JSON
      jsonStr = jsonStr
        .replace(/```json\n|```\n|```/g, '')
        .replace(/„([^"]*?)"/g, '\\"$1\\"')
        .replace(/[\u201C\u201D]/g, '"')
        .replace(/[\u2018\u2019]/g, "'")
        .replace(/\u2026/g, '...')
        .trim();

      const parsedResponse = JSON.parse(jsonStr);

      // Handle all possible formats
      let messages: string[] = [];

      if (parsedResponse.messages && Array.isArray(parsedResponse.messages)) {
        messages = parsedResponse.messages;
      } else if (parsedResponse.message) {
        messages = [parsedResponse.message];
      } else {
        if (parsedResponse.message1) messages.push(parsedResponse.message1);
        if (parsedResponse.message2) messages.push(parsedResponse.message2);
        if (parsedResponse.message3) messages.push(parsedResponse.message3);
        if (parsedResponse.message4) messages.push(parsedResponse.message4);
      }

      // Handle follow-ups
      const followUps = parsedResponse.followUps || [];
      if (!followUps.length) {
        if (parsedResponse.fu1_message) {
          followUps.push({
            message: parsedResponse.fu1_message,
            delayHours: 24
          });
        }
        if (parsedResponse.fu2_message) {
          followUps.push({
            message: parsedResponse.fu2_message,
            delayHours: 48
          });
        }
        if (parsedResponse.fu3_message) {
          followUps.push({
            message: parsedResponse.fu3_message,
            delayHours: 72
          });
        }
        if (parsedResponse.fu4_message) {
          followUps.push({
            message: parsedResponse.fu4_message,
            delayHours: 96
          });
        }
      }

      return {
        message: messages[0] || '',
        messages: messages,
        stage: parsedResponse.stage || 'initial',
        priority: parsedResponse.priority || 3,
        reason: parsedResponse.reason,
        followUps: followUps,
        linkToSend: parsedResponse.linkToSend,
        messageDelaySeconds: parsedResponse.messageDelaySeconds || 3,
        thinking: thinkingContent || undefined,
        rawResponse: rawResponse,
        usage: {
          inputTokens: rawResponse.usage?.input_tokens || 0,
          outputTokens: rawResponse.usage?.output_tokens || 0,
          reasoningTokens: rawResponse.usage?.reasoning_tokens || 0,
          cacheHit,
          cost: this.calculateCost(rawResponse.usage)
        },
        // Legacy format fields
        message1: parsedResponse.message1,
        message2: parsedResponse.message2,
        message3: parsedResponse.message3,
        message4: parsedResponse.message4,
        fu1_message: parsedResponse.fu1_message,
        fu1_time: parsedResponse.fu1_time,
        fu1_status: parsedResponse.fu1_status,
        fu2_message: parsedResponse.fu2_message,
        fu2_time: parsedResponse.fu2_time,
        fu2_status: parsedResponse.fu2_status,
        fu3_message: parsedResponse.fu3_message,
        fu3_time: parsedResponse.fu3_time,
        fu3_status: parsedResponse.fu3_status,
        fu4_message: parsedResponse.fu4_message,
        fu4_time: parsedResponse.fu4_time,
        fu4_status: parsedResponse.fu4_status
      };

    } catch (parseError) {
      // Fallback response
      const fallbackMessage = 'Sorry, I encountered an error processing your message. Please try again.';
      
      return {
        message: fallbackMessage,
        messages: [fallbackMessage],
        message1: fallbackMessage,
        stage: 'initial',
        messageDelaySeconds: 3,
        thinking: thinkingContent || undefined,
        rawResponse: rawResponse,
        usage: {
          inputTokens: rawResponse.usage?.input_tokens || 0,
          outputTokens: rawResponse.usage?.output_tokens || 0,
          cacheHit,
          cost: this.calculateCost(rawResponse.usage)
        }
      };
    }
  }

  private calculateCost(usage: any): number {
    if (!usage) return 0;
    
    // Claude Sonnet 4 pricing (approximate)
    const inputCostPerToken = 0.000008; // $8 per 1M tokens
    const outputCostPerToken = 0.000024; // $24 per 1M tokens
    
    const inputCost = (usage.input_tokens || 0) * inputCostPerToken;
    const outputCost = (usage.output_tokens || 0) * outputCostPerToken;
    
    return inputCost + outputCost;
  }
}