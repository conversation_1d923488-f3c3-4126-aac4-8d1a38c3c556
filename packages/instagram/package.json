{"name": "@workspace/instagram", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@t3-oss/env-nextjs": "0.12.0", "@workspace/common": "workspace:*", "axios": "^1.6.7", "zod": "3.24.2"}, "devDependencies": {"@types/node": "22.13.9", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "prettier": "@workspace/prettier-config", "exports": {".": "./src/index.ts", "./keys": "./keys.ts", "./webhook": "./src/webhook.ts", "./client": "./src/client.ts"}}