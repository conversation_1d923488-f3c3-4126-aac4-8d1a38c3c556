import axios from 'axios';
import { SendMediaOptions, SendMessageOptions, InstagramProfile } from './types';
import { logger, createTimer } from '@workspace/common/logger';

const GRAPH_API_URL = 'https://graph.instagram.com/v22.0';

export async function sendMessage({ recipientId, message, accessToken }: SendMessageOptions): Promise<any> {
  const maxRetries = 3;
  let retryCount = 0;
  let lastError: any = null;

  while (retryCount < maxRetries) {
    try {
      console.log(`Sending message to Instagram (attempt ${retryCount + 1}/${maxRetries}): ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}`);

      // Log the request details for debugging
      console.log('Request URL:', `${GRAPH_API_URL}/me/messages`);
      console.log('Request payload:', {
        recipient: { id: recipientId },
        message: { text: message }
      });

      const response = await axios.post(
        `${GRAPH_API_URL}/me/messages`,
        {
          recipient: { id: recipientId },
          message: { text: message }
        },
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Instagram API response:', response.data);
      return response.data;
    } catch (error) {
      lastError = error;
      console.error(`Error sending message to Instagram (attempt ${retryCount + 1}/${maxRetries}):`, error);

      if (axios.isAxiosError(error) && error.response) {
        console.error('API Error Response:', error.response.data);

        // Don't retry for certain error types
        if (error.response.status === 400) {
          console.log('Bad request error, checking details...');
          const errorData = error.response.data;

          // If it's a permission or authentication error, don't retry
          if (errorData?.error?.type === 'OAuthException' ||
            errorData?.error?.code === 190 ||
            errorData?.error?.code === 10) {
            console.log('Authentication or permission error, not retrying');
            break;
          }
        }
      }

      retryCount++;
      if (retryCount < maxRetries) {
        const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // If we've exhausted all retries, throw the last error
  console.error('All retry attempts failed');
  throw new Error('Failed to send message to Instagram after multiple attempts');
}

export async function sendMedia({ recipientId, mediaUrl, mediaType, accessToken }: SendMediaOptions): Promise<any> {
  try {
    console.log(`Sending ${mediaType} to Instagram: ${mediaUrl}`);

    const response = await axios.post(
      `${GRAPH_API_URL}/me/messages`,
      {
        recipient: { id: recipientId },
        message: {
          attachment: {
            type: mediaType,
            payload: {
              url: mediaUrl,
              is_reusable: true
            }
          }
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('Instagram API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error sending media to Instagram:', error);
    if (axios.isAxiosError(error) && error.response) {
      console.error('API Error Response:', error.response.data);
    }
    throw new Error('Failed to send media to Instagram');
  }
}

export async function getBusinessAccountInfo(accessToken: string): Promise<any> {
  try {
    console.log('Fetching Instagram business account information...');

    const response = await axios.get(`${GRAPH_API_URL}/me`, {
      params: {
        fields: 'id,username,name,profile_picture_url,followers_count,media_count,account_type,biography'
      },
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Instagram business account info response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching Instagram business account info:', error);
    if (axios.isAxiosError(error) && error.response) {
      console.error('API Error Response:', error.response.data);
    }
    throw new Error('Failed to fetch Instagram business account info');
  }
}

export async function getProfile(userId: string, accessToken: string): Promise<InstagramProfile> {
  try {
    console.log(`Fetching Instagram profile for user: ${userId}`);

    const response = await axios.get(`${GRAPH_API_URL}/${userId}`, {
      params: {
        fields: 'id,username,name,profile_picture'
      },
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('Instagram API profile response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching Instagram profile:', error);
    if (axios.isAxiosError(error) && error.response) {
      console.error('API Error Response:', error.response.data);
    }
    throw new Error('Failed to fetch Instagram profile');
  }
}

export async function getConversationHistory(userId: string, accessToken: string): Promise<any> {
  const timer = createTimer('Instagram API - Get Conversation History');

  try {
    logger.instagramConversation('Starting conversation history fetch', {
      operation: 'getConversationHistory',
      instagramId: userId,
      apiCall: true
    });

    const response = await axios.get(`${GRAPH_API_URL}/me/conversations`, {
      params: {
        user_id: userId,
        fields: 'participants,messages{id,message,created_time,from,to,attachments}'
      },
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    const messageCount = response.data?.data?.[0]?.messages?.data?.length || 0;
    const conversationCount = response.data?.data?.length || 0;

    logger.instagramConversation('Successfully fetched conversation history', {
      operation: 'getConversationHistory',
      instagramId: userId,
      messageCount,
      conversationCount,
      apiCall: true,
      responseSize: JSON.stringify(response.data).length
    });

    timer.end({
      instagramId: userId,
      messageCount,
      conversationCount
    });

    return response.data;
  } catch (error) {
    logger.error('Failed to fetch conversation history', {
      operation: 'getConversationHistory',
      instagramId: userId,
      apiCall: true
    }, error as Error);

    if (axios.isAxiosError(error) && error.response) {
      logger.error('Instagram API error response', {
        operation: 'getConversationHistory',
        instagramId: userId,
        statusCode: error.response.status,
        errorData: error.response.data
      });
    }

    timer.end({
      instagramId: userId,
      error: true
    });

    throw new Error('Failed to fetch conversation history');
  }
}
