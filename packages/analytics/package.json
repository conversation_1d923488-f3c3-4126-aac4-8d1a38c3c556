{"name": "@workspace/analytics", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@t3-oss/env-nextjs": "0.12.0", "next": "15.2.1", "posthog-js": "1.194.2", "posthog-node": "4.3.0", "react": "19.0.0", "react-dom": "19.0.0", "zod": "3.24.2"}, "devDependencies": {"@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "prettier": "@workspace/prettier-config", "exports": {"./keys": "./keys.ts", "./hooks/use-analytics": "./src/hooks/use-analytics.tsx", "./provider": "./src/provider/index.ts"}}