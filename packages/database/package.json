{"name": "@workspace/database", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "typecheck": "tsc --noEmit", "generate": "prisma generate", "migrate": "prisma migrate", "push": "prisma db push --skip-generate", "postinstall": "prisma generate"}, "dependencies": {"@prisma/client": "6.4.1", "@t3-oss/env-nextjs": "0.12.0", "zod": "3.24.2"}, "devDependencies": {"@types/node": "22.13.9", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "prisma": "6.4.1"}, "prettier": "@workspace/prettier-config", "exports": {".": "./src/index.ts", "./keys": "./keys.ts", "./client": "./src/client.ts"}}