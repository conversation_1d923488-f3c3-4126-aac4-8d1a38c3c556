-- CreateTable
CREATE TABLE "ChromeExtensionSettings" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "timeBetweenDMs" INTEGER NOT NULL DEFAULT 5,
    "messagesBeforeBreak" INTEGER NOT NULL DEFAULT 10,
    "breakDuration" INTEGER NOT NULL DEFAULT 15,
    "smartFocus" BOOLEAN NOT NULL DEFAULT true,
    "isConnected" BOOLEAN NOT NULL DEFAULT false,
    "lastConnectionAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ChromeExtensionSettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ChromeExtensionSettings_organizationId_key" ON "ChromeExtensionSettings"("organizationId");

-- CreateIndex
CREATE INDEX "ChromeExtensionSettings_organizationId_idx" ON "ChromeExtensionSettings"("organizationId");

-- AddForeignKey
ALTER TABLE "ChromeExtensionSettings" ADD CONSTRAINT "ChromeExtensionSettings_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
