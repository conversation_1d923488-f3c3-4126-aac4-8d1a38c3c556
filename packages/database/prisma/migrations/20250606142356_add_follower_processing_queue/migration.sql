-- CreateEnum
CREATE TYPE "FollowerProcessingStatus" AS ENUM ('pending', 'processing', 'completed', 'failed', 'retrying');

-- Create<PERSON>num
CREATE TYPE "FollowerProcessingType" AS ENUM ('batch', 'conversation');

-- CreateTable
CREATE TABLE "FollowerProcessingQueue" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "followerId" UUID NOT NULL,
    "priority" INTEGER NOT NULL DEFAULT 1,
    "scheduledAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "attempts" INTEGER NOT NULL DEFAULT 0,
    "maxAttempts" INTEGER NOT NULL DEFAULT 3,
    "status" "FollowerProcessingStatus" NOT NULL DEFAULT 'pending',
    "errorMessage" TEXT,
    "hasConversation" BOOLEAN NOT NULL DEFAULT false,
    "processingType" "FollowerProcessingType" NOT NULL DEFAULT 'batch',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FollowerProcessingQueue_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "FollowerProcessingQueue_status_scheduledAt_idx" ON "FollowerProcessingQueue"("status", "scheduledAt");

-- CreateIndex
CREATE INDEX "FollowerProcessingQueue_organizationId_idx" ON "FollowerProcessingQueue"("organizationId");

-- CreateIndex
CREATE INDEX "FollowerProcessingQueue_followerId_idx" ON "FollowerProcessingQueue"("followerId");

-- AddForeignKey
ALTER TABLE "FollowerProcessingQueue" ADD CONSTRAINT "FollowerProcessingQueue_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FollowerProcessingQueue" ADD CONSTRAINT "FollowerProcessingQueue_followerId_fkey" FOREIGN KEY ("followerId") REFERENCES "InstagramFollower"("id") ON DELETE CASCADE ON UPDATE CASCADE;
