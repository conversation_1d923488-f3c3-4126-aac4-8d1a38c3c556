-- CreateEnum
CREATE TYPE "ConversationSource" AS ENUM ('extension', 'api');

-- AlterTable
ALTER TABLE "InstagramContact" ADD COLUMN     "conversationSource" "ConversationSource" NOT NULL DEFAULT 'extension',
ADD COLUMN     "priority" INTEGER NOT NULL DEFAULT 3;

-- CreateTable
CREATE TABLE "MessageBatch" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MessageBatch_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MessageBatchItem" (
    "id" UUID NOT NULL,
    "messageBatchId" UUID NOT NULL,
    "messageText" TEXT NOT NULL,
    "sequenceNumber" INTEGER NOT NULL,
    "delayMinutes" INTEGER NOT NULL DEFAULT 5,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MessageBatchItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FollowUpTemplate" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "userId" UUID NOT NULL,
    "messageText" TEXT NOT NULL,
    "sequenceNumber" INTEGER NOT NULL,
    "delayHours" INTEGER NOT NULL DEFAULT 24,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FollowUpTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "MessageBatch_organizationId_idx" ON "MessageBatch"("organizationId");

-- CreateIndex
CREATE INDEX "MessageBatch_userId_idx" ON "MessageBatch"("userId");

-- CreateIndex
CREATE INDEX "MessageBatchItem_messageBatchId_idx" ON "MessageBatchItem"("messageBatchId");

-- CreateIndex
CREATE INDEX "FollowUpTemplate_organizationId_idx" ON "FollowUpTemplate"("organizationId");

-- CreateIndex
CREATE INDEX "FollowUpTemplate_userId_idx" ON "FollowUpTemplate"("userId");

-- CreateIndex
CREATE INDEX "FollowUpTemplate_sequenceNumber_idx" ON "FollowUpTemplate"("sequenceNumber");

-- AddForeignKey
ALTER TABLE "MessageBatch" ADD CONSTRAINT "MessageBatch_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MessageBatch" ADD CONSTRAINT "MessageBatch_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MessageBatchItem" ADD CONSTRAINT "MessageBatchItem_messageBatchId_fkey" FOREIGN KEY ("messageBatchId") REFERENCES "MessageBatch"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FollowUpTemplate" ADD CONSTRAINT "FollowUpTemplate_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FollowUpTemplate" ADD CONSTRAINT "FollowUpTemplate_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
