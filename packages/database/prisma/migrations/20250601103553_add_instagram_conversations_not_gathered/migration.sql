-- CreateTable
CREATE TABLE "InstagramConversationsNotGathered" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "instagramConversationId" TEXT NOT NULL,
    "participantUsername" TEXT NOT NULL,
    "participantId" TEXT NOT NULL,
    "updatedTime" TIMESTAMP(3) NOT NULL,
    "isGathered" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InstagramConversationsNotGathered_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "InstagramConversationsNotGathered_instagramConversationId_key" ON "InstagramConversationsNotGathered"("instagramConversationId");

-- CreateIndex
CREATE INDEX "InstagramConversationsNotGathered_organizationId_idx" ON "InstagramConversationsNotGathered"("organizationId");

-- CreateIndex
CREATE INDEX "InstagramConversationsNotGathered_participantUsername_idx" ON "InstagramConversationsNotGathered"("participantUsername");

-- CreateIndex
CREATE INDEX "InstagramConversationsNotGathered_isGathered_idx" ON "InstagramConversationsNotGathered"("isGathered");

-- CreateIndex
CREATE INDEX "InstagramConversationsNotGathered_updatedTime_idx" ON "InstagramConversationsNotGathered"("updatedTime");

-- AddForeignKey
ALTER TABLE "InstagramConversationsNotGathered" ADD CONSTRAINT "InstagramConversationsNotGathered_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
