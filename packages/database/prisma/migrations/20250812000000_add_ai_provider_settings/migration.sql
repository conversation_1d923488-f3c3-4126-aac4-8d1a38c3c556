-- Add AI provider selection fields to AdminSettings
ALTER TABLE "AdminSettings" 
ADD COLUMN "aiProvider" TEXT NOT NULL DEFAULT 'claude',
ADD COLUMN "openrouterApiKey" TEXT,
ADD COLUMN "openrouterModel" TEXT,
ADD COLUMN "useReasoning" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN "reasoningBudget" INTEGER NOT NULL DEFAULT 4000;

-- Create index for faster queries on aiProvider
CREATE INDEX "IX_AdminSettings_aiProvider" ON "AdminSettings"("aiProvider");