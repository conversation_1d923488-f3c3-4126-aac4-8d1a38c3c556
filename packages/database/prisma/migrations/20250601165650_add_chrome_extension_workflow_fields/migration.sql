-- AlterTable
ALTER TABLE "InstagramContact" ADD COLUMN     "batchId" TEXT,
ADD COLUMN     "batchMessageStatus" TEXT DEFAULT 'pending',
ADD COLUMN     "currentMessageSequence" INTEGER DEFAULT 1,
ADD COLUMN     "lastMessageSentAt" TIMESTAMP(3);

-- CreateTable
CREATE TABLE "InstagramFollowerProcessingState" (
    "id" UUID NOT NULL,
    "organizationId" UUID NOT NULL,
    "lastProcessedPosition" INTEGER NOT NULL DEFAULT 0,
    "totalFollowersCount" INTEGER,
    "isCompleted" BOOLEAN NOT NULL DEFAULT false,
    "lastProcessedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InstagramFollowerProcessingState_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "InstagramFollowerProcessingState_organizationId_key" ON "InstagramFollowerProcessingState"("organizationId");

-- CreateIndex
CREATE INDEX "InstagramFollowerProcessingState_organizationId_idx" ON "InstagramFollowerProcessingState"("organizationId");

-- AddForeignKey
ALTER TABLE "InstagramFollowerProcessingState" ADD CONSTRAINT "InstagramFollowerProcessingState_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;
