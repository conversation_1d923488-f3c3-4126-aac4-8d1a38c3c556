{"name": "@workspace/common", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "devDependencies": {"@types/node": "22.13.9", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "prettier": "@workspace/prettier-config", "exports": {"./app": "./src/app.ts", "./errors": "./src/errors.ts", "./http": "./src/http.ts", "./logger": "./src/logger.ts", "./maybe": "./src/maybe.ts", "./type-guards": "./src/type-guards.ts"}}