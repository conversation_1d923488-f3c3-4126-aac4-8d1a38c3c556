{"name": "@workspace/markdown", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"markdown-it": "14.1.0", "sanitize-html": "2.14.0", "turndown": "7.2.0"}, "devDependencies": {"@types/markdown-it": "14.1.2", "@types/sanitize-html": "2.13.0", "@types/turndown": "5.0.5", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "prettier": "@workspace/prettier-config", "exports": {"./convert-html-to-markdown": "./src/convert-html-to-markdown.ts", "./convert-markdown-to-html": "./src/convert-markdown-to-html.ts"}}