{"name": "@workspace/monitoring", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@sentry/nextjs": "9.5.0", "@t3-oss/env-nextjs": "0.12.0", "import-in-the-middle": "1.13.1", "react": "19.0.0", "react-dom": "19.0.0", "zod": "3.24.2"}, "devDependencies": {"@types/node": "22.13.9", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "prettier": "@workspace/prettier-config", "exports": {"./keys": "./keys.ts", "./hooks/use-capture-error": "./src/hooks/use-capture-error.tsx", "./hooks/use-monitoring": "./src/hooks/use-monitoring.tsx", "./provider": "./src/provider/index.ts"}}